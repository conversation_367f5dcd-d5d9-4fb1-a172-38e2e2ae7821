package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("\"order\"")
data class Order(
    @Id
    val orderId: UUID? = null,
    val invoiceId: UUID,
    val orderStatus: String,
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
) 