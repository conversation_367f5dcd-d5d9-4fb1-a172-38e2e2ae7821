package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("replenishment_item")
data class ReplenishmentItem(
    @Id
    val id: UUID? = null,
    val orderCardId: UUID,
    val itemType: String,
    val description: String?,
    val replenishmentAmount: Long,
    val createdAt: Timestamp? = null
) 