package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table

import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("order_card")
data class OrderCard(
    @Id
    val id: UUID? = null,
    val orderId: UUID,
    val cardType: String,
    val pan: String?,
    val panHash: String?,
    val paymentSystem: String?,
    val cardId: UUID?,
    val createdAt: Timestamp? = null
) 