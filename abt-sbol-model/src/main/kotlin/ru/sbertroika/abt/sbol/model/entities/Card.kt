package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table

import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("card")
data class Card(
    @Id
    val id: UUID? = null,
    val cardId: UUID,
    val pan: String?,
    val panHash: String?,
    val paymentSystem: String?,
    val accountReference: String?,
    val balanceAmount: Long,
    val regionId: String,
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null,
    val type: String
) 