package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Table("service")
data class Service(
    @Id
    val id: UUID? = null,
    val projectId: UUID,
    val templateId: UUID,
    val serviceCode: String,
    val name: String,
    val description: String?,
    val isSocial: Boolean,
    val subscriptionType: String,
    val cost: Long?,
    val actionStartDate: LocalDate?,
    val actionEndDate: LocalDate?,
    val minReplenishmentAmount: Long?,
    val maxReplenishmentAmount: Long?,
    val recommendedAmount: Long?,
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
)