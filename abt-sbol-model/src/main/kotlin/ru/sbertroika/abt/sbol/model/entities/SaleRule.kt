package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table

import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("sale_rule")
data class SaleRule(
    @Id
    val id: UUID? = null,
    val serviceId: UUID,
    val ruleType: String,
    val ruleLogic: String,
    val startDay: Int?,
    val endDay: Int?,
    val startMonth: Int?,
    val endMonth: Int?,
    val startWeek: Int?,
    val endWeek: Int?,
    val minCardBalance: Long?,
    val maxCardBalance: Long?,
    val isActive: Boolean = true,
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
) 