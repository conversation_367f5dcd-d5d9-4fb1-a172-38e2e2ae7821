package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("service_agent")
data class ServiceAgent(
    @Id
    val id: UUID? = null,
    val serviceId: UUID,
    val agentId: UUID,
    val agentVersion: Int,
    val createdAt: Timestamp? = null
) 