package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("region_project")
data class RegionProject(
    @Id
    val id: UUID? = null,
    val regionId: String,
    val projectId: UUID,
    val canDecodeHash: Boolean,
    val panDecodeKey: String?,
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
) 