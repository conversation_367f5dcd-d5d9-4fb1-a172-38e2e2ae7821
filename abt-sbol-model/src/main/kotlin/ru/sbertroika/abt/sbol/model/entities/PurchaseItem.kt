package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table

import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("purchase_item")
data class PurchaseItem(
    @Id
    val id: UUID? = null,
    val orderCardId: UUID,
    val serviceId: UUID,
    val serviceIdString: String,
    val replenishmentAmount: Long?,
    val usedCounterAmount: Long?,
    val cost: Long,
    val descriptionText: String?,
    val intervalAmount: Int?,
    val intervalLength: String?,
    val actionStartDate: LocalDateTime?,
    val actionEndDate: LocalDateTime?,
    val createdAt: Timestamp? = null
) 