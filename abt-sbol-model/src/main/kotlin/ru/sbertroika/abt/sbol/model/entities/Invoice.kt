package ru.sbertroika.abt.sbol.model.entities

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Table

import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Table("invoice")
data class Invoice(
    @Id
    val invoiceId: UUID? = null,
    val agentTransactionId: String,
    val invoiceStatus: String,
    val invoiceAmount: Long,
    val regionId: String,
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
) 