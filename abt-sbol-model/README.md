# ABT SBOL Model

Модуль с моделями данных для работы с базой данных Agent Gate SBOL.

## Описание

Этот модуль содержит:
- Enum'ы для типов данных
- Entity классы для работы с базой данных PostgreSQL
- Настроен для работы с Spring Data R2DBC

## Структура

### Enums
- `SubscriptionType` - типы подписки (WALLET, TRAVEL, UNLIMITED)
- `SaleRuleType` - типы правил продаж (MONTHLY_RANGE, WEEKLY_RANGE, DAILY_RANGE, BALANCE_RANGE)
- `SaleRuleLogic` - логика применения правил (AND, OR)
- `InvoiceStatus` - статусы счетов (CREATED, PAID, CANCELED, OUTDATED)
- `CardType` - ти<PERSON><PERSON> карт (TRANSPORT, IPS)
- `PaymentSystem` - платежные системы (VISA, MASTERCARD, MIR)
- `IntervalLength` - длительность интервалов (M, D)

### Entities
- `RegionProject` - связка регионов и проектов
- `Service` - услуги/товары
- `ServiceAgent` - связка услуг и агентов
- `SaleRule` - правила продаж
- `Invoice` - счета
- `Order` - заказы
- `OrderCard` - карты в заказе
- `ReplenishmentItem` - элементы пополнения
- `PurchaseItem` - элементы покупки
- `Card` - кэш информации о картах

## Использование

Добавьте зависимость в ваш проект:

```kotlin
implementation(project(":abt-sbol-model"))
```

## Миграции

Миграции Flyway находятся в модуле `abt-sbol-gate` в директории `src/main/resources/db/migration/`. 