package ru.sbertroika.tkp3.abt.api.model

import java.time.ZonedDateTime
import java.util.*

enum class AbtType {
    TICKET, WALLET
}

data class AbtTap(
    /**
     * Идентификатор транзакции
     */
    val trxId: UUID,

    /**
     * Время проведения операции
     */
    val createdAt: ZonedDateTime,

    /**
     * Идентификатор проекта
     */
    val projectId: UUID,

    /**
     * Идентификатор организации
     */
    var orgId: UUID,

    /**
     * Тип
     * @see AbtType
     */
    val type: AbtType,

    /**
     * Порядковый номер операции на терминале (уникальный в рамках смены)
     */
    val ern: Int,

    /**
     * Идентификатор терминала
     */
    val terminalId: UUID,

    /**
     * Серийный номер терминала
     */
    val terminalSerial: String,

    /**
     * Стоимость билета
     */
    val amount: Int?,

    /**
     * Номер смены на терминала
     */
    val shiftNum: Int,

    /**
     * Битмап после списания (если есть)
     */
    val raw: String?,

    /**
     * Идентификатор шаблона списания
     */
    val templateId: UUID,

    /**
     * Версия шаблона списания
     */
    val templateVersion: Int,

    /**
     * Идентификатор абонемента
     */
    val abonementId: Long? = null,

    /**
     * Идентификатор носителя (UID)
     */
    val cardUid: String? = null,

    /**
     * Идентификатор носителя (Hash(PAN))
     */
    val cardHash: String? = null,

    /**
     * Вариант хэширования (HashType)
     */
    val cardHashType: String? = null,

    val transportType: TransportType
)

enum class TransportType {
    BUS, TROLLEYBUS, TRAM, METRO
}