# Pipeline Workflow Spreading Plan
Execute the pipeline generation script or process defined in the `pipeline_gen.md` file. If this is a markdown file containing instructions or code snippets, follow those instructions to run the pipeline generation process. If it contains executable code blocks, run them in the appropriate environment. Please check the contents of `pipeline_gen.md` first to understand what needs to be executed and how, then proceed with running the pipeline generation process as specified in that file.

## Section 1: Preparation

1. **Service Pipeline Template (pro-gate/.gitlab-ci.yml)**

```yaml
include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# PRO-GATE: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
pro_gate_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "pro-gate"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-gate/**
        - ./*

pro_gate_helm_kubeval_testing_develop:
  stage: test
  needs: [pro_gate_build_develop]
  variables:
    SERVICE_NAME: "pro-gate"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-gate/**
        - charts/pro-gate/**

pro_gate_deploy_chart_develop:
  stage: deploy
  needs:
    - pro_gate_helm_kubeval_testing_develop
    - job: pro_gate_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "pro-gate"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-gate/**
        - charts/pro-gate/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - pro-gate/**

pro_gate_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-gate"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

pro_gate_helm_kubeval_testing_tag:
  stage: test
  needs:
    - pro_gate_build_tag
  variables:
    SERVICE_NAME: "pro-gate"
  extends:
    - .validate_helm_template
  <<: *tag_rules

pro_gate_deploy_chart_tag:
  stage: deploy
  needs:
    - pro_gate_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-gate"
  extends:
    - .deploy_helm_template
  <<: *tag_rules
```

2. **Main Pipeline Template (root .gitlab-ci.yml)**

```yaml
# GitLab CI/CD Pipeline для pro-domain (shell executor + docker run)

include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/main-pipeline-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates
  - local: '/pro-gate/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/pro-gate-private/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/pro-processing/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/pro-ui/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/pro-manifest-processing/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always

variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  DOCKER_BASE_IMAGE: "gradle:8.5-jdk17"
  KUBE_NAMESPACE: "pro"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - .gradle/daemon

stages:
  - publish-snapshot
  - build
  - test
  - deploy
  - release

.docker_gradle: &docker_gradle
  before_script:
    - echo "Docker base image $DOCKER_BASE_IMAGE"
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh

# ========================
# 1. ЛЮБАЯ ВЕТКА (кроме develop/master): build + test
# ========================
build_and_test:
  <<: *docker_gradle
  stage: test
  extends:
    - .gradle_build_new_template
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME != "develop" && $CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_TAG == null

# ========================
# 2. DEVELOP: build + test + publish SNAPSHOT (одним шагом)
# ========================

develop_build_test_publish:
  <<: *docker_gradle
  stage: publish-snapshot
  extends:
    - .gradle_build_test_publish_template
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  environment:
    name: develop
    url: https://nexus.sbertroika.tech/repository/maven-snapshots/

# ========================
# 3. MASTER: только release (без отдельного build/test)
# ========================

release:
  <<: *docker_gradle
  stage: release
  extends:
    - .gradle_release_template
```

3. **VirtualService Template: Simple Configuration**
   - Example `virtualservice.yaml`:
     ```yaml
     kind: VirtualService
     apiVersion: networking.istio.io/v1beta1
     metadata:
       name: {{ include "service.fullname" . }}
       namespace: {{ .Release.Namespace }}
     spec:
       hosts:
         - {{ .Values.virtualService.host }}
       gateways:
         - {{ .Values.virtualService.gateway }}
       http:
         - match:
             - uri:
                 prefix: {{ .Values.virtualService.apiPrefix | default "/api/" }}
           route:
             - destination:
                 host: {{ .Values.virtualService.apiDestination.host }}
                 port:
                   number: {{ .Values.virtualService.apiDestination.port }}
         - match:
             - uri:
                 prefix: /
           route:
             - destination:
                 host: {{ .Values.virtualService.destination.host }}
                 port:
                   number: {{ .Values.virtualService.destination.port }}
     ```
   - Example `values.yaml` block:
     ```yaml
     virtualService:
       host: "crm-pro-dev.sbertroika.tech"
       gateway: "istio-ingressgateway/pro-ui-gateway"
       apiPrefix: "/api/"
       apiDestination:
         host: "pro-gate-private.pro.svc.cluster.local"
         port: 5005
       destination:
         host: "pro-ui.pro.svc.cluster.local"
         port: 80
     ```

4. **Environment-Specific Values Files**
   - For each chart, create `values.dev.yaml` and `values.prod.yaml`.
   - Only keep stage-dependent values in these files (e.g., `replicaCount`, `resources`, and any values that differ by environment).
   - Remove all other values from these files to avoid duplication.
   - Example:
     - `values.yaml`: contains sharable/default values.
     - `values.dev.yaml` and `values.prod.yaml`: only override what is needed for each environment.

5. **Helm Validation Script for All Charts** skip this step
   - Add a script named `hlmvld.sh` in the `charts/` folder with the following logic:
     - Scans all charts in `charts/`.
     - Stage defaults to `dev`.
     - Prompts for namespace once and reuses it for all charts.
     - Validates each chart using `helm install --dry-run` with both `values.yaml` and the stage-specific values file.
   - Example script:
     ```bash
     #!/bin/bash
     # Helm validation for all charts in charts/ folder
     read -p "Enter namespace for validation: " NAMESPACE
     STAGE="dev"
     for CHART_DIR in ./*/ ; do
       CHART_NAME=$(basename "$CHART_DIR")
       if [[ -f "$CHART_DIR/values.yaml" && -f "$CHART_DIR/values.$STAGE.yaml" ]]; then
         echo "Validating $CHART_NAME in namespace $NAMESPACE (stage: $STAGE)"
         helm install --dry-run -n "$NAMESPACE" "$CHART_NAME" "$CHART_DIR" \
           -f "$CHART_DIR/values.yaml" \
           -f "$CHART_DIR/values.$STAGE.yaml"
         if [[ $? -ne 0 ]]; then
           echo "Error: Helm validation failed for $CHART_NAME"
           exit 1
         fi
       else
         echo "Skipping $CHART_NAME: missing values.yaml or values.$STAGE.yaml"
       fi
     done
     echo "Helm validation completed for all charts."
     exit 0
     ```
   - Place this script in the `charts/` folder. When running the plan, create or update this script as needed.

## Section 2: Execution in New Projects
This section is instruction. Do not update this section without approval.
1. **Insert Service Pipeline (Template 1) into Each Service Directory**
   - For each service, add the service pipeline as `.gitlab-ci.yml` in the service directory.
   - **Important:** Update the template to use the actual service name (replace all occurrences of the example name, e.g., `pro-gate`, with your service's name).
   - **Note:** For UI services, use `.docker_build_and_push` instead of `.docker_gradle_build_and_push`.

2. **Insert Main Pipeline (Template 2) into Project Root**
   - Place the main pipeline loader into the root of each project. Dynamically generate the `include:` list based on available service pipelines.
   - **Important:** Each service include should have rules to prevent execution during merge request events:
     ```yaml
     - local: '/service-name/.gitlab-ci.yml'
       rules:
         - if: $CI_PIPELINE_SOURCE == "merge_request_event"
           when: never
         - when: always
     ```

3. **Update Charts with Healthchecks and Environment Values**
   - Healthcheck configuration is already present in current charts with `enabled: false` by default.
   - do not add healthcheck and readiness configurations into the values.dev and values.prod
   - Update deployment templates to include 
      conditional health check probes:
     ```yaml
     {{- if .Values.readinessProbe.enabled }}
     readinessProbe:
       httpGet:
         path: {{ .Values.readinessProbe.path }}
         port: {{ .Values.readinessProbe.port }}
       initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
       periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
       failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
       successThreshold: {{ .Values.readinessProbe.successThreshold }}
       timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
     {{- end }}
     {{- if .Values.livenessProbe.enabled }}
     livenessProbe:
       httpGet:
         path: {{ .Values.livenessProbe.path }}
         port: {{ .Values.livenessProbe.port }}
       initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
       periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
       failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
       successThreshold: {{ .Values.livenessProbe.successThreshold }}
       timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
     {{- end }}
     ```
   - Current values.yaml templates already include health check probes configuration:
     ```yaml
     livenessProbe:
       enabled: false
       path: /actuator/health/liveness
       port: 8080
       initialDelaySeconds: 10
       periodSeconds: 30
       failureThreshold: 3
       successThreshold: 1
       timeoutSeconds: 1

     readinessProbe:
       enabled: false
       path: /actuator/health/readiness
       port: 8080
       initialDelaySeconds: 60
       periodSeconds: 30
       failureThreshold: 3
       successThreshold: 1
       timeoutSeconds: 1
     ```
   - Ensure `values.dev.yaml` and `values.prod.yaml` exist and only contain stage-dependent values.

4. **Update Namespace References in Helm Templates**
   - Replace all namespace references in Helm chart templates with the standard `{{ .Release.Namespace }}` format.
   - This ensures consistent namespace handling across all charts and environments.
   - Example: Replace `namespace: {{ .Values.namespace | default (include "chart.fullname" .) }}` with `namespace: {{ .Release.Namespace }}`

5. **Add/Update Helm Validation Script**
   - Place the updated `hlmvld.sh` script in the `charts/` folder as described above.

---

**Note:**
- All templates should be parameterized for easy reuse.
- Ensure that all inserted templates and values are consistent with the examples provided above.
- **Release jobs are configured to NOT run on Merge Request events** (`$CI_PIPELINE_SOURCE != "merge_request_event"`). This ensures that:
  - Release jobs only run after MR is merged to master/main branch
  - No accidental releases are triggered during MR review process
  - Production deployments happen only on actual commits to master branch