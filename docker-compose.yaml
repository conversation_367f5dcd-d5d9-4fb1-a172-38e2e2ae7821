version: '3.7'

services:
  abt_gate:
    image: abt-gate:local
    container_name: abt-gate
    ports:
      - 5010:5000
      - 8080:8080
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://abt-gate-db:5432/abt
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "true"
      R2DB_URL: ***********************************************/abt
      KAFKA_SERVERS: kafka:29092
      ABT_EMISSION_URL: abt-emission:5000
    depends_on:
      - abt_gate_db
      - kafka
      - abt_emission
    networks:
      - abt-domain_default

  abt_emission:
    image: abt-emission:local
    container_name: abt-emission
    ports:
      - 5008:5000
      - 8081:8080
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://abt-emission-db:5432/abt_emission
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "true"
      FLYWAY_DB_URL: postgresql://abt-emission-db:5432/abt_emission
    depends_on:
      - abt_emission_db
    networks:
      - abt-domain_default

  abt_processing:
    image: abt-processing:local
    container_name: abt-processing
    ports:
      - 8082:8080
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://abt-gate-db:5432/abt
      DB_USER: postgres
      DB_PASSWORD: postgres
      R2DB_URL: ***********************************************/abt
      KAFKA_SERVERS: kafka:29092
      ABT_EMISSION_URL: abt-emission:5000
    depends_on:
      - abt_gate_db
      - kafka
      - abt_emission
    networks:
      - abt-domain_default

  abt_sbol_gate:
    image: abt-sbol-gate:local
    container_name: abt-sbol-gate
    ports:
      - 8083:8080
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://abt-sbol-gate-db:5432/abt_sbol_gate
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "true"
      R2DB_URL: ****************************************************/abt_sbol_gate
      ABT_EMISSION_URL: abt-emission:5000
      EMV_EMISSION_URL: emv-emission:5000
    depends_on:
      - abt_sbol_gate_db
      - abt_emission
    networks:
      - abt-domain_default

  abt_gate_db:
    image: postgres:14
    container_name: abt-gate-db
    restart: always
    ports:
      - 5432:5432
    volumes:
      - ./data/abt_gate:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: abt
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - abt-domain_default

  abt_emission_db:
    image: postgres:14
    container_name: abt-emission-db
    restart: always
    ports:
      - 5433:5432
    volumes:
      - ./data/abt_emission:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: abt_emission
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - abt-domain_default

  abt_sbol_gate_db:
    image: postgres:14
    container_name: abt-sbol-gate-db
    restart: always
    ports:
      - 5434:5432
    volumes:
      - ./data/abt_sbol_gate:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: abt_sbol_gate
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - abt-domain_default

  zoo1:
    image: zookeeper
    restart: always
    hostname: zoo1
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181
    networks:
      - abt-domain_default

  kafka:
    image: confluentinc/cp-kafka:7.5.9
    container_name: kafka
    ports:
      - 29092:29092
    depends_on:
      - zoo1
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zoo1:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - abt-domain_default

  kafka-ui:
    image: provectuslabs/kafka-ui:v0.7.2
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - 8086:8080
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zoo1:2181
    networks:
      - abt-domain_default

networks:
  abt-domain_default:
    driver: bridge 