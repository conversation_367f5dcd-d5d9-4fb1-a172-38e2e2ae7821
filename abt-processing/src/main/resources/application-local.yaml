spring:
  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/abt}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    abt_in_topic: ${ABT_IN_TOPIC:PRO.INTERNAL.ABT}
    abt_trx_in_topic: ${ABT_IN_TOPIC:ABT.TRX}
    abt_operation_out_topic: ${ABT_SUCCESS_OUT_TOPIC:ABT.OPERATION.OUT}
    abt_error_out_topic: ${ABT_ERROR_OUT_TOPIC:ABT.ERROR.OUT}
    abt_processing_in_group: ${BATCH_PROCESSING_IN_GROUP:abt_processing_in_group}
    abt_batch_processing_in_group: ${BATCH_PROCESSING_IN_GROUP:abt_batch_processing_in_group}

service:
  abt_emission_url: ${ABT_EMISSION_URL:localhost:5008} 