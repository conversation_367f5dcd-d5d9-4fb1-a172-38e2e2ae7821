spring:
  application:
    name: abt-processing

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/abt}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    abt_in_topic: ${ABT_IN_TOPIC:PRO.INTERNAL.ABT}
    abt_trx_in_topic: ${ABT_IN_TOPIC:ABT.TRX}
    abt_operation_out_topic: ${ABT_SUCCESS_OUT_TOPIC:ABT.OPERATION.OUT}
    abt_error_out_topic: ${ABT_ERROR_OUT_TOPIC:ABT.ERROR.OUT}
    abt_processing_in_group: ${BATCH_PROCESSING_IN_GROUP:abt_processing_in_group}
    abt_batch_processing_in_group: ${BATCH_PROCESSING_IN_GROUP:abt_batch_processing_in_group}

#zookeeper:
#  nodes: ${ZOOKEEPER_NODES:***********:2181,**********:2181,***********:2181}
#  rbs:
#    root_path: ${ZOOKEEPER_ROOT_PATH:/abt/rbs}
#    terminal_batch_num:
#      path: ${zookeeper.rbs.root_path}/counters/%s/batchId

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

server:
  port: 8080

service:
  abt_emission_url: ${abt_EMISSION_URL:abt-emission.abt.svc.cluster.local:5000}