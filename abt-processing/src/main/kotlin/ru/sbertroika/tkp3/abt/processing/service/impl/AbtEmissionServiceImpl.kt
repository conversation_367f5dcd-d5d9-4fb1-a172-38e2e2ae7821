package ru.sbertroika.tkp3.abt.processing.service.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.emission.v1.ABTEmissionServiceGrpcKt
import ru.sbertroika.abt.emission.v1.CardInfoRequest
import ru.sbertroika.abt.emission.v1.EmissionRequest
import ru.sbertroika.tkp3.abt.processing.service.AbtEmissionService
import java.util.UUID

@Service
class AbtEmissionServiceImpl(
    @Value("\${service.abt_emission_url}")
    private val abtEmissionServiceUrl: String
) : AbtEmissionService {

    private val channel = ManagedChannelBuilder.forTarget(abtEmissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)

    override suspend fun emission(uid: String, projectId: UUID): Either<Throwable, UUID> {
        val request = EmissionRequest.newBuilder()
            .setUid(uid)
            .setProjectId(projectId.toString())
            .build()
        val response = client.emission(request)
        if (response.hasError()) throw Error("getCardInfo() from abt-emission err: " + response.error.message)

        return UUID.fromString(response.cardId).right()
    }
}