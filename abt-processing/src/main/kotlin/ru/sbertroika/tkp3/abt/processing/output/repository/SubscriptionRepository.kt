package ru.sbertroika.tkp3.abt.processing.output.repository

import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.Subscription
import java.util.*

interface SubscriptionRepository : CoroutineCrudRepository<Subscription, UUID> {
    suspend fun findAllByCrdId(crdId: UUID): List<Subscription>
    suspend fun findAllByAbonementId(abonementId: Long): Subscription?
} 