package ru.sbertroika.tkp3.abt.processing.service.impl

import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.abt.api.model.AbtTap
import ru.sbertroika.tkp3.abt.api.model.TransportType
import ru.sbertroika.tkp3.abt.model.AbtPassStatusType
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateCounterType
import ru.sbertroika.tkp3.abt.model.SubsriptionTrx
import ru.sbertroika.tkp3.abt.processing.Util.mapper
import ru.sbertroika.tkp3.abt.processing.output.repository.SubscriptionBaseCounterRepository
import ru.sbertroika.tkp3.abt.processing.output.repository.SubscriptionRepository
import ru.sbertroika.tkp3.abt.processing.service.AbtEmissionService
import ru.sbertroika.tkp3.abt.processing.service.AbtPassService
import java.time.ZonedDateTime
import java.util.*

@Service
class AbtPassServiceImpl(
    kafkaProducerFactory: ProducerFactory<String, Any>,

    private val subscriptionRepository: SubscriptionRepository,
    private val subscriptionBaseCounterRepository: SubscriptionBaseCounterRepository,
    private val abtEmissionService: AbtEmissionService,

    @Value("\${spring.kafka.abt_trx_in_topic}")
    private val trxTopic: String
) : AbtPassService {

    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    override suspend fun passTicket(tap: AbtTap): Either<Throwable, Unit> {
        val subscription = subscriptionRepository.findAllByAbonementId(tap.abonementId!!) ?: throw Error("Abonement id=${tap.abonementId} not found")

        val counters = subscriptionBaseCounterRepository.findAllBySubscriptionId(subscription.id)
        if (counters.isEmpty()) throw Error("Counters not found by Subscription id=${subscription.id}")

        val cardId = if (tap.cardUid.isNullOrEmpty()) {
            UUID.randomUUID()
            //TODO EMV emission
        } else {
            abtEmissionService.emission(tap.cardUid!!, tap.projectId).getOrElse {
                throw Error("Card uid=${tap.cardUid} not found")
            }
        }

        // 1. Ищем сначала счетчик для конкретного типа ТС
        val counterByTt = when (tap.transportType) {
            TransportType.BUS -> counters.find { it.isBus }
            TransportType.TROLLEYBUS -> counters.find { it.isBus }
            TransportType.TRAM -> counters.find { it.isBus }
            TransportType.METRO -> counters.find { it.isBus }
        }

        if (counterByTt == null) throw Error("Counters not found by Subscription id=${subscription.id} for TT=${tap.transportType}")

        val newCounterByTc = when (SubscriptionTemplateCounterType.valueOf(counterByTt.counterType)) {
            SubscriptionTemplateCounterType.ALL,
            SubscriptionTemplateCounterType.ALLOW_LIST,
            SubscriptionTemplateCounterType.SINGLE -> {
                if (counterByTt.counterValue - 1 < 0) {
                    //TODO добавить настройку разрешающую или запрещающую перелимит
                    publishTrx(
                        SubsriptionTrx(
                            trxId = tap.trxId,
                            createdAt = tap.createdAt,
                            recordAt = ZonedDateTime.now(),
                            crdId = cardId,
                            status = AbtPassStatusType.OVERLIMIT,
                            subscriptionId = subscription.id,
                            templateId = subscription.ttId,
                            templateVer = subscription.ttVersion.toUInt(),
                            terminalSerial = tap.terminalSerial,
                            shiftNum = tap.shiftNum.toUInt(),
                            ern = tap.ern.toUInt(),
                            amount = tap.amount?.toUInt(),
                        )
                    ).getOrElse {
                        throw Error(it)
                    }

                    subscriptionBaseCounterRepository.save(counterByTt.copy(counterValue = counterByTt.counterValue - 1))
                    return Unit.right()
                } else {
                    counterByTt.copy(counterValue = counterByTt.counterValue - 1)
                }
            }

            SubscriptionTemplateCounterType.ALL_UNLIMITED,
            SubscriptionTemplateCounterType.ALLOW_LIST_UNLIMITED -> {
                counterByTt.copy(counterValue = counterByTt.counterValue + 1)
            }
        }

        publishTrx(
            SubsriptionTrx(
                trxId = tap.trxId,
                createdAt = tap.createdAt,
                recordAt = ZonedDateTime.now(),
                crdId = cardId,
                status = AbtPassStatusType.SUCCESS,
                subscriptionId = subscription.id,
                templateId = subscription.ttId,
                templateVer = subscription.ttVersion.toUInt(),
                terminalSerial = tap.terminalSerial,
                shiftNum = tap.shiftNum.toUInt(),
                ern = tap.ern.toUInt(),
                amount = tap.amount?.toUInt(),
            )
        ).getOrElse {
            throw Error(it)
        }
        subscriptionBaseCounterRepository.save(newCounterByTc)

        return Unit.right()
    }

    override suspend fun passWallet(tap: AbtTap): Either<Throwable, Unit> {
        val subscription = subscriptionRepository.findAllByAbonementId(tap.abonementId!!) ?: throw Error("Abonement id=${tap.abonementId} not found")

        val counters = subscriptionBaseCounterRepository.findAllBySubscriptionId(subscription.id)
        if (counters.isEmpty()) throw Error("Counters not found by Subscription id=${subscription.id}")

        val cardId = if (tap.cardUid.isNullOrEmpty()) {
            UUID.randomUUID()
            //TODO EMV emission
        } else {
            abtEmissionService.emission(tap.cardUid!!, tap.projectId).getOrElse {
                throw Error("Card uid=${tap.cardUid} not found")
            }
        }

        // Для кошелька используем amount из tap
        val amount = tap.amount ?: throw Error("Amount is required for wallet pass")

        // 1. Ищем сначала счетчик для конкретного типа ТС
        val counterByTt = when (tap.transportType) {
            TransportType.BUS -> counters.find { it.isBus }
            TransportType.TROLLEYBUS -> counters.find { it.isBus }
            TransportType.TRAM -> counters.find { it.isBus }
            TransportType.METRO -> counters.find { it.isBus }
        }

        if (counterByTt == null) throw Error("Counters not found by Subscription id=${subscription.id} for TT=${tap.transportType}")

        val newCounterByTc = when (SubscriptionTemplateCounterType.valueOf(counterByTt.counterType)) {
            SubscriptionTemplateCounterType.ALL,
            SubscriptionTemplateCounterType.ALLOW_LIST,
            SubscriptionTemplateCounterType.SINGLE -> {
                if (counterByTt.counterValue - amount < 0) {
                    //TODO добавить настройку разрешающую или запрещающую перелимит
                    publishTrx(
                        SubsriptionTrx(
                            trxId = tap.trxId,
                            createdAt = tap.createdAt,
                            recordAt = ZonedDateTime.now(),
                            crdId = cardId,
                            status = AbtPassStatusType.OVERLIMIT,
                            subscriptionId = subscription.id,
                            templateId = subscription.ttId,
                            templateVer = subscription.ttVersion.toUInt(),
                            terminalSerial = tap.terminalSerial,
                            shiftNum = tap.shiftNum.toUInt(),
                            ern = tap.ern.toUInt(),
                            amount = tap.amount?.toUInt(),
                        )
                    ).getOrElse {
                        throw Error(it)
                    }

                    subscriptionBaseCounterRepository.save(counterByTt.copy(counterValue = counterByTt.counterValue - amount))
                    return Unit.right()
                } else {
                    counterByTt.copy(counterValue = counterByTt.counterValue - amount)
                }
            }

            SubscriptionTemplateCounterType.ALL_UNLIMITED,
            SubscriptionTemplateCounterType.ALLOW_LIST_UNLIMITED -> {
                counterByTt.copy(counterValue = counterByTt.counterValue + 1)
            }
        }

        publishTrx(
            SubsriptionTrx(
                trxId = tap.trxId,
                createdAt = tap.createdAt,
                recordAt = ZonedDateTime.now(),
                crdId = cardId,
                status = AbtPassStatusType.SUCCESS,
                subscriptionId = subscription.id,
                templateId = subscription.ttId,
                templateVer = subscription.ttVersion.toUInt(),
                terminalSerial = tap.terminalSerial,
                shiftNum = tap.shiftNum.toUInt(),
                ern = tap.ern.toUInt(),
                amount = tap.amount?.toUInt(),
            )
        ).getOrElse {
            throw Error(it)
        }
        subscriptionBaseCounterRepository.save(newCounterByTc)

        return Unit.right()
    }

    private fun publishTrx(trx: SubsriptionTrx): Either<Throwable, Unit> = try {
        val out = ProducerRecord<String, Any>(
            trxTopic, trx.trxId.toString(), mapper.writeValueAsString(
                trx
            )
        )
        producer.send(out)
        Unit.right()
    } catch (e: Exception) {
        Error(e).left()
    }
}