package ru.sbertroika.tkp3.abt.processing.output.repository

import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionBaseCounter
import java.util.*

interface SubscriptionBaseCounterRepository : CoroutineCrudRepository<SubscriptionBaseCounter, UUID> {
    suspend fun findAllBySubscriptionId(subscriptionId: UUID): List<SubscriptionBaseCounter>
} 