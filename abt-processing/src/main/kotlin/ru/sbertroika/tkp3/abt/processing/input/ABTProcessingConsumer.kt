package ru.sbertroika.tkp3.abt.processing.input

import arrow.core.Either
import arrow.core.left
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.abt.api.model.AbtTap
import ru.sbertroika.tkp3.abt.api.model.AbtType
import ru.sbertroika.tkp3.abt.processing.Util.mapper
import ru.sbertroika.tkp3.abt.processing.output.model.ErrorOperation
import ru.sbertroika.tkp3.abt.processing.service.AbtPassService
import java.time.Duration

@Component
class ABTProcessingConsumer(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    private val passService: AbtPassService,

    @Value("\${spring.kafka.abt_error_out_topic}")
    private val errorTopic: String
) {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    @KafkaListener(groupId = "\${spring.kafka.abt_processing_in_group}", topics = ["\${spring.kafka.abt_in_topic}"])
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        try {
            toOperationMessage(record.value()).fold(
                {
                    logger.error("Error process transaction ${record.key()}: ${it.message}")
                    acknowledgment.acknowledge()
                },
                { message ->
                    logger.info("Process message type {}, message {}", message.type, message)

                    when(message.type) {
                        AbtType.TICKET -> passService.passTicket(message)
                        AbtType.WALLET -> passService.passWallet(message)
                        else -> Error("unknown TPP Type").left()
                    }.fold(
                        { err ->
                            //TODO Забросить в очередь на повторы а не откладывать задание
                            logger.error("Error process transaction ${record.key()}: ${err.message}")
                            val out = ProducerRecord<String, Any>(
                                errorTopic, record.key(), mapper.writeValueAsString(
                                    ErrorOperation(
                                        trxId = record.key(),
                                        error = err.message
                                    )
                                )
                            )
                            producer.send(out)

                            acknowledgment.nack(Duration.ofSeconds(10))
                        },
                        { _ ->
                            logger.info("End process transaction trx={}", message.trxId)
                            acknowledgment.acknowledge()
                        }
                    )
                }
            )
        } catch (e: Exception) {

        }
    }

    private fun toOperationMessage(data: String): Either<Throwable, AbtTap> = Either.catch {
        mapper.readValue<AbtTap>(data)
    }
}