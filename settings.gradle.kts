rootProject.name = "abt"

include(
    ":abt-model",
    ":abt-api",
    ":abt-gate-api",
    ":abt-emission-api",
    ":abt-emission",
    ":abt-gate",
    ":abt-processing",
    ":abt-gate-it",
    ":abt-sbol-gate",
    ":abt-sbol-model",
    ":abt-sbol-controller",
    ":abt-controller",
    ":abt-ui",
)

dependencyResolutionManagement {
    repositories {
        gradlePluginPortal()
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }

    versionCatalogs {
        create("libs") {
            from("ru.sbertroika.sharedcatalog:gradle-common:1.3")
        }
    }
}