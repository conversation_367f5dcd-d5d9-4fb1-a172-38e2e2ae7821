# Настройка API Endpoints

## Конфигурация API клиентов

В проекте используются два API клиента для разных сервисов:

### 1. sbolApiClient (http://localhost:8083)
Используется для **билетного меню** и связанных сервисов:
- `ServiceService.js` - услуги билетного меню
- Другие сервисы билетного меню

### 2. abtApiClient (http://localhost:8086)
Используется для **абонементов, кошельков и шаблонов**:
- `SubscriptionService.js` - абонементы и кошельки
- `SubscriptionTemplateService.js` - шаблоны абонементов

## Настройка в ApiClient.js

```javascript
// Создаем экземпляр клиента для abt-sbol-controller (билетное меню)
export const sbolApiClient = new ApiClient('http://localhost:8083');

// Создаем экземпляр клиента для abt-controller (абонементы, кошельки, шаблоны)
export const abtApiClient = new ApiClient('http://localhost:8086');
```

## Использование в сервисах

### Для билетного меню:
```javascript
import { sbolApiClient } from './ApiClient.js';

// Используем sbolApiClient для запросов к билетному меню
const response = await sbolApiClient.get('/api/v1/services');
```

### Для абонементов и кошельков:
```javascript
import { abtApiClient } from './ApiClient.js';

// Используем abtApiClient для запросов к абонементам
const response = await abtApiClient.get('/api/v1/subscriptions');
```

## Проверка настройки

1. Убедитесь, что `abt-sbol-controller` запущен на порту 8083
2. Убедитесь, что `abt-controller` запущен на порту 8086
3. Проверьте, что запросы идут на правильные endpoints

## Изменение портов

Если нужно изменить порты, обновите конфигурацию в `src/service/ApiClient.js`:

```javascript
// Измените URL на нужные порты
export const sbolApiClient = new ApiClient('http://localhost:НОВЫЙ_ПОРТ_8083');
export const abtApiClient = new ApiClient('http://localhost:НОВЫЙ_ПОРТ_8086');
``` 