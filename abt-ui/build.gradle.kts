import com.github.gradle.node.npm.task.NpmTask

buildscript {
    repositories {
        gradlePluginPortal()
    }
    dependencies {
        classpath("com.github.node-gradle:gradle-node-plugin:7.1.0")
    }
}

plugins {
    id("com.github.node-gradle.node") version "7.1.0"
}

// Настройка директорий
val protoDir = file("proto")
val generatedDir = file("src/generated")
val privateApiProtoDir = file("../pro-gate-private-api/src/main/proto")
val commonProtoDir = file("../pro-common/src/main/proto")
val apiProtoDir = file("../pro-api/src/main/proto")

// Задача для очистки директорий
tasks.register<Delete>("cleanProto") {
    delete(protoDir)
    delete(generatedDir)
}

// Задача для копирования proto файлов
tasks.register<Copy>("copyProtoFiles") {
    dependsOn("cleanProto", ":pro-gate-private-api:extractIncludeProto")

    // Создаем директории
    doFirst {
        protoDir.mkdirs()
        generatedDir.mkdirs()
    }

    // Копируем основной proto файл из pro-gate-private-api
    from(privateApiProtoDir) {
        include("*.proto")
    }

    // Копируем извлеченные proto файлы из build директории pro-gate-private-api
    from(file("../pro-gate-private-api/build/extracted-include-protos/main")) {
        include("*.proto")
    }

    into(protoDir)

    doLast {
        println("Proto files copied to ${protoDir.absolutePath}")
        println("Copied files:")
        protoDir.listFiles()?.forEach { file ->
            if (file.isFile && file.name.endsWith(".proto")) {
                println("  - ${file.name}")
            }
        }
    }
}

// Задача для генерации gRPC клиента
tasks.register<NpmTask>("generateGrpcClient") {
    dependsOn("copyProtoFiles", "npmInstall")

    args = listOf("run", "generate-proto")

    inputs.dir(protoDir)
    outputs.dir(generatedDir)

    doLast {
        println("gRPC client generated in ${generatedDir.absolutePath}")
    }
}

tasks.register<NpmTask>("npmRunFormat") {
    dependsOn("npmInstall")

    args = listOf("run", "format")
}

tasks.register<NpmTask>("runDev") {
    dependsOn("generateGrpcClient")

    args = listOf("run", "dev")

    doFirst {
        println("Starting development server with generated gRPC client...")
    }
}

// Оставляем старую задачу для совместимости
tasks.register<NpmTask>("npmRunDev") {
    dependsOn("runDev")
}
