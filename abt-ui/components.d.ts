/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Avatar: typeof import('primevue/avatar')['default']
    BestSellingWidget: typeof import('./src/components/dashboard/BestSellingWidget.vue')['default']
    Button: typeof import('primevue/button')['default']
    Calendar: typeof import('primevue/calendar')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Column: typeof import('primevue/column')['default']
    DataTable: typeof import('primevue/datatable')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divider: typeof import('primevue/divider')['default']
    Dropdown: typeof import('primevue/dropdown')['default']
    FeaturesWidget: typeof import('./src/components/landing/FeaturesWidget.vue')['default']
    FloatingConfigurator: typeof import('./src/components/FloatingConfigurator.vue')['default']
    FooterWidget: typeof import('./src/components/landing/FooterWidget.vue')['default']
    HeroWidget: typeof import('./src/components/landing/HeroWidget.vue')['default']
    HighlightsWidget: typeof import('./src/components/landing/HighlightsWidget.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Menu: typeof import('primevue/menu')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    NotificationsWidget: typeof import('./src/components/dashboard/NotificationsWidget.vue')['default']
    PricingWidget: typeof import('./src/components/landing/PricingWidget.vue')['default']
    ProgressBar: typeof import('primevue/progressbar')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RecentSalesWidget: typeof import('./src/components/dashboard/RecentSalesWidget.vue')['default']
    RevenueStreamWidget: typeof import('./src/components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectButton: typeof import('primevue/selectbutton')['default']
    SplitButton: typeof import('primevue/splitbutton')['default']
    Splitter: typeof import('primevue/splitter')['default']
    SplitterPanel: typeof import('primevue/splitterpanel')['default']
    StatsWidget: typeof import('./src/components/dashboard/StatsWidget.vue')['default']
    TabPanel: typeof import('primevue/tabpanel')['default']
    TabView: typeof import('primevue/tabview')['default']
    Tag: typeof import('primevue/tag')['default']
    Textarea: typeof import('primevue/textarea')['default']
    Toast: typeof import('primevue/toast')['default']
    TopbarWidget: typeof import('./src/components/landing/TopbarWidget.vue')['default']
  }
  export interface ComponentCustomProperties {
    StyleClass: typeof import('primevue/styleclass')['default']
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
