# gRPC Client Setup для PRO UI

Этот документ описывает настройку автоматической генерации gRPC клиента для работы с pro-gate-private API.

## Обзор

При запуске задачи `:pro-ui:runDev` автоматически выполняются следующие шаги:

1. **Копирование proto файлов** из `pro-gate-private-api` и зависимостей
2. **Генерация TypeScript/JavaScript клиента** из proto файлов
3. **Запуск development сервера** с готовым gRPC клиентом

## Gradle задачи

### `:pro-ui:runDev`
Основная задача для разработки. Выполняет полный цикл подготовки и запуска:
```bash
./gradlew :pro-ui:runDev
```

### `:pro-ui:copyProtoFiles`
Копирует proto файлы из зависимых модулей:
```bash
./gradlew :pro-ui:copyProtoFiles
```

### `:pro-ui:generateGrpcClient`
Генерирует gRPC клиент из proto файлов:
```bash
./gradlew :pro-ui:generateGrpcClient
```

### `:pro-ui:cleanProto`
Очищает сгенерированные файлы:
```bash
./gradlew :pro-ui:cleanProto
```

## Структура файлов

```
pro-ui/
├── proto/                    # Скопированные proto файлы (генерируется)
│   ├── pro-gate-private-api.proto
│   ├── common.proto
│   └── common-pro.proto
├── src/
│   ├── generated/           # Сгенерированный gRPC клиент (генерируется)
│   │   ├── *.js
│   │   ├── *.d.ts
│   │   └── *_grpc_web_pb.js
│   └── service/
│       └── ProGatePrivateService.js  # Обертка для gRPC клиента
└── package.json
```

## Использование в коде

После генерации клиента можно использовать сервис:

```javascript
import ProGatePrivateService from '@/service/ProGatePrivateService';

// Инициализация клиента
ProGatePrivateService.initClient();

// Использование методов
const roles = await ProGatePrivateService.getRoleList();
const employees = await ProGatePrivateService.getEmployeeList(request);
```

## Зависимости

Добавлены следующие npm пакеты:

### Dependencies
- `grpc-web` - gRPC клиент для браузера
- `google-protobuf` - Protocol Buffers для JavaScript

### DevDependencies
- `@types/google-protobuf` - TypeScript типы для protobuf
- `grpc-tools` - Инструменты для генерации кода
- `grpc_tools_node_protoc_ts` - TypeScript генератор для protoc
- `typescript` - TypeScript компилятор

## NPM скрипты

- `npm run generate-proto` - Генерация JavaScript и TypeScript кода
- `npm run generate-proto-js` - Генерация только JavaScript кода
- `npm run generate-proto-ts` - Генерация только TypeScript кода

## Конфигурация

### Пути к proto файлам
Настроены в `build.gradle.kts`:
- `../pro-gate-private-api/src/main/proto` - Основные proto файлы
- `../pro-common/src/main/proto` - Общие proto файлы
- `../pro-api/src/main/proto` - API proto файлы

### gRPC-Web настройки
- **Режим**: `grpcwebtext` - совместимость с большинством прокси
- **Стиль импорта**: `typescript` - для лучшей типизации
- **Выходная директория**: `./src/generated`

## Разработка

1. **Запуск development сервера**:
   ```bash
   ./gradlew :pro-ui:runDev
   ```

2. **Обновление proto файлов**:
   При изменении proto файлов в `pro-gate-private-api` просто перезапустите `runDev`

3. **Отладка**:
   - Проверьте `proto/` директорию для скопированных файлов
   - Проверьте `src/generated/` для сгенерированного кода
   - Используйте browser dev tools для отладки gRPC запросов

## Примечания

- Сгенерированные файлы (`proto/` и `src/generated/`) исключены из git
- При первом запуске может потребоваться время для установки зависимостей
- gRPC-Web требует настройки прокси на стороне сервера (уже настроено в nginx.conf)
