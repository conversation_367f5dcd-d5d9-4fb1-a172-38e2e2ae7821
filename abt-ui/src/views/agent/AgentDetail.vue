<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { AgentService } from '@/service/AgentService';

const route = useRoute();
const router = useRouter();
const agentId = route.params.agentId;

const loading = ref(true);
const agent = ref(null);

onMounted(async () => {
    await loadAgent();
});

const loadAgent = async () => {
    try {
        loading.value = true;
        const data = await AgentService.getAgentById(agentId);
        agent.value = data;
    } catch (error) {
        console.error('Ошибка загрузки агента:', error);
    } finally {
        loading.value = false;
    }
};

const goBack = () => {
    router.push('/agent/registry');
};

const editAgent = () => {
    router.push(`/agent/registry/${agentId}/edit`);
};

const activateAgent = async () => {
    if (confirm('Активировать агента?')) {
        try {
            await AgentService.activateAgent(agentId);
            console.log('Агент активирован');
            await loadAgent();
        } catch (error) {
            console.error('Ошибка активации:', error);
        }
    }
};

const suspendAgent = async () => {
    const reason = prompt('Укажите причину приостановки:');
    if (reason) {
        try {
            await AgentService.suspendAgent(agentId, reason);
            console.log('Агент приостановлен');
            await loadAgent();
        } catch (error) {
            console.error('Ошибка приостановки:', error);
        }
    }
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'suspended': return 'danger';
        case 'draft': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'suspended': return 'Приостановлен';
        case 'draft': return 'Черновик';
        default: return status;
    }
};

const getTypeLabel = (type) => {
    switch (type) {
        case 'legal_entity': return 'Юридическое лицо';
        case 'individual': return 'Индивидуальный предприниматель';
        default: return type;
    }
};

const getServiceTypesLabels = (serviceTypes) => {
    const labels = {
        'card_sales': 'Продажа карт',
        'card_refill': 'Пополнение карт',
        'subscription_sales': 'Продажа абонементов',
        'mobile_app': 'Мобильное приложение',
        'online_refill': 'Онлайн пополнение',
        'terminal_maintenance': 'Обслуживание терминалов',
        'customer_service': 'Клиентский сервис'
    };
    
    return serviceTypes.map(type => labels[type] || type);
};
</script>

<template>
    <div class="agent-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Детали агента</h1>
            <div class="flex gap-2">
                <Button 
                    label="Назад к списку" 
                    icon="pi pi-arrow-left" 
                    outlined 
                    @click="goBack"
                />
                <Button 
                    v-if="agent"
                    label="Редактировать" 
                    icon="pi pi-pencil" 
                    @click="editAgent"
                />
            </div>
        </div>

        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных агента...</p>
            </div>
        </div>

        <div v-else-if="!agent" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Агент не найден</h3>
                <Button 
                    label="Вернуться к списку" 
                    icon="pi pi-arrow-left" 
                    @click="goBack"
                />
            </div>
        </div>

        <div v-else>
            <!-- Основная информация -->
            <div class="card mb-4">
                <div class="flex align-items-start justify-content-between mb-4">
                    <div class="flex align-items-center">
                        <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-users text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold m-0 mb-1">{{ agent.name }}</h2>
                            <p class="text-color-secondary m-0 mb-2 font-mono">{{ agent.code }}</p>
                            <div class="flex align-items-center gap-2">
                                <Tag 
                                    :value="getStatusLabel(agent.status)" 
                                    :severity="getStatusSeverity(agent.status)" 
                                />
                                <Tag 
                                    :value="getTypeLabel(agent.type)" 
                                    severity="info"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <Button 
                            v-if="agent.status === 'draft'"
                            label="Активировать" 
                            icon="pi pi-check" 
                            @click="activateAgent"
                        />
                        <Button 
                            v-if="agent.status === 'active'"
                            label="Приостановить" 
                            icon="pi pi-pause" 
                            severity="warning"
                            outlined
                            @click="suspendAgent"
                        />
                    </div>
                </div>

                <Divider />

                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">ИНН</label>
                            <p class="m-0 font-mono">{{ agent.inn }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6" v-if="agent.kpp">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">КПП</label>
                            <p class="m-0 font-mono">{{ agent.kpp }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Организация</label>
                            <p class="m-0">{{ agent.organizationName }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Контактное лицо</label>
                            <p class="m-0">{{ agent.contactPerson }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Телефон</label>
                            <p class="m-0">{{ agent.phone }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Email</label>
                            <p class="m-0">{{ agent.email }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Комиссия</label>
                            <p class="m-0 font-semibold">{{ agent.commissionRate }}%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Показатели -->
            <div class="grid mb-4">
                <div class="col-12 md:col-3">
                    <div class="card bg-blue-50 border-blue-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-blue-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-map-marker text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-blue-900">{{ agent.totalServicePoints }}</div>
                                <div class="text-blue-700">Точек обслуживания</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="card bg-green-50 border-green-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-green-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-check-circle text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-green-900">{{ agent.activeServicePoints }}</div>
                                <div class="text-green-700">Активных</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="card bg-orange-50 border-orange-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-orange-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-wallet text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-orange-900">{{ formatAmount(agent.monthlyTurnover) }}</div>
                                <div class="text-orange-700">Оборот в месяц</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="card bg-purple-50 border-purple-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-purple-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-percentage text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-purple-900">{{ agent.commissionRate }}%</div>
                                <div class="text-purple-700">Комиссия</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Услуги -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Предоставляемые услуги</h3>
                <div class="flex flex-wrap gap-2" v-if="agent.serviceTypes && agent.serviceTypes.length > 0">
                    <Tag 
                        v-for="serviceType in getServiceTypesLabels(agent.serviceTypes)" 
                        :key="serviceType"
                        :value="serviceType"
                        severity="info"
                    />
                </div>
                <div v-else class="text-center p-4 text-color-secondary">
                    Услуги не указаны
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.agent-detail {
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.bg-blue-50 { background-color: #eff6ff; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-orange-50 { background-color: #fff7ed; }
.bg-purple-50 { background-color: #faf5ff; }

.border-blue-200 { border-color: #bfdbfe; }
.border-green-200 { border-color: #bbf7d0; }
.border-orange-200 { border-color: #fed7aa; }
.border-purple-200 { border-color: #e9d5ff; }

.text-blue-900 { color: #1e3a8a; }
.text-green-900 { color: #14532d; }
.text-orange-900 { color: #9a3412; }
.text-purple-900 { color: #581c87; }

.text-blue-700 { color: #1d4ed8; }
.text-green-700 { color: #15803d; }
.text-orange-700 { color: #c2410c; }
.text-purple-700 { color: #7c3aed; }

.bg-blue-100 {
    background-color: #dbeafe;
}

.text-blue-600 {
    color: #2563eb;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
