<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { RouteService } from '@/service/RouteService';
import { StationService } from '@/service/StationService';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const routeId = route.params.routeId;
const isEdit = computed(() => !!routeId);

const loading = ref(false);
const saving = ref(false);
const availableStations = ref([]);

// Форма данных
const form = ref({
    number: '',
    name: '',
    isCircular: false,
    stations: []
});

// Валидация
const errors = ref({});

// Редактирование остановок
const editingRows = ref([]);
const stationOptions = ref([]);

onMounted(async () => {
    await loadAvailableStations();
    if (isEdit.value) {
        await loadRoute();
    }
});

const loadAvailableStations = async () => {
    try {
        const stations = await StationService.getStationsByProject(projectCode);
        availableStations.value = stations;
        stationOptions.value = stations.map(station => ({
            label: station.name,
            value: station.id
        }));
    } catch (error) {
        console.error('Ошибка загрузки остановок:', error);
    }
};

const loadRoute = async () => {
    try {
        loading.value = true;
        const routeData = await RouteService.getRouteById(routeId);
        if (routeData) {
            form.value = { ...routeData };
        }
    } catch (error) {
        console.error('Ошибка загрузки маршрута:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.number.trim()) {
        errors.value.number = 'Номер маршрута обязателен для заполнения';
    }
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Название маршрута обязательно для заполнения';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveRoute = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        if (isEdit.value) {
            await RouteService.updateRoute(routeId, form.value);
            console.log('Маршрут обновлен:', form.value);
        } else {
            await RouteService.createRoute(projectCode, form.value);
            console.log('Маршрут создан:', form.value);
        }
        
        router.push(`/pro/${projectCode}/nsi/route`);
        
    } catch (error) {
        console.error('Ошибка сохранения маршрута:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/nsi/route`);
};

// Функции для работы с остановками маршрута
const addStation = () => {
    const newOrder = form.value.stations.length > 0 
        ? Math.max(...form.value.stations.map(s => s.order)) + 1 
        : 1;
    
    form.value.stations.push({
        id: null,
        name: '',
        order: newOrder
    });
};

const removeStation = (index) => {
    form.value.stations.splice(index, 1);
    // Пересчитываем порядок
    form.value.stations.forEach((station, idx) => {
        station.order = idx + 1;
    });
};

const moveStationUp = (index) => {
    if (index > 0) {
        const temp = form.value.stations[index];
        form.value.stations[index] = form.value.stations[index - 1];
        form.value.stations[index - 1] = temp;
        
        // Обновляем порядок
        form.value.stations[index].order = index + 1;
        form.value.stations[index - 1].order = index;
    }
};

const moveStationDown = (index) => {
    if (index < form.value.stations.length - 1) {
        const temp = form.value.stations[index];
        form.value.stations[index] = form.value.stations[index + 1];
        form.value.stations[index + 1] = temp;
        
        // Обновляем порядок
        form.value.stations[index].order = index + 1;
        form.value.stations[index + 1].order = index + 2;
    }
};

const onRowEditSave = (event) => {
    const { newData, index } = event;
    const selectedStation = availableStations.value.find(s => s.id === newData.id);
    
    if (selectedStation) {
        form.value.stations[index] = {
            id: selectedStation.id,
            name: selectedStation.name,
            order: newData.order
        };
    }
};

const onRowEditCancel = (event) => {
    // Отменяем изменения
};

const getStationName = (stationId) => {
    const station = availableStations.value.find(s => s.id === stationId);
    return station ? station.name : '';
};
</script>

<template>
    <div class="route-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование маршрута' : 'Создание маршрута' }}
            </h2>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>
        
        <div class="card" v-if="!loading">
            <form @submit.prevent="saveRoute">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="number" class="font-medium">Номер маршрута *</label>
                            <InputText 
                                id="number"
                                v-model="form.number" 
                                :class="{ 'p-invalid': errors.number }"
                                placeholder="Например: 101, 102К"
                                class="w-full"
                            />
                            <small v-if="errors.number" class="p-error">{{ errors.number }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-8">
                        <div class="field">
                            <label for="name" class="font-medium">Название маршрута *</label>
                            <InputText 
                                id="name"
                                v-model="form.name" 
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="Введите название маршрута"
                                class="w-full"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <div class="flex align-items-center">
                                <Checkbox 
                                    id="isCircular" 
                                    v-model="form.isCircular" 
                                    :binary="true"
                                />
                                <label for="isCircular" class="ml-2 font-medium">
                                    Кольцевой маршрут
                                </label>
                            </div>
                            <small class="text-color-secondary">
                                Отметьте, если маршрут является кольцевым (возвращается к начальной остановке)
                            </small>
                        </div>
                    </div>
                    
                    <!-- Остановки маршрута -->
                    <div class="col-12">
                        <div class="flex justify-content-between align-items-center mb-3 mt-4">
                            <h3 class="text-lg font-medium m-0">Остановки маршрута</h3>
                            <Button 
                                label="Добавить остановку" 
                                icon="pi pi-plus" 
                                size="small"
                                @click="addStation"
                            />
                        </div>
                        
                        <DataTable 
                            :value="form.stations" 
                            editMode="row" 
                            v-model:editingRows="editingRows"
                            @row-edit-save="onRowEditSave"
                            @row-edit-cancel="onRowEditCancel"
                            responsiveLayout="scroll"
                            :pt="{
                                table: { style: 'min-width: 50rem' }
                            }"
                        >
                            <template #empty>
                                <div class="text-center p-4">
                                    <i class="pi pi-info-circle text-2xl text-color-secondary mb-2"></i>
                                    <p class="text-color-secondary">Остановки не добавлены</p>
                                    <small class="text-color-secondary">
                                        Нажмите "Добавить остановку" для добавления остановок в маршрут
                                    </small>
                                </div>
                            </template>
                            
                            <Column field="order" header="Порядок" style="width: 100px">
                                <template #body="{ data }">
                                    <span class="font-semibold">{{ data.order }}</span>
                                </template>
                            </Column>
                            
                            <Column field="name" header="Остановка" style="min-width: 300px">
                                <template #body="{ data }">
                                    {{ data.name }}
                                </template>
                                <template #editor="{ data, field }">
                                    <Dropdown 
                                        v-model="data.id"
                                        :options="stationOptions" 
                                        optionLabel="label" 
                                        optionValue="value"
                                        placeholder="Выберите остановку"
                                        class="w-full"
                                        @change="data.name = getStationName(data.id)"
                                    />
                                </template>
                            </Column>
                            
                            <Column header="Действия" style="width: 200px">
                                <template #body="{ data, index }">
                                    <div class="flex gap-1">
                                        <Button 
                                            icon="pi pi-arrow-up" 
                                            size="small" 
                                            text 
                                            :disabled="index === 0"
                                            @click="moveStationUp(index)"
                                            v-tooltip.top="'Переместить вверх'"
                                        />
                                        <Button 
                                            icon="pi pi-arrow-down" 
                                            size="small" 
                                            text 
                                            :disabled="index === form.stations.length - 1"
                                            @click="moveStationDown(index)"
                                            v-tooltip.top="'Переместить вниз'"
                                        />
                                        <Button 
                                            icon="pi pi-pencil" 
                                            size="small" 
                                            text 
                                            @click="editingRows[index] = true"
                                            v-tooltip.top="'Редактировать'"
                                        />
                                        <Button 
                                            icon="pi pi-trash" 
                                            size="small" 
                                            text 
                                            severity="danger"
                                            @click="removeStation(index)"
                                            v-tooltip.top="'Удалить'"
                                        />
                                    </div>
                                </template>
                                <template #editor="{ data, index }">
                                    <div class="flex gap-1">
                                        <Button 
                                            icon="pi pi-check" 
                                            size="small" 
                                            text 
                                            severity="success"
                                            @click="editingRows[index] = false"
                                            v-tooltip.top="'Сохранить'"
                                        />
                                        <Button 
                                            icon="pi pi-times" 
                                            size="small" 
                                            text 
                                            severity="secondary"
                                            @click="editingRows[index] = false"
                                            v-tooltip.top="'Отмена'"
                                        />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>
                    </div>
                </div>
                
                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных маршрута...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.route-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
