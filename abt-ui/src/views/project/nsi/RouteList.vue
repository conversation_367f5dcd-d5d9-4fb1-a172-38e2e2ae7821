<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { RouteService } from '@/service/RouteService';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const routes = ref([]);
const loading = ref(true);
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    number: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        number: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(() => {
    loadRoutes();
});

const loadRoutes = async () => {
    try {
        loading.value = true;
        const data = await RouteService.getRoutesByProjectWithStations(projectId);
        routes.value = data;
    } catch (error) {
        console.error('Ошибка загрузки маршрутов:', error);
        routes.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const refreshRoutes = () => {
    loadRoutes();
};

const createRoute = () => {
    router.push(`/pro/${projectId}/nsi/route/create`);
};

const editRoute = (routeItem) => {
    router.push(`/pro/${projectId}/nsi/route/${routeItem.id}/edit`);
};

const deleteRoute = async (routeItem) => {
    if (confirm(`Вы уверены, что хотите удалить маршрут "${routeItem.number} - ${routeItem.name}"?`)) {
        try {
            await RouteService.deleteRoute(routeItem.id);
            console.log('Маршрут удален:', routeItem.id);
            await loadRoutes();
        } catch (error) {
            console.error('Ошибка удаления маршрута:', error);
        }
    }
};

const importFromFile = () => {
    console.log('Загрузить маршруты из файла для проекта:', projectId);
};

const exportData = () => {
    console.log('Экспорт маршрутов проекта:', projectId);
};

const getCircularIcon = (isCircular) => {
    return isCircular ? 'pi pi-refresh' : 'pi pi-arrow-right';
};

const getCircularLabel = (isCircular) => {
    return isCircular ? 'Кольцевой' : 'Линейный';
};

const getStationsCount = (stations) => {
    return stations ? stations.length : 0;
};
</script>

<template>
    <div class="route-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Маршруты</h2>
            <div class="flex gap-2">
                <Button
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button
                    label="Создать"
                    icon="pi pi-plus"
                    @click="createRoute"
                />
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="routes"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['number', 'name']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Маршруты не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="number" header="Номер" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по номеру"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-semibold">{{ data.number }}</span>
                    </template>
                </Column>

                <Column field="name" header="Название" :sortable="true" style="min-width: 250px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по названию"
                        />
                    </template>
                </Column>

                <Column header="Тип маршрута" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i
                                :class="getCircularIcon(data.isCircular)"
                                :style="{ color: data.isCircular ? '#8b5cf6' : '#06b6d4' }"
                                class="mr-2"
                            ></i>
                            <span>{{ getCircularLabel(data.isCircular) }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Количество остановок" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-map-marker mr-2 text-color-secondary"></i>
                            <span>{{ getStationsCount(data.stations) }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editRoute(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteRoute(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.route-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}
</style>
