<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { StationService } from '@/service/StationService';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const stations = ref([]);
const loading = ref(true);
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    city: { value: null, matchMode: FilterMatchMode.CONTAINS },
    region: { value: null, matchMode: FilterMatchMode.CONTAINS }
});



const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        city: { value: null, matchMode: FilterMatchMode.CONTAINS },
        region: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(() => {
    loadStations();
});

const loadStations = async () => {
    try {
        loading.value = true;
        const data = await StationService.getStationsByProject(projectId);
        stations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки остановок:', error);
        stations.value = [];
    } finally {
        loading.value = false;
    }
};



const clearFilter = () => {
    initFilters();
};

const refreshStations = () => {
    loadStations();
};

const createStation = () => {
    router.push(`/pro/${projectId}/nsi/station/create`);
};

const editStation = (station) => {
    router.push(`/pro/${projectId}/nsi/station/${station.id}/edit`);
};

const deleteStation = async (station) => {
    if (confirm(`Вы уверены, что хотите удалить остановку "${station.name}"?`)) {
        try {
            await StationService.deleteStation(station.id);
            console.log('Остановка удалена:', station.id);
            // Перезагружаем список после удаления
            await loadStations();
        } catch (error) {
            console.error('Ошибка удаления остановки:', error);
            // Здесь можно показать уведомление об ошибке
        }
    }
};

const importFromFile = () => {
    console.log('Загрузить остановки из файла для проекта:', projectId);
    // Здесь будет логика импорта из файла
    // После импорта перезагружаем список
    // await StationService.importStations(projectId, file);
    // await loadStations();
};

const exportData = () => {
    console.log('Экспорт остановок проекта:', projectId);
    // Здесь будет логика экспорта данных
    // StationService.exportStations(projectId, stations.value);
};

const getCoordinatesIcon = (hasCoordinates) => {
    return hasCoordinates ? 'pi pi-check-circle' : 'pi pi-times-circle';
};

const getCoordinatesSeverity = (hasCoordinates) => {
    return hasCoordinates ? 'success' : 'danger';
};
</script>

<template>
    <div class="station-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Остановки</h2>
            <div class="flex gap-2">
                <Button
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button
                    label="Создать"
                    icon="pi pi-plus"
                    @click="createStation"
                />
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="stations"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['name', 'latinName', 'city', 'district', 'region', 'country']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Остановки не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="name" header="Наименование" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по наименованию"
                        />
                    </template>
                </Column>

                <Column field="latinName" header="Латинское наименование" :sortable="true" style="min-width: 200px">
                </Column>

                <Column field="city" header="Город" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по городу"
                        />
                    </template>
                </Column>

                <Column field="district" header="Район" :sortable="true" style="min-width: 150px">
                </Column>

                <Column field="region" header="Регион" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по региону"
                        />
                    </template>
                </Column>

                <Column field="country" header="Страна" :sortable="true" style="min-width: 120px">
                </Column>

                <Column header="Координаты" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i
                                :class="getCoordinatesIcon(data.hasCoordinates)"
                                :style="{ color: data.hasCoordinates ? '#22c55e' : '#ef4444' }"
                                class="mr-2"
                            ></i>
                            <span>{{ data.hasCoordinates ? 'Есть' : 'Нет' }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editStation(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteStation(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.station-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}
</style>
