<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { DriverService } from '@/service/DriverService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const driverId = route.params.driverId;
const isEdit = computed(() => !!driverId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);

// Форма данных
const form = ref({
    personnelNumber: '',
    fullName: '',
    pinCode: '',
    cardPan: '',
    organizationId: null,
    organizationName: ''
});

// Валидация
const errors = ref({});

onMounted(async () => {
    await loadOrganizations();
    if (isEdit.value) {
        await loadDriver();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data.filter(org => org.status === 'active').map(org => ({
            label: org.shortName,
            value: org.id,
            fullName: org.name
        }));
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const loadDriver = async () => {
    try {
        loading.value = true;
        const driver = await DriverService.getDriverById(driverId);
        if (driver) {
            form.value = { ...driver };
        }
    } catch (error) {
        console.error('Ошибка загрузки водителя:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};

    if (!form.value.personnelNumber.trim()) {
        errors.value.personnelNumber = 'Табельный номер обязателен для заполнения';
    }

    if (!form.value.fullName.trim()) {
        errors.value.fullName = 'ФИО обязательно для заполнения';
    }

    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна для выбора';
    }

    if (!form.value.pinCode.trim()) {
        errors.value.pinCode = 'Пин-код обязателен для заполнения';
    } else if (!/^\d{4}$/.test(form.value.pinCode)) {
        errors.value.pinCode = 'Пин-код должен состоять из 4 цифр';
    }

    if (!form.value.cardPan.trim()) {
        errors.value.cardPan = 'PAN служебной карты обязателен для заполнения';
    } else if (!/^\d{16}$/.test(form.value.cardPan.replace(/\s/g, ''))) {
        errors.value.cardPan = 'PAN должен состоять из 16 цифр';
    }

    return Object.keys(errors.value).length === 0;
};

const saveDriver = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        saving.value = true;

        // Очищаем PAN от пробелов перед сохранением
        const driverData = {
            ...form.value,
            cardPan: form.value.cardPan.replace(/\s/g, '')
        };

        if (isEdit.value) {
            await DriverService.updateDriver(driverId, driverData);
            console.log('Водитель обновлен:', driverData);
        } else {
            await DriverService.createDriver(projectCode, driverData);
            console.log('Водитель создан:', driverData);
        }

        router.push(`/pro/${projectCode}/nsi/driver`);

    } catch (error) {
        console.error('Ошибка сохранения водителя:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/nsi/driver`);
};

// Форматирование PAN карты
const formatCardPan = (value) => {
    // Удаляем все нецифровые символы
    const cleaned = value.replace(/\D/g, '');
    // Ограничиваем до 16 цифр
    const limited = cleaned.substring(0, 16);
    // Добавляем пробелы каждые 4 цифры
    return limited.replace(/(\d{4})(?=\d)/g, '$1 ');
};

const onCardPanInput = (event) => {
    const formatted = formatCardPan(event.target.value);
    form.value.cardPan = formatted;
    event.target.value = formatted;
};

// Форматирование пин-кода
const onPinCodeInput = (event) => {
    // Разрешаем только цифры и ограничиваем до 4 символов
    const cleaned = event.target.value.replace(/\D/g, '').substring(0, 4);
    form.value.pinCode = cleaned;
    event.target.value = cleaned;
};

// Генерация случайного пин-кода
const generatePinCode = () => {
    const pin = Math.floor(1000 + Math.random() * 9000).toString();
    form.value.pinCode = pin;
    delete errors.value.pinCode;
};

const onOrganizationChange = () => {
    const selectedOrg = organizations.value.find(org => org.value === form.value.organizationId);
    if (selectedOrg) {
        form.value.organizationName = selectedOrg.fullName;
    }
    delete errors.value.organizationId;
};
</script>

<template>
    <div class="driver-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование водителя' : 'Создание водителя' }}
            </h2>
            <Button
                label="Назад к списку"
                icon="pi pi-arrow-left"
                outlined
                @click="cancel"
            />
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveDriver">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="personnelNumber" class="font-medium">Табельный номер *</label>
                            <InputText
                                id="personnelNumber"
                                v-model="form.personnelNumber"
                                :class="{ 'p-invalid': errors.personnelNumber }"
                                placeholder="Например: D001, EMP123"
                                class="w-full"
                            />
                            <small v-if="errors.personnelNumber" class="p-error">{{ errors.personnelNumber }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="fullName" class="font-medium">ФИО *</label>
                            <InputText
                                id="fullName"
                                v-model="form.fullName"
                                :class="{ 'p-invalid': errors.fullName }"
                                placeholder="Введите полное ФИО"
                                class="w-full"
                            />
                            <small v-if="errors.fullName" class="p-error">{{ errors.fullName }}</small>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <Dropdown
                                id="organizationId"
                                v-model="form.organizationId"
                                :options="organizations"
                                optionLabel="label"
                                optionValue="value"
                                :class="{ 'p-invalid': errors.organizationId }"
                                placeholder="Выберите организацию"
                                class="w-full"
                                @change="onOrganizationChange"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                        </div>
                    </div>

                    <!-- Данные доступа -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Данные доступа</h3>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="pinCode" class="font-medium">Пин-код *</label>
                            <div class="p-inputgroup">
                                <InputText
                                    id="pinCode"
                                    v-model="form.pinCode"
                                    :class="{ 'p-invalid': errors.pinCode }"
                                    placeholder="4 цифры"
                                    maxlength="4"
                                    @input="onPinCodeInput"
                                />
                                <Button
                                    icon="pi pi-refresh"
                                    @click="generatePinCode"
                                    v-tooltip.top="'Сгенерировать случайный пин-код'"
                                />
                            </div>
                            <small v-if="errors.pinCode" class="p-error">{{ errors.pinCode }}</small>
                            <small v-else class="text-color-secondary">4 цифры для доступа к системе</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="cardPan" class="font-medium">PAN служебной карты *</label>
                            <InputText
                                id="cardPan"
                                v-model="form.cardPan"
                                :class="{ 'p-invalid': errors.cardPan }"
                                placeholder="1234 5678 9012 3456"
                                maxlength="19"
                                @input="onCardPanInput"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.cardPan" class="p-error">{{ errors.cardPan }}</small>
                            <small v-else class="text-color-secondary">16 цифр номера служебной карты</small>
                        </div>
                    </div>

                    <!-- Предварительный просмотр -->
                    <div class="col-12" v-if="form.fullName || form.personnelNumber">
                        <h3 class="text-lg font-medium mb-3 mt-4">Предварительный просмотр</h3>
                        <div class="surface-100 border-round p-3">
                            <div class="flex align-items-center">
                                <Avatar
                                    :label="form.fullName ? form.fullName.split(' ').map(n => n[0]).join('') : '?'"
                                    size="large"
                                    shape="circle"
                                    class="mr-3"
                                />
                                <div>
                                    <h4 class="m-0 mb-1">{{ form.fullName || 'Не указано' }}</h4>
                                    <p class="m-0 text-color-secondary mb-1">
                                        Табельный номер: {{ form.personnelNumber || 'Не указан' }}
                                    </p>
                                    <div class="flex align-items-center mb-2" v-if="form.organizationId">
                                        <i class="pi pi-building mr-1 text-color-secondary"></i>
                                        <span class="text-sm">
                                            {{ organizations.find(org => org.value === form.organizationId)?.label || 'Не указана' }}
                                        </span>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="flex align-items-center">
                                            <i class="pi pi-lock mr-1 text-color-secondary"></i>
                                            <span class="font-mono text-sm">
                                                {{ form.pinCode ? '****' : 'Не задан' }}
                                            </span>
                                        </div>
                                        <div class="flex align-items-center">
                                            <i class="pi pi-credit-card mr-1 text-color-secondary"></i>
                                            <span class="font-mono text-sm">
                                                {{ form.cardPan ? '**** **** **** ' + form.cardPan.slice(-4) : 'Не указан' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'"
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>

        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных водителя...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.driver-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.text-sm {
    font-size: 0.875rem;
}
</style>
