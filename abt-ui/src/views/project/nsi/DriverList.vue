<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { DriverService } from '@/service/DriverService';

const route = useRoute();
const router = useRouter();
const projectCode = route.params.code;

const drivers = ref([]);
const loading = ref(true);
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    personnelNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
    fullName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    cardPan: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        personnelNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
        fullName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        cardPan: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(() => {
    loadDrivers();
});

const loadDrivers = async () => {
    try {
        loading.value = true;
        const data = await DriverService.getDriversByProject(projectCode);
        drivers.value = data;
    } catch (error) {
        console.error('Ошибка загрузки водителей:', error);
        drivers.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const refreshDrivers = () => {
    loadDrivers();
};

const createDriver = () => {
    router.push(`/pro/${projectCode}/nsi/driver/create`);
};

const editDriver = (driver) => {
    router.push(`/pro/${projectCode}/nsi/driver/${driver.id}/edit`);
};

const deleteDriver = async (driver) => {
    if (confirm(`Вы уверены, что хотите удалить водителя "${driver.fullName}"?`)) {
        try {
            await DriverService.deleteDriver(driver.id);
            console.log('Водитель удален:', driver.id);
            await loadDrivers();
        } catch (error) {
            console.error('Ошибка удаления водителя:', error);
        }
    }
};

const importFromFile = () => {
    console.log('Загрузить водителей из файла для проекта:', projectCode);
};

const exportData = () => {
    console.log('Экспорт водителей проекта:', projectCode);
};

const maskCardPan = (pan) => {
    if (!pan || pan.length < 4) return pan;
    return '**** **** **** ' + pan.slice(-4);
};

const maskPinCode = (pin) => {
    return pin ? '****' : '';
};
</script>

<template>
    <div class="driver-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Водители</h2>
            <div class="flex gap-2">
                <Button
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button
                    label="Создать"
                    icon="pi pi-plus"
                    @click="createDriver"
                />
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="drivers"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['personnelNumber', 'fullName', 'organizationName', 'cardPan']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Водители не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="personnelNumber" header="Табельный номер" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по номеру"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-semibold">{{ data.personnelNumber }}</span>
                    </template>
                </Column>

                <Column field="fullName" header="ФИО" :sortable="true" style="min-width: 250px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по ФИО"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <Avatar
                                :label="data.fullName.split(' ').map(n => n[0]).join('')"
                                size="normal"
                                shape="circle"
                                class="mr-2"
                            />
                            <span>{{ data.fullName }}</span>
                        </div>
                    </template>
                </Column>

                <Column field="organizationName" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по организации"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Пин-код" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-lock mr-2 text-color-secondary"></i>
                            <span class="font-mono">{{ maskPinCode(data.pinCode) }}</span>
                        </div>
                    </template>
                </Column>

                <Column field="cardPan" header="PAN служебной карты" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по PAN"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-credit-card mr-2 text-color-secondary"></i>
                            <span class="font-mono">{{ maskCardPan(data.cardPan) }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editDriver(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteDriver(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.driver-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
