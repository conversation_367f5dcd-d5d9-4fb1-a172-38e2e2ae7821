<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { FilterMatchMode } from '@primevue/core/api';
import { TariffService } from '@/service/TariffService';

const router = useRouter();

const tariffs = ref([]);
const loading = ref(true);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    code: { value: null, matchMode: FilterMatchMode.CONTAINS },
    transportType: { value: null, matchMode: FilterMatchMode.EQUALS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const transportTypes = ref([
    { label: 'Автобус', value: 'bus' },
    { label: 'Метро', value: 'metro' },
    { label: 'Троллейбус', value: 'trolley' },
    { label: 'Все виды', value: 'all' }
]);

const statuses = ref([
    { label: 'Активный', value: 'active' },
    { label: 'Черновик', value: 'draft' },
    { label: 'Архивный', value: 'archived' }
]);

onMounted(() => {
    loadTariffs();
});

const loadTariffs = async () => {
    try {
        loading.value = true;
        const data = await TariffService.getTariffs();
        tariffs.value = data;
    } catch (error) {
        console.error('Ошибка загрузки тарифов:', error);
        tariffs.value = [];
    } finally {
        loading.value = false;
    }
};

const createTariff = () => {
    router.push('/pasiv/tariffs/create');
};

const editTariff = (tariff) => {
    router.push(`/pasiv/tariffs/${tariff.id}/edit`);
};

const deleteTariff = async (tariff) => {
    if (confirm(`Вы уверены, что хотите удалить тариф "${tariff.name}"?`)) {
        try {
            await TariffService.deleteTariff(tariff.id);
            console.log('Тариф удален:', tariff.id);
            await loadTariffs();
        } catch (error) {
            console.error('Ошибка удаления тарифа:', error);
        }
    }
};

const activateTariff = async (tariff) => {
    if (confirm(`Вы уверены, что хотите активировать тариф "${tariff.name}"?`)) {
        try {
            const result = await TariffService.activateTariff(tariff.id);
            console.log('Тариф активирован:', result.message);
            await loadTariffs();
        } catch (error) {
            console.error('Ошибка активации тарифа:', error);
        }
    }
};

const deactivateTariff = async (tariff) => {
    if (confirm(`Вы уверены, что хотите деактивировать тариф "${tariff.name}"?`)) {
        try {
            const result = await TariffService.deactivateTariff(tariff.id);
            console.log('Тариф деактивирован:', result.message);
            await loadTariffs();
        } catch (error) {
            console.error('Ошибка деактивации тарифа:', error);
        }
    }
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'warning';
        case 'archived': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'archived': return 'Архивный';
        default: return 'Неизвестно';
    }
};

const getTransportTypeLabel = (type) => {
    switch (type) {
        case 'bus': return 'Автобус';
        case 'metro': return 'Метро';
        case 'trolley': return 'Троллейбус';
        case 'all': return 'Все виды';
        default: return type;
    }
};

const getFareTypeLabel = (type) => {
    switch (type) {
        case 'single': return 'Разовый';
        case 'subscription': return 'Абонемент';
        case 'daily': return 'Суточный';
        case 'weekly': return 'Недельный';
        case 'monthly': return 'Месячный';
        default: return type;
    }
};

const clearFilter = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        code: { value: null, matchMode: FilterMatchMode.CONTAINS },
        transportType: { value: null, matchMode: FilterMatchMode.EQUALS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};
</script>

<template>
    <div class="tariff-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Тарифы</h1>
            <Button 
                label="Создать тариф" 
                icon="pi pi-plus" 
                @click="createTariff"
            />
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button 
                        type="button" 
                        icon="pi pi-filter-slash" 
                        label="Очистить" 
                        outlined 
                        @click="clearFilter"
                    />
                </div>
                <IconField>
                    <InputIcon>
                        <i class="pi pi-search" />
                    </InputIcon>
                    <InputText 
                        v-model="filters.global.value" 
                        placeholder="Поиск по всем полям" 
                    />
                </IconField>
            </div>
            
            <DataTable
                :value="tariffs"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['name', 'code', 'description', 'organizationName']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-dollar text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Тарифы не найдены</p>
                        <Button 
                            label="Создать первый тариф" 
                            icon="pi pi-plus" 
                            class="mt-3"
                            @click="createTariff"
                        />
                    </div>
                </template>
                
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="name" header="Наименование" :sortable="true" style="min-width: 250px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по наименованию" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <small class="text-color-secondary">{{ data.description }}</small>
                        </div>
                    </template>
                </Column>
                
                <Column field="code" header="Код" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по коду" 
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.code }}</span>
                    </template>
                </Column>
                
                <Column field="transportType" header="Тип транспорта" :sortable="true" style="min-width: 130px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="transportTypes" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите тип" 
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getTransportTypeLabel(data.transportType)" 
                            severity="info"
                        />
                    </template>
                </Column>
                
                <Column field="fareType" header="Тип тарифа" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <span>{{ getFareTypeLabel(data.fareType) }}</span>
                    </template>
                </Column>
                
                <Column field="amount" header="Стоимость" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.amount) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true" style="min-width: 100px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="statuses" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите статус" 
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column header="Период действия" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div>{{ formatDate(data.validFrom) }} -</div>
                            <div>{{ formatDate(data.validTo) }}</div>
                        </div>
                    </template>
                </Column>
                
                <Column field="organizationName" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по организации" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editTariff(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                v-if="data.status === 'draft'"
                                icon="pi pi-check" 
                                size="small" 
                                text 
                                severity="success"
                                @click="activateTariff(data)"
                                v-tooltip.top="'Активировать'"
                            />
                            <Button 
                                v-if="data.status === 'active'"
                                icon="pi pi-pause" 
                                size="small" 
                                text 
                                severity="warning"
                                @click="deactivateTariff(data)"
                                v-tooltip.top="'Деактивировать'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deleteTariff(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.tariff-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
