<script setup>
import { ref } from 'vue';

const billingRecords = ref([
    {
        id: 1,
        organizationName: 'ООО "Городской транспорт"',
        period: '2024-01',
        totalAmount: 2500000.00,
        commission: 62500.00,
        status: 'completed',
        generatedDate: '2024-02-01T00:00:00Z'
    },
    {
        id: 2,
        organizationName: 'АО "Метрополитен"',
        period: '2024-01',
        totalAmount: 4500000.00,
        commission: 126000.00,
        status: 'processing',
        generatedDate: '2024-02-01T00:00:00Z'
    },
    {
        id: 3,
        organizationName: 'ООО "Автобусный парк №1"',
        period: '2024-01',
        totalAmount: 1200000.00,
        commission: 26400.00,
        status: 'draft',
        generatedDate: null
    }
]);

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не сформирован';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed': return 'success';
        case 'processing': return 'warning';
        case 'draft': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed': return 'Завершен';
        case 'processing': return 'Обработка';
        case 'draft': return 'Черновик';
        default: return status;
    }
};
</script>

<template>
    <div class="billing-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Биллинг</h1>
            <div class="flex gap-2">
                <Button 
                    label="Сформировать счета" 
                    icon="pi pi-plus" 
                />
                <Button 
                    label="Экспорт" 
                    icon="pi pi-download" 
                    outlined
                />
            </div>
        </div>

        <div class="card">
            <DataTable :value="billingRecords" responsiveLayout="scroll">
                <Column field="organizationName" header="Организация" :sortable="true">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column field="period" header="Период" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.period }}</span>
                    </template>
                </Column>
                
                <Column field="totalAmount" header="Общая сумма" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.totalAmount) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="commission" header="Комиссия" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.commission) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="generatedDate" header="Дата формирования" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.generatedDate) }}
                    </template>
                </Column>
                
                <Column header="Действия">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-eye" 
                                size="small" 
                                text 
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button 
                                icon="pi pi-download" 
                                size="small" 
                                text 
                                v-tooltip.top="'Скачать'"
                                :disabled="data.status === 'draft'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.billing-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
