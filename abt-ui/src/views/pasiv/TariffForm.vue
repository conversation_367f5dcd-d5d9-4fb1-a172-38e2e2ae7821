<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { TariffService } from '@/service/TariffService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const tariffId = route.params.tariffId;
const isEdit = computed(() => !!tariffId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);

const form = ref({
    name: '',
    code: '',
    transportType: 'bus',
    fareType: 'single',
    amount: 0,
    currency: 'RUB',
    validFrom: '',
    validTo: '',
    organizationId: null,
    description: '',
    zones: [],
    discountCategories: []
});

const errors = ref({});

const transportTypes = ref([
    { label: 'Автобус', value: 'bus' },
    { label: 'Метро', value: 'metro' },
    { label: 'Троллейбус', value: 'trolley' },
    { label: 'Все виды', value: 'all' }
]);

const fareTypes = ref([
    { label: 'Разовый', value: 'single' },
    { label: 'Абонемент', value: 'subscription' },
    { label: 'Суточный', value: 'daily' },
    { label: 'Недельный', value: 'weekly' },
    { label: 'Месячный', value: 'monthly' }
]);

const availableZones = ref([
    { label: 'Зона A', value: 'A' },
    { label: 'Зона B', value: 'B' },
    { label: 'Зона C', value: 'C' },
    { label: 'Метро', value: 'METRO' }
]);

const discountOptions = ref([
    { label: 'Студенты', value: 'student' },
    { label: 'Пенсионеры', value: 'pension' },
    { label: 'Школьники', value: 'school' },
    { label: 'Льготники', value: 'benefit' }
]);

onMounted(async () => {
    await loadOrganizations();
    if (isEdit.value) {
        await loadTariff();
    } else {
        // Установим дефолтные даты
        const now = new Date();
        const nextYear = new Date(now.getFullYear() + 1, 11, 31);
        form.value.validFrom = now.toISOString().split('T')[0];
        form.value.validTo = nextYear.toISOString().split('T')[0];
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
    }
};

const loadTariff = async () => {
    try {
        loading.value = true;
        const tariff = await TariffService.getTariffById(tariffId);
        if (tariff) {
            form.value = { 
                ...tariff,
                validFrom: tariff.validFrom ? tariff.validFrom.split('T')[0] : '',
                validTo: tariff.validTo ? tariff.validTo.split('T')[0] : ''
            };
        }
    } catch (error) {
        console.error('Ошибка загрузки тарифа:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование обязательно';
    }
    
    if (!form.value.code.trim()) {
        errors.value.code = 'Код обязателен';
    }
    
    if (form.value.amount < 0) {
        errors.value.amount = 'Сумма не может быть отрицательной';
    }
    
    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна';
    }
    
    if (!form.value.validFrom) {
        errors.value.validFrom = 'Дата начала действия обязательна';
    }
    
    if (!form.value.validTo) {
        errors.value.validTo = 'Дата окончания действия обязательна';
    }
    
    if (form.value.validFrom && form.value.validTo && form.value.validFrom >= form.value.validTo) {
        errors.value.validTo = 'Дата окончания должна быть позже даты начала';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveTariff = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        const tariffData = {
            ...form.value,
            validFrom: form.value.validFrom + 'T00:00:00Z',
            validTo: form.value.validTo + 'T23:59:59Z'
        };
        
        if (isEdit.value) {
            await TariffService.updateTariff(tariffId, tariffData);
        } else {
            await TariffService.createTariff(tariffData);
        }
        
        router.push('/pasiv/tariffs');
        
    } catch (error) {
        console.error('Ошибка сохранения тарифа:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/pasiv/tariffs');
};

const generateCode = () => {
    if (form.value.name) {
        const code = form.value.name
            .toUpperCase()
            .replace(/[^А-ЯA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 20);
        form.value.code = code;
    }
};
</script>

<template>
    <div class="tariff-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование тарифа' : 'Создание тарифа' }}
            </h1>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveTariff">
                <div class="grid">
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-8">
                        <div class="field">
                            <label for="name" class="font-medium">Наименование *</label>
                            <InputText 
                                id="name"
                                v-model="form.name" 
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="Базовый тариф автобус"
                                class="w-full"
                                @input="generateCode"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="code" class="font-medium">Код *</label>
                            <InputText 
                                id="code"
                                v-model="form.code" 
                                :class="{ 'p-invalid': errors.code }"
                                placeholder="BUS_BASE"
                                class="w-full"
                            />
                            <small v-if="errors.code" class="p-error">{{ errors.code }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="description" class="font-medium">Описание</label>
                            <Textarea 
                                id="description"
                                v-model="form.description" 
                                placeholder="Описание тарифа"
                                class="w-full"
                                rows="3"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="transportType" class="font-medium">Тип транспорта *</label>
                            <Dropdown 
                                id="transportType"
                                v-model="form.transportType" 
                                :options="transportTypes" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите тип транспорта"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="fareType" class="font-medium">Тип тарифа *</label>
                            <Dropdown 
                                id="fareType"
                                v-model="form.fareType" 
                                :options="fareTypes" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите тип тарифа"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="amount" class="font-medium">Стоимость *</label>
                            <InputNumber 
                                id="amount"
                                v-model="form.amount" 
                                :class="{ 'p-invalid': errors.amount }"
                                mode="currency" 
                                currency="RUB" 
                                locale="ru-RU"
                                class="w-full"
                            />
                            <small v-if="errors.amount" class="p-error">{{ errors.amount }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <Dropdown 
                                id="organizationId"
                                v-model="form.organizationId" 
                                :options="organizations" 
                                optionLabel="name" 
                                optionValue="id"
                                placeholder="Выберите организацию"
                                :class="{ 'p-invalid': errors.organizationId }"
                                class="w-full"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="validFrom" class="font-medium">Дата начала действия *</label>
                            <Calendar 
                                id="validFrom"
                                v-model="form.validFrom" 
                                :class="{ 'p-invalid': errors.validFrom }"
                                dateFormat="dd.mm.yy"
                                class="w-full"
                            />
                            <small v-if="errors.validFrom" class="p-error">{{ errors.validFrom }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="validTo" class="font-medium">Дата окончания действия *</label>
                            <Calendar 
                                id="validTo"
                                v-model="form.validTo" 
                                :class="{ 'p-invalid': errors.validTo }"
                                dateFormat="dd.mm.yy"
                                class="w-full"
                            />
                            <small v-if="errors.validTo" class="p-error">{{ errors.validTo }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="zones" class="font-medium">Зоны действия</label>
                            <MultiSelect 
                                id="zones"
                                v-model="form.zones" 
                                :options="availableZones" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите зоны"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="discountCategories" class="font-medium">Льготные категории</label>
                            <MultiSelect 
                                id="discountCategories"
                                v-model="form.discountCategories" 
                                :options="discountOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите категории"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных тарифа...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.tariff-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
