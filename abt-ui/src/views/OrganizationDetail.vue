<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();
const organizationId = route.params.organizationId;

const loading = ref(true);
const organization = ref(null);

onMounted(() => {
    loadOrganization();
});

const loadOrganization = async () => {
    try {
        loading.value = true;
        const data = await OrganizationService.getOrganizationById(organizationId);
        organization.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организации:', error);
    } finally {
        loading.value = false;
    }
};

const goBack = () => {
    router.push('/organizations');
};

const editOrganization = () => {
    router.push(`/organizations/${organizationId}/edit`);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'inactive': return 'secondary';
        case 'suspended': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активная';
        case 'inactive': return 'Неактивная';
        case 'suspended': return 'Приостановлена';
        default: return 'Неизвестно';
    }
};

const getSyncStatusSeverity = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'success';
        case 'pending': return 'warning';
        case 'error': return 'danger';
        case 'never': return 'secondary';
        default: return 'secondary';
    }
};

const getSyncStatusLabel = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'Синхронизирована';
        case 'pending': return 'Ожидает синхронизации';
        case 'error': return 'Ошибка синхронизации';
        case 'never': return 'Не синхронизировалась';
        default: return 'Неизвестно';
    }
};
</script>

<template>
    <div class="organization-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Детальная информация об организации</h1>
            <div class="flex gap-2">
                <Button 
                    label="Назад к списку" 
                    icon="pi pi-arrow-left" 
                    outlined 
                    @click="goBack"
                />
                <Button 
                    v-if="organization"
                    label="Редактировать" 
                    icon="pi pi-pencil" 
                    @click="editOrganization"
                />
            </div>
        </div>

        <!-- Загрузка -->
        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных организации...</p>
            </div>
        </div>

        <!-- Организация не найдена -->
        <div v-else-if="!organization" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Организация не найдена</h3>
                <p class="text-color-secondary mb-4">
                    Организация с указанным идентификатором не существует или была удалена.
                </p>
                <Button 
                    label="Вернуться к списку" 
                    icon="pi pi-arrow-left" 
                    @click="goBack"
                />
            </div>
        </div>

        <!-- Информация об организации -->
        <div v-else>
            <!-- Основная карточка -->
            <div class="card mb-4">
                <div class="flex align-items-start justify-content-between mb-4">
                    <div class="flex align-items-center">
                        <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-building text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <div class="flex align-items-center mb-2">
                                <Tag 
                                    :value="organization.type === 'organization' ? 'Организация' : 'ИП'" 
                                    :severity="organization.type === 'organization' ? 'info' : 'warning'"
                                    class="mr-2"
                                />
                                <span class="text-xl font-bold">{{ organization.ownershipForm }}</span>
                            </div>
                            <h2 class="text-2xl font-bold m-0 mb-1">{{ organization.name }}</h2>
                            <p class="text-color-secondary m-0 mb-2">{{ organization.fullName }}</p>
                            <div class="flex align-items-center gap-2">
                                <Tag 
                                    :value="getStatusLabel(organization.status)" 
                                    :severity="getStatusSeverity(organization.status)" 
                                />
                                <Tag 
                                    :value="getSyncStatusLabel(organization.syncStatus)" 
                                    :severity="getSyncStatusSeverity(organization.syncStatus)" 
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <Divider />

                <!-- Основные реквизиты -->
                <h3 class="text-lg font-semibold mb-3">Основные реквизиты</h3>
                <div class="grid">
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">ИНН</label>
                            <p class="m-0 font-mono text-lg">{{ organization.inn }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">
                                {{ organization.type === 'organization' ? 'ОГРН' : 'ОГРНИП' }}
                            </label>
                            <p class="m-0 font-mono">{{ organization.ogrn }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4" v-if="organization.kpp">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">КПП</label>
                            <p class="m-0 font-mono">{{ organization.kpp }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">ОКПО</label>
                            <p class="m-0 font-mono">{{ organization.okpo }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">ОКТМО</label>
                            <p class="m-0 font-mono">{{ organization.oktmo }}</p>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">ОКВЭД</label>
                            <p class="m-0">{{ organization.okved }}</p>
                        </div>
                    </div>
                </div>

                <Divider />

                <!-- Адреса -->
                <h3 class="text-lg font-semibold mb-3">Адреса</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Юридический адрес</label>
                            <p class="m-0">{{ organization.legalAddress }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Фактический адрес</label>
                            <p class="m-0">{{ organization.actualAddress }}</p>
                        </div>
                    </div>
                </div>

                <Divider />

                <!-- Руководство -->
                <h3 class="text-lg font-semibold mb-3">Руководство и контакты</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Действует на основании</label>
                            <p class="m-0">{{ organization.basedOn }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6" v-if="organization.generalDirector">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Генеральный директор</label>
                            <p class="m-0">{{ organization.generalDirector }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Ответственный за подпись</label>
                            <p class="m-0">{{ organization.responsibleForSignature }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Электронная почта</label>
                            <p class="m-0">
                                <a :href="`mailto:${organization.email}`" class="text-primary">{{ organization.email }}</a>
                            </p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Контактный телефон</label>
                            <p class="m-0">
                                <a :href="`tel:${organization.phone}`" class="text-primary">{{ organization.phone }}</a>
                            </p>
                        </div>
                    </div>
                </div>

                <Divider />

                <!-- Системная информация -->
                <h3 class="text-lg font-semibold mb-3">Системная информация</h3>
                <div class="grid">
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Дата создания</label>
                            <p class="m-0">{{ formatDate(organization.createdDate) }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4" v-if="organization.lastSyncDate">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Последняя синхронизация</label>
                            <p class="m-0">{{ formatDate(organization.lastSyncDate) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.organization-detail {
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.text-blue-600 {
    color: #2563eb;
}
</style>
