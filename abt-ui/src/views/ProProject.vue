<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';

const route = useRoute();
const router = useRouter();
const projectCode = route.params.code;

// Статистика проекта
const projectStats = ref({
    totalEntities: 0,
    activeEntities: 0
});

const dashboardStats = ref({
    organizations: 8,
    drivers: 12,
    vehicles: 10,
    routes: 8,
    stations: 12
});

const organizationStats = ref([
    { name: 'Городской транспорт', count: 3, color: '#3b82f6' },
    { name: 'Метрополитен', count: 2, color: '#10b981' },
    { name: 'Автобусный парк №1', count: 2, color: '#f59e0b' },
    { name: 'Мосгортранс', count: 2, color: '#8b5cf6' },
    { name: 'Другие', count: 3, color: '#6b7280' }
]);

const recentActivities = ref([
    {
        title: 'Добавлен новый водитель',
        description: 'Семенов Роман Олегович добавлен в систему',
        time: '2 часа назад',
        icon: 'pi pi-user-plus',
        color: '#10b981'
    },
    {
        title: 'Обновлен маршрут 102К',
        description: 'Изменен список остановок кольцевого маршрута',
        time: '4 часа назад',
        icon: 'pi pi-route',
        color: '#3b82f6'
    },
    {
        title: 'Транспорт отправлен на ТО',
        description: 'НефАЗ-5299 (М345НО777) отправлен на техобслуживание',
        time: '6 часов назад',
        icon: 'pi pi-wrench',
        color: '#f59e0b'
    },
    {
        title: 'Добавлена новая остановка',
        description: 'Площадь Победы добавлена в справочник',
        time: '1 день назад',
        icon: 'pi pi-map-marker',
        color: '#8b5cf6'
    }
]);

onMounted(() => {
    calculateStats();
});

const calculateStats = () => {
    const total = dashboardStats.value.organizations + dashboardStats.value.drivers +
                  dashboardStats.value.vehicles + dashboardStats.value.routes +
                  dashboardStats.value.stations;
    projectStats.value.totalEntities = total;
    projectStats.value.activeEntities = Math.floor(total * 0.85); // 85% активных
};

const formatDate = (date) => {
    return date.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const navigateTo = (path) => {
    router.push(`/pro/${projectCode}/nsi/${path}`);
};

const openProjectSettings = () => {
    console.log('Открыть настройки проекта');
};

const toggleNavigation = () => {
    console.log('Переключить навигацию');
};

const menuItems = ref([
    {
        label: 'Общая информация',
        icon: 'pi pi-info-circle',
        command: () => router.push(`/pro/${projectCode}/info`)
    },
    {
        label: 'НСИ',
        icon: 'pi pi-fw pi-table',
        items: [
            {
                label: 'Остановки',
                icon: 'pi pi-fw pi-map-marker',
                command: () => router.push(`/pro/${projectCode}/nsi/station`)
            },
            {
                label: 'Маршруты',
                icon: 'pi pi-fw pi-route',
                command: () => router.push(`/pro/${projectCode}/nsi/route`)
            },
            {
                label: 'Водители',
                icon: 'pi pi-fw pi-user',
                command: () => router.push(`/pro/${projectCode}/nsi/driver`)
            },
            {
                label: 'Транспортные средства',
                icon: 'pi pi-fw pi-car',
                command: () => router.push(`/pro/${projectCode}/nsi/vehicle`)
            },
            {
                label: 'Тарифы',
                icon: 'pi pi-fw pi-dollar',
                command: () => router.push(`/pro/${projectCode}/nsi/tariff`)
            }
        ]
    },
    {
        label: 'Финансы',
        icon: 'pi pi-fw pi-money-bill',
        items: [
            {
                label: 'Договор проекта',
                icon: 'pi pi-fw pi-file-edit',
                command: () => router.push(`/pro/${projectCode}/finance/contract`)
            },
            {
                label: 'Способы оплаты',
                icon: 'pi pi-fw pi-credit-card',
                command: () => router.push(`/pro/${projectCode}/finance/payment-methods`)
            },
            {
                label: 'Движение средств',
                icon: 'pi pi-fw pi-chart-line',
                command: () => router.push(`/pro/${projectCode}/finance/cash-flow`)
            }
        ]
    }
]);

</script>

<template>
    <div class="project-container">
        <!-- Заголовок проекта -->
        <div class="project-header mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-round">
            <div class="flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-3xl font-bold text-primary m-0 mb-2">СберТройка ПРО</h1>
                    <p class="text-lg text-color-secondary m-0">Проект: {{ projectCode }}</p>
                    <div class="flex align-items-center mt-2">
                        <Tag value="Активный" severity="success" class="mr-2" />
                        <span class="text-sm text-color-secondary">Последнее обновление: {{ formatDate(new Date()) }}</span>
                    </div>
                </div>
                <div class="flex align-items-center gap-3">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary">{{ projectStats.totalEntities }}</div>
                        <div class="text-sm text-color-secondary">Всего записей</div>
                    </div>
                    <Divider layout="vertical" />
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-500">{{ projectStats.activeEntities }}</div>
                        <div class="text-sm text-color-secondary">Активных</div>
                    </div>
                    <Divider layout="vertical" />
                    <Button
                        icon="pi pi-cog"
                        label="Настройки"
                        outlined
                        size="small"
                        @click="openProjectSettings"
                    />
                </div>
            </div>
        </div>

        <Splitter style="height: calc(100vh - 140px)">
            <SplitterPanel :size="20" :minSize="15">
                <div class="navigation-panel h-full">
                    <div class="p-3">
                        <div class="flex align-items-center justify-content-between mb-3">
                            <h3 class="text-lg font-semibold m-0">Навигация</h3>
                            <Button
                                icon="pi pi-bars"
                                text
                                size="small"
                                @click="toggleNavigation"
                                v-tooltip.right="'Свернуть меню'"
                            />
                        </div>
                        <Menu :model="menuItems" class="w-full navigation-menu" />
                    </div>
                </div>
            </SplitterPanel>

            <SplitterPanel :size="80">
                <div class="content-panel h-full overflow-hidden">
                    <router-view v-if="$route.path !== `/pro/${projectCode}`" />
                    <div v-else class="project-dashboard p-4">
                        <!-- Дашборд проекта -->
                        <div class="grid">
                            <!-- Карточки статистики -->
                            <div class="col-12 lg:col-3 md:col-6">
                                <div class="card stats-card bg-blue-50 border-blue-200">
                                    <div class="flex align-items-center">
                                        <div class="flex-1">
                                            <div class="text-2xl font-bold text-blue-600">{{ dashboardStats.organizations }}</div>
                                            <div class="text-blue-700 font-medium">Организации</div>
                                        </div>
                                        <div class="w-3rem h-3rem bg-blue-100 border-round flex align-items-center justify-content-center">
                                            <i class="pi pi-building text-blue-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 lg:col-3 md:col-6">
                                <div class="card stats-card bg-green-50 border-green-200">
                                    <div class="flex align-items-center">
                                        <div class="flex-1">
                                            <div class="text-2xl font-bold text-green-600">{{ dashboardStats.drivers }}</div>
                                            <div class="text-green-700 font-medium">Водители</div>
                                        </div>
                                        <div class="w-3rem h-3rem bg-green-100 border-round flex align-items-center justify-content-center">
                                            <i class="pi pi-user text-green-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 lg:col-3 md:col-6">
                                <div class="card stats-card bg-purple-50 border-purple-200">
                                    <div class="flex align-items-center">
                                        <div class="flex-1">
                                            <div class="text-2xl font-bold text-purple-600">{{ dashboardStats.vehicles }}</div>
                                            <div class="text-purple-700 font-medium">Транспорт</div>
                                        </div>
                                        <div class="w-3rem h-3rem bg-purple-100 border-round flex align-items-center justify-content-center">
                                            <i class="pi pi-car text-purple-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 lg:col-3 md:col-6">
                                <div class="card stats-card bg-orange-50 border-orange-200">
                                    <div class="flex align-items-center">
                                        <div class="flex-1">
                                            <div class="text-2xl font-bold text-orange-600">{{ dashboardStats.routes }}</div>
                                            <div class="text-orange-700 font-medium">Маршруты</div>
                                        </div>
                                        <div class="w-3rem h-3rem bg-orange-100 border-round flex align-items-center justify-content-center">
                                            <i class="pi pi-route text-orange-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Быстрые действия -->
                            <div class="col-12">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Быстрые действия</h3>
                                    <div class="grid">
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Добавить водителя"
                                                icon="pi pi-user-plus"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('driver/create')"
                                            />
                                        </div>
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Добавить транспорт"
                                                icon="pi pi-plus"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('vehicle/create')"
                                            />
                                        </div>
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Создать маршрут"
                                                icon="pi pi-map"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('route/create')"
                                            />
                                        </div>
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Добавить остановку"
                                                icon="pi pi-map-marker"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('station/create')"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Последние изменения -->
                            <div class="col-12 lg:col-8">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Последние изменения</h3>
                                    <Timeline :value="recentActivities" class="w-full">
                                        <template #content="{ item }">
                                            <div class="flex align-items-center">
                                                <i :class="item.icon" :style="{ color: item.color }" class="mr-3"></i>
                                                <div class="flex-1">
                                                    <div class="font-medium">{{ item.title }}</div>
                                                    <div class="text-sm text-color-secondary">{{ item.description }}</div>
                                                </div>
                                                <small class="text-color-secondary">{{ item.time }}</small>
                                            </div>
                                        </template>
                                    </Timeline>
                                </div>
                            </div>

                            <!-- Статистика по организациям -->
                            <div class="col-12 lg:col-4">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Распределение по организациям</h3>
                                    <div class="space-y-3">
                                        <div v-for="org in organizationStats" :key="org.name" class="flex align-items-center justify-content-between mb-3">
                                            <div class="flex align-items-center">
                                                <div class="w-1rem h-1rem border-round mr-2" :style="{ backgroundColor: org.color }"></div>
                                                <span class="text-sm">{{ org.name }}</span>
                                            </div>
                                            <span class="font-semibold">{{ org.count }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </SplitterPanel>
        </Splitter>
    </div>
</template>

<style scoped>
.project-container {
    height: 100vh;
    margin: 0;
    padding: 1rem;
    background: #f8fafc;
}

.project-header {
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
    border: 1px solid #bfdbfe;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navigation-panel {
    background: white;
    border-right: 1px solid #e5e7eb;
}

.content-panel {
    background: white;
}

.stats-card {
    border: 1px solid;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.project-dashboard {
    height: 100%;
    overflow-y: auto;
}

:deep(.p-splitter) {
    height: 100% !important;
    border: none;
}

:deep(.p-splitter-panel) {
    overflow: auto;
}

:deep(.navigation-menu .p-menuitem-link) {
    border-radius: 6px;
    margin-bottom: 2px;
}

:deep(.navigation-menu .p-menuitem-link:hover) {
    background: #f3f4f6;
}

:deep(.p-timeline .p-timeline-event-content) {
    padding: 0.5rem 0;
}

:deep(.p-timeline .p-timeline-event-marker) {
    border: 2px solid #e5e7eb;
    background: white;
}

.bg-gradient-to-r {
    background: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-50 {
    --tw-gradient-from: #eff6ff;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(239, 246, 255, 0));
}

.to-indigo-50 {
    --tw-gradient-to: #eef2ff;
}

.border-blue-200 {
    border-color: #bfdbfe;
}

.border-green-200 {
    border-color: #bbf7d0;
}

.border-purple-200 {
    border-color: #e9d5ff;
}

.border-orange-200 {
    border-color: #fed7aa;
}

.bg-blue-50 {
    background-color: #eff6ff;
}

.bg-green-50 {
    background-color: #f0fdf4;
}

.bg-purple-50 {
    background-color: #faf5ff;
}

.bg-orange-50 {
    background-color: #fff7ed;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.bg-green-100 {
    background-color: #dcfce7;
}

.bg-purple-100 {
    background-color: #f3e8ff;
}

.bg-orange-100 {
    background-color: #ffedd5;
}

.text-blue-600 {
    color: #2563eb;
}

.text-blue-700 {
    color: #1d4ed8;
}

.text-green-600 {
    color: #16a34a;
}

.text-green-700 {
    color: #15803d;
}

.text-purple-600 {
    color: #9333ea;
}

.text-purple-700 {
    color: #7c3aed;
}

.text-orange-600 {
    color: #ea580c;
}

.text-orange-700 {
    color: #c2410c;
}

.text-green-500 {
    color: #22c55e;
}
</style>
