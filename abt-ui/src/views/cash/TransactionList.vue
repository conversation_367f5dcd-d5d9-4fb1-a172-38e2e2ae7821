<script setup>
import { ref, onMounted } from 'vue';
import { ProcessingService } from '@/service/ProcessingService';

const transactions = ref([]);
const loading = ref(true);

onMounted(async () => {
    try {
        const data = await ProcessingService.getTransactionsByType('cash');
        transactions.value = data;
    } catch (error) {
        console.error('Ошибка загрузки транзакций:', error);
    } finally {
        loading.value = false;
    }
});

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed': return 'success';
        case 'processing': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
};

const getCashboxStatusLabel = (status) => {
    switch (status) {
        case 'full': return 'Заполнена';
        case 'normal': return 'Нормально';
        case 'low_change': return 'Мало сдачи';
        default: return status;
    }
};

const getCashboxStatusSeverity = (status) => {
    switch (status) {
        case 'full': return 'danger';
        case 'normal': return 'success';
        case 'low_change': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed': return 'Завершена';
        case 'processing': return 'Обработка';
        case 'error': return 'Ошибка';
        default: return status;
    }
};
</script>

<template>
    <div class="transaction-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Транзакции CASH</h1>
                <p class="text-color-secondary m-0">Транзакции наличными деньгами</p>
            </div>
            <Button label="Обновить" icon="pi pi-refresh" />
        </div>

        <div class="card">
            <DataTable :value="transactions" :loading="loading" :paginator="true" :rows="15" responsiveLayout="scroll">
                <Column field="transactionId" header="ID транзакции" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.transactionId }}</span>
                    </template>
                </Column>

                <Column field="amount" header="Сумма транзакции" :sortable="true" style="min-width: 140px">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.amount) }}
                        </div>
                    </template>
                </Column>

                <Column header="Наличные" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="text-sm" v-if="data.paymentDetails">
                            <div><strong>Получено:</strong> {{ formatAmount(data.paymentDetails.cashReceived) }}</div>
                            <div><strong>Сдача:</strong> {{ formatAmount(data.paymentDetails.changeGiven) }}</div>
                        </div>
                    </template>
                </Column>

                <Column field="transportBenefitCode" header="Код транс. льготы" :sortable="true" style="min-width: 140px">
                    <template #body="{ data }">
                        <span v-if="data.transportBenefitCode" class="font-mono text-sm">{{ data.transportBenefitCode }}</span>
                        <span v-else class="text-color-secondary">—</span>
                    </template>
                </Column>

                <Column field="socialBenefitCode" header="Код соц. льготы" :sortable="true" style="min-width: 140px">
                    <template #body="{ data }">
                        <span v-if="data.socialBenefitCode" class="font-mono text-sm">{{ data.socialBenefitCode }}</span>
                        <span v-else class="text-color-secondary">—</span>
                    </template>
                </Column>

                <Column field="cashboxStatus" header="Статус кассы" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag
                            :value="getCashboxStatusLabel(data.cashboxStatus)"
                            :severity="getCashboxStatusSeverity(data.cashboxStatus)"
                            size="small"
                        />
                    </template>
                </Column>

                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column field="operationDate" header="Время операции" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.operationDate) }}
                    </template>
                </Column>

                <Column header="Детали оплаты">
                    <template #body="{ data }">
                        <div class="text-sm" v-if="data.paymentDetails">
                            <div>Получено: {{ formatAmount(data.paymentDetails.cashReceived || 0) }}</div>
                            <div>Сдача: {{ formatAmount(data.paymentDetails.changeGiven || 0) }}</div>
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.transaction-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
