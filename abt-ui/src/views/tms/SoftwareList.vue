<script setup>
import { ref } from 'vue';

const softwareVersions = ref([
    {
        id: 1,
        version: '2.1.15',
        releaseDate: '2024-01-15T00:00:00Z',
        status: 'stable',
        description: 'Исправления ошибок и улучшения производительности',
        downloadUrl: '/software/v2.1.15.zip'
    },
    {
        id: 2,
        version: '2.1.14',
        releaseDate: '2024-01-01T00:00:00Z',
        status: 'stable',
        description: 'Новые функции безопасности',
        downloadUrl: '/software/v2.1.14.zip'
    },
    {
        id: 3,
        version: '2.2.0-beta',
        releaseDate: '2024-01-20T00:00:00Z',
        status: 'beta',
        description: 'Бета-версия с новыми возможностями',
        downloadUrl: '/software/v2.2.0-beta.zip'
    }
]);

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'stable': return 'success';
        case 'beta': return 'warning';
        case 'deprecated': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'stable': return 'Стабильная';
        case 'beta': return 'Бета';
        case 'deprecated': return 'Устаревшая';
        default: return status;
    }
};
</script>

<template>
    <div class="software-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">ПО Терминалов</h1>
            <Button 
                label="Загрузить новую версию" 
                icon="pi pi-upload" 
            />
        </div>

        <div class="card">
            <DataTable :value="softwareVersions" responsiveLayout="scroll">
                <Column field="version" header="Версия" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.version }}</span>
                    </template>
                </Column>
                
                <Column field="releaseDate" header="Дата выпуска" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.releaseDate) }}
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="description" header="Описание">
                    <template #body="{ data }">
                        {{ data.description }}
                    </template>
                </Column>
                
                <Column header="Действия">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-download" 
                                size="small" 
                                text 
                                v-tooltip.top="'Скачать'"
                            />
                            <Button 
                                icon="pi pi-send" 
                                size="small" 
                                text 
                                v-tooltip.top="'Развернуть на терминалы'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.software-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
