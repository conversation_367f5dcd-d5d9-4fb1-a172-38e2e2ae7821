<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { TerminalService } from '@/service/TerminalService';

const route = useRoute();
const router = useRouter();
const terminalId = route.params.terminalId;

const loading = ref(true);
const terminal = ref(null);
const telemetry = ref(null);

onMounted(async () => {
    await loadTerminal();
    await loadTelemetry();
});

const loadTerminal = async () => {
    try {
        loading.value = true;
        const data = await TerminalService.getTerminalById(terminalId);
        terminal.value = data;
    } catch (error) {
        console.error('Ошибка загрузки терминала:', error);
    } finally {
        loading.value = false;
    }
};

const loadTelemetry = async () => {
    try {
        const data = await TerminalService.getTerminalTelemetry(terminalId);
        telemetry.value = data;
    } catch (error) {
        console.error('Ошибка загрузки телеметрии:', error);
    }
};

const goBack = () => {
    router.push('/tms/terminals');
};

const editTerminal = () => {
    router.push(`/tms/terminals/${terminalId}/edit`);
};

const restartTerminal = async () => {
    if (confirm('Вы уверены, что хотите перезагрузить терминал?')) {
        try {
            await TerminalService.restartTerminal(terminalId);
            console.log('Терминал перезагружается');
        } catch (error) {
            console.error('Ошибка перезагрузки:', error);
        }
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'Никогда';
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'online': return 'success';
        case 'offline': return 'danger';
        case 'maintenance': return 'warning';
        case 'warning': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'online': return 'Онлайн';
        case 'offline': return 'Офлайн';
        case 'maintenance': return 'Обслуживание';
        case 'warning': return 'Предупреждение';
        default: return 'Неизвестно';
    }
};
</script>

<template>
    <div class="terminal-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Детали терминала</h1>
            <div class="flex gap-2">
                <Button 
                    label="Назад к списку" 
                    icon="pi pi-arrow-left" 
                    outlined 
                    @click="goBack"
                />
                <Button 
                    v-if="terminal"
                    label="Редактировать" 
                    icon="pi pi-pencil" 
                    @click="editTerminal"
                />
            </div>
        </div>

        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных терминала...</p>
            </div>
        </div>

        <div v-else-if="!terminal" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Терминал не найден</h3>
                <Button 
                    label="Вернуться к списку" 
                    icon="pi pi-arrow-left" 
                    @click="goBack"
                />
            </div>
        </div>

        <div v-else>
            <!-- Основная информация -->
            <div class="card mb-4">
                <div class="flex align-items-start justify-content-between mb-4">
                    <div class="flex align-items-center">
                        <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-tablet text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold m-0 mb-1">{{ terminal.serialNumber }}</h2>
                            <p class="text-color-secondary m-0 mb-2">{{ terminal.model }}</p>
                            <Tag 
                                :value="getStatusLabel(terminal.status)" 
                                :severity="getStatusSeverity(terminal.status)" 
                            />
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <Button 
                            label="Перезагрузить" 
                            icon="pi pi-refresh" 
                            outlined
                            @click="restartTerminal"
                            :disabled="terminal.status === 'offline'"
                        />
                    </div>
                </div>

                <Divider />

                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Местоположение</label>
                            <p class="m-0">{{ terminal.location }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Организация</label>
                            <p class="m-0">{{ terminal.organizationName }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6" v-if="terminal.driverName">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Водитель</label>
                            <p class="m-0">{{ terminal.driverName }} ({{ terminal.driverId }})</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6" v-if="terminal.vehiclePlate">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Транспортное средство</label>
                            <p class="m-0">{{ terminal.vehiclePlate }} ({{ terminal.vehicleId }})</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Версия ПО</label>
                            <p class="m-0 font-mono">{{ terminal.softwareVersion }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Версия конфигурации</label>
                            <p class="m-0 font-mono">{{ terminal.configurationVersion }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Последняя активность</label>
                            <p class="m-0">{{ formatDate(terminal.lastSeen) }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Дата создания</label>
                            <p class="m-0">{{ formatDate(terminal.createdDate) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Телеметрия -->
            <div class="card" v-if="telemetry">
                <h3 class="text-lg font-semibold mb-4">Телеметрия</h3>
                <div class="grid">
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Заряд батареи</label>
                            <div class="flex align-items-center">
                                <ProgressBar :value="telemetry.batteryLevel" class="mr-2" style="width: 100px" />
                                <span class="font-semibold">{{ telemetry.batteryLevel }}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Сигнал сети</label>
                            <p class="m-0">{{ telemetry.networkSignal }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Температура</label>
                            <p class="m-0">{{ telemetry.temperature }}°C</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Транзакций сегодня</label>
                            <p class="m-0 font-semibold">{{ telemetry.transactionsToday }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Использование памяти</label>
                            <div class="flex align-items-center">
                                <ProgressBar :value="telemetry.memoryUsage" class="mr-2" style="width: 200px" />
                                <span>{{ telemetry.memoryUsage }}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Использование диска</label>
                            <div class="flex align-items-center">
                                <ProgressBar :value="telemetry.diskUsage" class="mr-2" style="width: 200px" />
                                <span>{{ telemetry.diskUsage }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.terminal-detail {
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.text-blue-600 {
    color: #2563eb;
}
</style>
