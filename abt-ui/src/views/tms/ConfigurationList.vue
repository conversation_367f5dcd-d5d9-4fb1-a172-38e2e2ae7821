<script setup>
import { ref } from 'vue';

const configurations = ref([
    {
        id: 1,
        name: 'Базовая конфигурация',
        version: '1.0.8',
        description: 'Стандартная конфигурация для городского транспорта',
        status: 'active',
        createdDate: '2024-01-01T00:00:00Z'
    },
    {
        id: 2,
        name: 'Конфигурация метро',
        version: '1.0.7',
        description: 'Специальная конфигурация для метрополитена',
        status: 'active',
        createdDate: '2023-12-15T00:00:00Z'
    },
    {
        id: 3,
        name: 'Тестовая конфигурация',
        version: '1.1.0-beta',
        description: 'Конфигурация для тестирования новых функций',
        status: 'draft',
        createdDate: '2024-01-20T00:00:00Z'
    }
]);

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'warning';
        case 'deprecated': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активная';
        case 'draft': return 'Черновик';
        case 'deprecated': return 'Устаревшая';
        default: return status;
    }
};
</script>

<template>
    <div class="configuration-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Конфигурации</h1>
            <Button 
                label="Создать конфигурацию" 
                icon="pi pi-plus" 
            />
        </div>

        <div class="card">
            <DataTable :value="configurations" responsiveLayout="scroll">
                <Column field="name" header="Наименование" :sortable="true">
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <small class="text-color-secondary">{{ data.description }}</small>
                        </div>
                    </template>
                </Column>
                
                <Column field="version" header="Версия" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.version }}</span>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="createdDate" header="Дата создания" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.createdDate) }}
                    </template>
                </Column>
                
                <Column header="Действия">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-eye" 
                                size="small" 
                                text 
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                icon="pi pi-send" 
                                size="small" 
                                text 
                                v-tooltip.top="'Развернуть'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.configuration-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
