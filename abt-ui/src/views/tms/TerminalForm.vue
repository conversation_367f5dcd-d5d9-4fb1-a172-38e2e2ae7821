<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { TerminalService } from '@/service/TerminalService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const terminalId = route.params.terminalId;
const isEdit = computed(() => !!terminalId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);

const form = ref({
    serialNumber: '',
    model: '',
    location: '',
    organizationId: null,
    driverId: '',
    driverName: '',
    vehicleId: '',
    vehiclePlate: ''
});

const errors = ref({});

onMounted(async () => {
    await loadOrganizations();
    if (isEdit.value) {
        await loadTerminal();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
    }
};

const loadTerminal = async () => {
    try {
        loading.value = true;
        const terminal = await TerminalService.getTerminalById(terminalId);
        if (terminal) {
            form.value = { ...terminal };
        }
    } catch (error) {
        console.error('Ошибка загрузки терминала:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.serialNumber.trim()) {
        errors.value.serialNumber = 'Серийный номер обязателен';
    }
    
    if (!form.value.model.trim()) {
        errors.value.model = 'Модель обязательна';
    }
    
    if (!form.value.location.trim()) {
        errors.value.location = 'Местоположение обязательно';
    }
    
    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveTerminal = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        if (isEdit.value) {
            await TerminalService.updateTerminal(terminalId, form.value);
        } else {
            await TerminalService.createTerminal(form.value);
        }
        
        router.push('/tms/terminals');
        
    } catch (error) {
        console.error('Ошибка сохранения терминала:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/tms/terminals');
};
</script>

<template>
    <div class="terminal-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование терминала' : 'Создание терминала' }}
            </h1>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveTerminal">
                <div class="grid">
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="serialNumber" class="font-medium">Серийный номер *</label>
                            <InputText 
                                id="serialNumber"
                                v-model="form.serialNumber" 
                                :class="{ 'p-invalid': errors.serialNumber }"
                                placeholder="TRM-MSK-001"
                                class="w-full"
                            />
                            <small v-if="errors.serialNumber" class="p-error">{{ errors.serialNumber }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="model" class="font-medium">Модель *</label>
                            <InputText 
                                id="model"
                                v-model="form.model" 
                                :class="{ 'p-invalid': errors.model }"
                                placeholder="SberTroika Terminal v2.1"
                                class="w-full"
                            />
                            <small v-if="errors.model" class="p-error">{{ errors.model }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="location" class="font-medium">Местоположение *</label>
                            <InputText 
                                id="location"
                                v-model="form.location" 
                                :class="{ 'p-invalid': errors.location }"
                                placeholder="Автобус №101, маршрут М1"
                                class="w-full"
                            />
                            <small v-if="errors.location" class="p-error">{{ errors.location }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <Dropdown 
                                id="organizationId"
                                v-model="form.organizationId" 
                                :options="organizations" 
                                optionLabel="name" 
                                optionValue="id"
                                placeholder="Выберите организацию"
                                :class="{ 'p-invalid': errors.organizationId }"
                                class="w-full"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Дополнительная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="driverId" class="font-medium">ID водителя</label>
                            <InputText 
                                id="driverId"
                                v-model="form.driverId" 
                                placeholder="DRV001"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="driverName" class="font-medium">ФИО водителя</label>
                            <InputText 
                                id="driverName"
                                v-model="form.driverName" 
                                placeholder="Иванов И.И."
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="vehicleId" class="font-medium">ID транспортного средства</label>
                            <InputText 
                                id="vehicleId"
                                v-model="form.vehicleId" 
                                placeholder="BUS101"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="vehiclePlate" class="font-medium">Гос. номер ТС</label>
                            <InputText 
                                id="vehiclePlate"
                                v-model="form.vehiclePlate" 
                                placeholder="А123БВ77"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных терминала...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.terminal-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
