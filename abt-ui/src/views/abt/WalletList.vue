<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { SubscriptionService } from '@/service/SubscriptionService';

const router = useRouter();
const wallets = ref([]);
const pagination = ref({});
const loading = ref(true);
const selectedWallet = ref(null);

// Фильтры
const filters = ref({
    search: '',
    status: null,
    balanceFrom: null,
    balanceTo: null,
    dateFrom: null,
    dateTo: null
});

// Пагинация
const currentPage = ref(0);
const pageSize = ref(15);

// Опции для фильтров
const statusOptions = [
    { label: 'Все статусы', value: null },
    { label: 'Активный', value: 'active' },
    { label: 'Неактивный', value: 'inactive' },
    { label: 'Заблокирован', value: 'blocked' },
    { label: 'Истекший', value: 'expired' }
];

const loadWallets = async (page = 0) => {
    try {
        loading.value = true;
        const params = {
            page: page,
            size: pageSize.value,
            type: 'WALLET' // Фильтруем только кошельки
        };
        
        const data = await SubscriptionService.getSubscriptions(params);
        wallets.value = data.content || [];
        pagination.value = data.pagination || {};
        currentPage.value = page;
    } catch (error) {
        console.error('Ошибка загрузки кошельков:', error);
        wallets.value = [];
        pagination.value = {};
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    loadWallets();
});

// Фильтрованные данные (локальная фильтрация для поиска и дат)
const filteredWallets = computed(() => {
    let filtered = wallets.value;

    if (filters.value.search) {
        const search = filters.value.search.toLowerCase();
        filtered = filtered.filter(wallet => 
            wallet.abonementId?.toString().includes(search) ||
            wallet.name?.toLowerCase().includes(search) ||
            wallet.description?.toLowerCase().includes(search)
        );
    }

    if (filters.value.dateFrom) {
        filtered = filtered.filter(wallet => new Date(wallet.activeFrom) >= filters.value.dateFrom);
    }

    if (filters.value.dateTo) {
        filtered = filtered.filter(wallet => new Date(wallet.activeTill) <= filters.value.dateTo);
    }

    return filtered;
});

const getStatusSeverity = (wallet) => {
    const now = new Date();
    const activeFrom = new Date(wallet.activeFrom);
    const activeTill = new Date(wallet.activeTill);
    
    if (now >= activeFrom && now <= activeTill) {
        return 'success';
    } else if (now > activeTill) {
        return 'danger';
    } else {
        return 'secondary';
    }
};

const getStatusLabel = (wallet) => {
    const now = new Date();
    const activeFrom = new Date(wallet.activeFrom);
    const activeTill = new Date(wallet.activeTill);
    
    if (now >= activeFrom && now <= activeTill) {
        return 'Активный';
    } else if (now > activeTill) {
        return 'Истекший';
    } else {
        return 'Неактивный';
    }
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount || 0);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getBalanceSeverity = (balance) => {
    if (balance === 0) return 'danger';
    if (balance < 100) return 'warning';
    if (balance < 1000) return 'info';
    return 'success';
};

const clearFilters = () => {
    filters.value = {
        search: '',
        status: null,
        balanceFrom: null,
        balanceTo: null,
        dateFrom: null,
        dateTo: null
    };
    // Перезагружаем данные с очищенными фильтрами
    loadWallets(0);
};

const viewWallet = (wallet) => {
    router.push(`/abt/subscriptions/${wallet.id}`);
};

const refreshData = async () => {
    await loadWallets(currentPage.value);
};

const onPageChange = (event) => {
    loadWallets(event.page);
};

const applyFilters = () => {
    // Применяем серверные фильтры
    loadWallets(0);
};

// Получаем баланс кошелька (пока заглушка, нужно будет добавить логику получения счетчиков)
const getWalletBalance = (wallet) => {
    // TODO: Добавить логику получения баланса из счетчиков
    return 0;
};

// Получаем количество операций (пока заглушка)
const getTransactionCount = (wallet) => {
    // TODO: Добавить логику получения количества операций
    return 0;
};
</script>

<template>
    <div class="wallet-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Кошельки</h1>
                <p class="text-color-secondary m-0">Управление кошельками пользователей</p>
            </div>
            <Button 
                label="Обновить" 
                icon="pi pi-refresh" 
                @click="refreshData"
                :loading="loading"
            />
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3>Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Поиск</label>
                    <InputText 
                        v-model="filters.search" 
                        placeholder="Номер, название, описание..."
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="grid">
                        <div class="col-6">
                            <label class="font-semibold">С даты</label>
                            <Calendar 
                                v-model="filters.dateFrom" 
                                dateFormat="dd.mm.yy"
                                placeholder="С даты"
                                class="w-full"
                            />
                        </div>
                        <div class="col-6">
                            <label class="font-semibold">По дату</label>
                            <Calendar 
                                v-model="filters.dateTo" 
                                dateFormat="dd.mm.yy"
                                placeholder="По дату"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-content-end mt-3 gap-2">
                <Button 
                    label="Применить фильтры" 
                    icon="pi pi-filter" 
                    @click="applyFilters"
                    class="p-button-primary"
                />
                <Button 
                    label="Очистить фильтры" 
                    icon="pi pi-times" 
                    @click="clearFilters"
                    class="p-button-secondary"
                />
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable 
                :value="filteredWallets" 
                :loading="loading" 
                :paginator="true" 
                :rows="pageSize" 
                :totalRecords="pagination.totalElements || 0"
                :lazy="true"
                @page="onPageChange"
                responsiveLayout="scroll"
                v-model:selection="selectedWallet"
                selectionMode="single"
                dataKey="id"
            >
                <Column field="abonementId" header="Номер кошелька" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold cursor-pointer" @click="viewWallet(data)">
                            {{ data.abonementId || 'N/A' }}
                        </div>
                    </template>
                </Column>
                
                <Column field="name" header="Название" :sortable="true">
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <div class="text-sm text-color-secondary">{{ data.description }}</div>
                        </div>
                    </template>
                </Column>
                
                <Column field="balance" header="Баланс" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right">
                            <div class="font-semibold" :class="`text-${getBalanceSeverity(getWalletBalance(data))}`">
                                {{ formatCurrency(getWalletBalance(data)) }}
                            </div>
                            <div class="text-xs text-color-secondary">
                                {{ getTransactionCount(data) }} операций
                            </div>
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data)" 
                            :severity="getStatusSeverity(data)" 
                        />
                    </template>
                </Column>
                
                <Column field="activeFrom" header="Период действия" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div>С: {{ formatDate(data.activeFrom) }}</div>
                            <div>По: {{ formatDate(data.activeTill) }}</div>
                        </div>
                    </template>
                </Column>
                
                <Column field="createdAt" header="Создан" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ formatDateTime(data.createdAt) }}
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-eye" 
                                class="p-button-text p-button-sm" 
                                @click="viewWallet(data)"
                            />
                            <Button 
                                icon="pi pi-plus" 
                                class="p-button-text p-button-sm p-button-success" 
                            />
                            <Button 
                                icon="pi pi-ban" 
                                class="p-button-text p-button-sm p-button-warning" 
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.wallet-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-pointer:hover {
    text-decoration: underline;
}
</style> 