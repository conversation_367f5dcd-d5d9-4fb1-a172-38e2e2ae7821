<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { SaleRuleService } from '@/service/SaleRuleService';

const router = useRouter();
const saleRules = ref([]);
const loading = ref(true);
const selectedRule = ref(null);

// Фильтры
const filters = ref({
    search: '',
    ruleType: null,
    isActive: null
});

// Опции для фильтров
const ruleTypeOptions = [
    { label: 'Все типы', value: null },
    { label: 'Месячный диапазон', value: 'MONTHLY_RANGE' },
    { label: 'Недельный диапазон', value: 'WEEKLY_RANGE' },
    { label: 'Дневной диапазон', value: 'DAILY_RANGE' },
    { label: 'Диапазон баланса', value: 'BALANCE_RANGE' }
];

const activeOptions = [
    { label: 'Все', value: null },
    { label: 'Активные', value: true },
    { label: 'Неактивные', value: false }
];

onMounted(async () => {
    try {
        const data = await SaleRuleService.getSaleRules();
        saleRules.value = data;
    } catch (error) {
        console.error('Ошибка загрузки правил продаж:', error);
    } finally {
        loading.value = false;
    }
});

// Фильтрованные данные
const filteredRules = computed(() => {
    let filtered = saleRules.value;

    if (filters.value.search) {
        const search = filters.value.search.toLowerCase();
        filtered = filtered.filter(rule => 
            rule.serviceName.toLowerCase().includes(search) ||
            rule.ruleType.toLowerCase().includes(search)
        );
    }

    if (filters.value.ruleType) {
        filtered = filtered.filter(rule => rule.ruleType === filters.value.ruleType);
    }

    if (filters.value.isActive !== null) {
        filtered = filtered.filter(rule => rule.isActive === filters.value.isActive);
    }

    return filtered;
});

const getRuleTypeLabel = (type) => {
    switch (type) {
        case 'MONTHLY_RANGE': return 'Месячный диапазон';
        case 'WEEKLY_RANGE': return 'Недельный диапазон';
        case 'DAILY_RANGE': return 'Дневной диапазон';
        case 'BALANCE_RANGE': return 'Диапазон баланса';
        default: return type;
    }
};

const getRuleTypeSeverity = (type) => {
    switch (type) {
        case 'MONTHLY_RANGE': return 'info';
        case 'WEEKLY_RANGE': return 'warning';
        case 'DAILY_RANGE': return 'success';
        case 'BALANCE_RANGE': return 'danger';
        default: return 'secondary';
    }
};

const getRuleLogicLabel = (logic) => {
    switch (logic) {
        case 'AND': return 'И';
        case 'OR': return 'ИЛИ';
        default: return logic;
    }
};

const formatCurrency = (amount) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount / 100); // Конвертируем из копеек в рубли
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getRuleDescription = (rule) => {
    switch (rule.ruleType) {
        case 'MONTHLY_RANGE':
            return `Дни месяца: ${rule.startDay} - ${rule.endDay}`;
        case 'WEEKLY_RANGE':
            return `Недели: ${rule.startWeek} - ${rule.endWeek}`;
        case 'DAILY_RANGE':
            return `Дни: ${rule.startDay} - ${rule.endDay}`;
        case 'BALANCE_RANGE':
            return `Баланс: ${formatCurrency(rule.minCardBalance)} - ${formatCurrency(rule.maxCardBalance)}`;
        default:
            return 'Не определено';
    }
};

const clearFilters = () => {
    filters.value = {
        search: '',
        ruleType: null,
        isActive: null
    };
};

const createRule = () => {
    router.push('/abt/sale-rules/create');
};

const editRule = (rule) => {
    router.push(`/abt/sale-rules/${rule.id}/edit`);
};

const deleteRule = async (rule) => {
    try {
        await SaleRuleService.deleteSaleRule(rule.id);
        // Обновляем список после удаления
        const data = await SaleRuleService.getSaleRules();
        saleRules.value = data;
    } catch (error) {
        console.error('Ошибка удаления правила:', error);
    }
};
</script>

<template>
    <div class="sale-rule-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Правила продаж</h1>
                <p class="text-color-secondary m-0">Управление правилами продаж услуг</p>
            </div>
            <Button 
                label="Создать правило" 
                icon="pi pi-plus" 
                @click="createRule"
                class="p-button-success"
            />
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3>Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-4">
                    <label class="font-semibold">Поиск</label>
                    <InputText 
                        v-model="filters.search" 
                        placeholder="Услуга, тип правила..."
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-4">
                    <label class="font-semibold">Тип правила</label>
                    <Dropdown 
                        v-model="filters.ruleType" 
                        :options="ruleTypeOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите тип"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-4">
                    <label class="font-semibold">Статус</label>
                    <Dropdown 
                        v-model="filters.isActive" 
                        :options="activeOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите статус"
                        class="w-full"
                    />
                </div>
            </div>
            
            <div class="flex justify-content-end mt-3">
                <Button 
                    label="Очистить фильтры" 
                    icon="pi pi-times" 
                    @click="clearFilters"
                    class="p-button-secondary"
                />
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable 
                :value="filteredRules" 
                :loading="loading" 
                :paginator="true" 
                :rows="15" 
                responsiveLayout="scroll"
                v-model:selection="selectedRule"
                selectionMode="single"
                dataKey="id"
            >
                <Column field="serviceName" header="Услуга" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold">{{ data.serviceName }}</div>
                    </template>
                </Column>
                
                <Column field="ruleType" header="Тип правила" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getRuleTypeLabel(data.ruleType)" 
                            :severity="getRuleTypeSeverity(data.ruleType)" 
                        />
                    </template>
                </Column>
                
                <Column field="ruleLogic" header="Логика" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getRuleLogicLabel(data.ruleLogic)" 
                            severity="info"
                        />
                    </template>
                </Column>
                
                <Column header="Условия" :sortable="false">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ getRuleDescription(data) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="isActive" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="data.isActive ? 'Активно' : 'Неактивно'" 
                            :severity="data.isActive ? 'success' : 'danger'"
                        />
                    </template>
                </Column>
                
                <Column field="createdAt" header="Создано" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ formatDate(data.createdAt) }}
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-pencil" 
                                class="p-button-text p-button-sm p-button-success" 
                                @click="editRule(data)"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                class="p-button-text p-button-sm p-button-danger" 
                                @click="deleteRule(data)"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.sale-rule-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
</style> 