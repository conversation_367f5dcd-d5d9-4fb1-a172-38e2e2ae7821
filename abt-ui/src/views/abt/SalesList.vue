<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { SalesService } from '@/service/SalesService';

const router = useRouter();
const sales = ref([]);
const loading = ref(true);
const selectedSale = ref(null);

// Фильтры
const filters = ref({
    search: '',
    invoiceStatus: null,
    orderStatus: null,
    regionId: '',
    dateFrom: null,
    dateTo: null
});

// Опции для фильтров
const invoiceStatusOptions = ref([]);
const orderStatusOptions = ref([]);

onMounted(async () => {
    try {
        // Загружаем опции для фильтров
        invoiceStatusOptions.value = await SalesService.getInvoiceStatuses();
        orderStatusOptions.value = await SalesService.getOrderStatuses();
        
        // Загружаем данные
        const data = await SalesService.getSales();
        sales.value = data;
    } catch (error) {
        console.error('Ошибка загрузки продаж:', error);
    } finally {
        loading.value = false;
    }
});

// Фильтрованные данные
const filteredSales = computed(() => {
    let filtered = sales.value;

    if (filters.value.search) {
        const search = filters.value.search.toLowerCase();
        filtered = filtered.filter(sale => 
            sale.invoiceId.toLowerCase().includes(search) ||
            sale.orderId.toLowerCase().includes(search) ||
            sale.agentTransactionId.toLowerCase().includes(search) ||
            sale.items.some(item => 
                item.serviceName && item.serviceName.toLowerCase().includes(search) ||
                item.description && item.description.toLowerCase().includes(search)
            )
        );
    }

    if (filters.value.invoiceStatus) {
        filtered = filtered.filter(sale => sale.invoiceStatus === filters.value.invoiceStatus);
    }

    if (filters.value.orderStatus) {
        filtered = filtered.filter(sale => sale.orderStatus === filters.value.orderStatus);
    }

    if (filters.value.regionId) {
        filtered = filtered.filter(sale => sale.regionId === filters.value.regionId);
    }

    if (filters.value.dateFrom) {
        filtered = filtered.filter(sale => 
            new Date(sale.createdAt) >= new Date(filters.value.dateFrom)
        );
    }

    if (filters.value.dateTo) {
        filtered = filtered.filter(sale => 
            new Date(sale.createdAt) <= new Date(filters.value.dateTo)
        );
    }

    return filtered;
});

const getInvoiceStatusLabel = (status) => {
    switch (status) {
        case 'CREATED': return 'Создан';
        case 'PAID': return 'Оплачен';
        case 'CANCELED': return 'Отменен';
        case 'OUTDATED': return 'Устарел';
        default: return status;
    }
};

const getInvoiceStatusSeverity = (status) => {
    switch (status) {
        case 'CREATED': return 'info';
        case 'PAID': return 'success';
        case 'CANCELED': return 'danger';
        case 'OUTDATED': return 'warning';
        default: return 'secondary';
    }
};

const getOrderStatusLabel = (status) => {
    switch (status) {
        case 'PENDING': return 'В ожидании';
        case 'COMPLETED': return 'Завершен';
        case 'CANCELED': return 'Отменен';
        default: return status;
    }
};

const getOrderStatusSeverity = (status) => {
    switch (status) {
        case 'PENDING': return 'warning';
        case 'COMPLETED': return 'success';
        case 'CANCELED': return 'danger';
        default: return 'secondary';
    }
};

const getCardTypeLabel = (type) => {
    switch (type) {
        case 'TRANSPORT': return 'Транспортная';
        case 'IPS': return 'IPS';
        default: return type;
    }
};

const formatCurrency = (amount) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount / 100); // Конвертируем из копеек в рубли
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ru-RU');
};

const clearFilters = () => {
    filters.value = {
        search: '',
        invoiceStatus: null,
        orderStatus: null,
        regionId: '',
        dateFrom: null,
        dateTo: null
    };
};

const viewSale = (sale) => {
    router.push(`/abt/sales/${sale.invoiceId}`);
};

const getItemsSummary = (items) => {
    const purchases = items.filter(item => item.type === 'PURCHASE');
    const replenishments = items.filter(item => item.type === 'REPLENISHMENT');
    
    let summary = '';
    if (purchases.length > 0) {
        summary += `Абонементы: ${purchases.length}`;
    }
    if (replenishments.length > 0) {
        if (summary) summary += ', ';
        summary += `Пополнения: ${replenishments.length}`;
    }
    return summary || 'Нет товаров';
};
</script>

<template>
    <div class="sales-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Продажи</h1>
                <p class="text-color-secondary m-0">Список проданных абонементов и пополнений кошельков</p>
            </div>
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3>Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Поиск</label>
                    <InputText 
                        v-model="filters.search" 
                        placeholder="ID счета, заказа, транзакции..."
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Статус счета</label>
                    <Dropdown 
                        v-model="filters.invoiceStatus" 
                        :options="invoiceStatusOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите статус"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Статус заказа</label>
                    <Dropdown 
                        v-model="filters.orderStatus" 
                        :options="orderStatusOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите статус"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Регион</label>
                    <InputText 
                        v-model="filters.regionId" 
                        placeholder="ID региона"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Дата с</label>
                    <Calendar 
                        v-model="filters.dateFrom" 
                        dateFormat="dd.mm.yy"
                        placeholder="Выберите дату"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Дата по</label>
                    <Calendar 
                        v-model="filters.dateTo" 
                        dateFormat="dd.mm.yy"
                        placeholder="Выберите дату"
                        class="w-full"
                    />
                </div>
            </div>
            
            <div class="flex justify-content-end mt-3">
                <Button 
                    label="Очистить фильтры" 
                    icon="pi pi-times" 
                    @click="clearFilters"
                    class="p-button-secondary"
                />
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable 
                :value="filteredSales" 
                :loading="loading" 
                :paginator="true" 
                :rows="15" 
                responsiveLayout="scroll"
                v-model:selection="selectedSale"
                selectionMode="single"
                dataKey="invoiceId"
            >
                <Column field="invoiceId" header="ID счета" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold cursor-pointer" @click="viewSale(data)">
                            {{ data.invoiceId }}
                        </div>
                    </template>
                </Column>
                
                <Column field="orderId" header="ID заказа" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold">{{ data.orderId }}</div>
                    </template>
                </Column>
                
                <Column field="invoiceStatus" header="Статус счета" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getInvoiceStatusLabel(data.invoiceStatus)" 
                            :severity="getInvoiceStatusSeverity(data.invoiceStatus)" 
                        />
                    </template>
                </Column>
                
                <Column field="orderStatus" header="Статус заказа" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getOrderStatusLabel(data.orderStatus)" 
                            :severity="getOrderStatusSeverity(data.orderStatus)" 
                        />
                    </template>
                </Column>
                
                <Column field="invoiceAmount" header="Сумма" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatCurrency(data.invoiceAmount) }}
                        </div>
                    </template>
                </Column>
                
                <Column header="Товары" :sortable="false">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ getItemsSummary(data.items) }}
                        </div>
                        <div v-if="data.items.length > 0" class="text-xs text-color-secondary mt-1">
                            <div v-for="item in data.items.slice(0, 2)" :key="item.id">
                                {{ item.serviceName || item.description }}
                            </div>
                            <div v-if="data.items.length > 2" class="text-color-secondary">
                                +{{ data.items.length - 2 }} еще
                            </div>
                        </div>
                    </template>
                </Column>
                
                <Column header="Карты" :sortable="false">
                    <template #body="{ data }">
                        <div v-if="data.cards.length > 0" class="text-sm">
                            <div v-for="card in data.cards.slice(0, 2)" :key="card.cardId">
                                <div class="font-semibold">{{ getCardTypeLabel(card.cardType) }}</div>
                                <div v-if="card.pan" class="text-xs text-color-secondary">
                                    {{ card.pan }}
                                </div>
                                <div v-if="card.paymentSystem" class="text-xs text-color-secondary">
                                    {{ card.paymentSystem }}
                                </div>
                            </div>
                            <div v-if="data.cards.length > 2" class="text-color-secondary text-xs">
                                +{{ data.cards.length - 2 }} еще
                            </div>
                        </div>
                        <div v-else class="text-color-secondary">
                            Нет карт
                        </div>
                    </template>
                </Column>
                
                <Column field="createdAt" header="Дата создания" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ formatDateTime(data.createdAt) }}
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-eye" 
                                class="p-button-text p-button-sm" 
                                @click="viewSale(data)"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.sales-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-pointer:hover {
    text-decoration: underline;
}
</style> 