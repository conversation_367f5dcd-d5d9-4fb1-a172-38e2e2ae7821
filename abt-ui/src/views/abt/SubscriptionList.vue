<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { SubscriptionService } from '@/service/SubscriptionService';

const router = useRouter();
const subscriptions = ref([]);
const pagination = ref({});
const loading = ref(true);
const selectedSubscription = ref(null);

// Фильтры
const filters = ref({
    search: '',
    type: null,
    status: null,
    isSocial: null,
    dateFrom: null,
    dateTo: null
});

// Пагинация
const currentPage = ref(0);
const pageSize = ref(15);

// Опции для фильтров (исключаем WALLET, так как это кошельки)
const typeOptions = [
    { label: 'Все типы', value: null },
    { label: 'Поездочный', value: 'TRAVEL' },
    { label: 'Безлимитный', value: 'UNLIMITED' }
];

const statusOptions = [
    { label: 'Все статусы', value: null },
    { label: 'Активный', value: 'active' },
    { label: 'Неактивный', value: 'inactive' },
    { label: 'Просрочен', value: 'expired' },
    { label: 'Заблокирован', value: 'blocked' }
];

const socialOptions = [
    { label: 'Все', value: null },
    { label: 'Социальные', value: true },
    { label: 'Обычные', value: false }
];

const loadSubscriptions = async (page = 0) => {
    try {
        loading.value = true;
        const params = {
            page: page,
            size: pageSize.value
        };
        
        // Исключаем кошельки (WALLET) из списка абонементов
        // Показываем только TRAVEL и UNLIMITED
        const excludeTypes = ['TRAVEL', 'UNLIMITED'];
        params.excludeType = 'WALLET'; // Добавляем параметр для исключения WALLET
        
        // Добавляем фильтры
        if (filters.value.type && filters.value.type !== 'WALLET') {
            params.type = filters.value.type;
        }
        
        if (filters.value.isSocial !== null) {
            params.isSocial = filters.value.isSocial;
        }
        
        const data = await SubscriptionService.getSubscriptions(params);
        subscriptions.value = data.content || [];
        pagination.value = data.pagination || {};
        currentPage.value = page;
    } catch (error) {
        console.error('Ошибка загрузки абонементов:', error);
        subscriptions.value = [];
        pagination.value = {};
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    loadSubscriptions();
});

// Фильтрованные данные (локальная фильтрация для поиска и дат)
const filteredSubscriptions = computed(() => {
    let filtered = subscriptions.value;

    if (filters.value.search) {
        const search = filters.value.search.toLowerCase();
        filtered = filtered.filter(sub => 
            sub.abonementId?.toString().includes(search) ||
            sub.name?.toLowerCase().includes(search) ||
            sub.description?.toLowerCase().includes(search)
        );
    }

    if (filters.value.dateFrom) {
        filtered = filtered.filter(sub => new Date(sub.activeFrom) >= filters.value.dateFrom);
    }

    if (filters.value.dateTo) {
        filtered = filtered.filter(sub => new Date(sub.activeTill) <= filters.value.dateTo);
    }

    return filtered;
});

const getTypeLabel = (subscription) => {
    // Здесь нужно будет добавить логику определения типа абонемента
    // на основе шаблона или других полей
    return 'Не определен';
};

const getStatusSeverity = (subscription) => {
    const now = new Date();
    const activeFrom = new Date(subscription.activeFrom);
    const activeTill = new Date(subscription.activeTill);
    
    if (now >= activeFrom && now <= activeTill) {
        return 'success';
    } else if (now > activeTill) {
        return 'danger';
    } else {
        return 'secondary';
    }
};

const getStatusLabel = (subscription) => {
    const now = new Date();
    const activeFrom = new Date(subscription.activeFrom);
    const activeTill = new Date(subscription.activeTill);
    
    if (now >= activeFrom && now <= activeTill) {
        return 'Активный';
    } else if (now > activeTill) {
        return 'Просрочен';
    } else {
        return 'Неактивный';
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const clearFilters = () => {
    filters.value = {
        search: '',
        type: null,
        status: null,
        isSocial: null,
        dateFrom: null,
        dateTo: null
    };
    // Перезагружаем данные с очищенными фильтрами
    loadSubscriptions(0);
};

const viewSubscription = (subscription) => {
    router.push(`/abt/subscriptions/${subscription.id}`);
};

const refreshData = async () => {
    await loadSubscriptions(currentPage.value);
};

const onPageChange = (event) => {
    loadSubscriptions(event.page);
};

const applyFilters = () => {
    // Применяем серверные фильтры (тип, социальный статус)
    loadSubscriptions(0);
};
</script>

<template>
    <div class="subscription-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Абонементы</h1>
                <p class="text-color-secondary m-0">Управление абонементами пользователей</p>
            </div>
            <Button 
                label="Обновить" 
                icon="pi pi-refresh" 
                @click="refreshData"
                :loading="loading"
            />
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3>Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-3">
                    <label class="font-semibold">Поиск</label>
                    <InputText 
                        v-model="filters.search" 
                        placeholder="Номер, название, описание..."
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-2">
                    <label class="font-semibold">Тип</label>
                    <Dropdown 
                        v-model="filters.type" 
                        :options="typeOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите тип"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-2">
                    <label class="font-semibold">Категория</label>
                    <Dropdown 
                        v-model="filters.isSocial" 
                        :options="socialOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите категорию"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="grid">
                        <div class="col-6">
                            <label class="font-semibold">С даты</label>
                            <Calendar 
                                v-model="filters.dateFrom" 
                                dateFormat="dd.mm.yy"
                                placeholder="С даты"
                                class="w-full"
                            />
                        </div>
                        <div class="col-6">
                            <label class="font-semibold">По дату</label>
                            <Calendar 
                                v-model="filters.dateTo" 
                                dateFormat="dd.mm.yy"
                                placeholder="По дату"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-content-end mt-3 gap-2">
                <Button 
                    label="Применить фильтры" 
                    icon="pi pi-filter" 
                    @click="applyFilters"
                    class="p-button-primary"
                />
                <Button 
                    label="Очистить фильтры" 
                    icon="pi pi-times" 
                    @click="clearFilters"
                    class="p-button-secondary"
                />
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable 
                :value="filteredSubscriptions" 
                :loading="loading" 
                :paginator="true" 
                :rows="pageSize" 
                :totalRecords="pagination.totalElements || 0"
                :lazy="true"
                @page="onPageChange"
                responsiveLayout="scroll"
                v-model:selection="selectedSubscription"
                selectionMode="single"
                dataKey="id"
            >
                <Column field="abonementId" header="Номер абонемента" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold cursor-pointer" @click="viewSubscription(data)">
                            {{ data.abonementId || 'N/A' }}
                        </div>
                    </template>
                </Column>
                
                <Column field="name" header="Название" :sortable="true">
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <div class="text-sm text-color-secondary">{{ data.description }}</div>
                        </div>
                    </template>
                </Column>
                
                <Column field="type" header="Тип" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <Tag :value="getTypeLabel(data)" severity="info" />
                            <Tag v-if="data.isSocial" value="Социальный" severity="warning" class="ml-1" />
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data)" 
                            :severity="getStatusSeverity(data)" 
                        />
                    </template>
                </Column>
                
                <Column field="activeFrom" header="Период действия" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div>С: {{ formatDate(data.activeFrom) }}</div>
                            <div>По: {{ formatDate(data.activeTill) }}</div>
                        </div>
                    </template>
                </Column>
                
                <Column field="createdAt" header="Создан" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ formatDateTime(data.createdAt) }}
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-eye" 
                                class="p-button-text p-button-sm" 
                                @click="viewSubscription(data)"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                class="p-button-text p-button-sm p-button-success" 
                            />
                            <Button 
                                icon="pi pi-ban" 
                                class="p-button-text p-button-sm p-button-warning" 
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.subscription-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-pointer:hover {
    text-decoration: underline;
}
</style> 