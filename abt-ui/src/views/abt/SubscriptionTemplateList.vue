<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { SubscriptionTemplateService } from '@/service/SubscriptionTemplateService';

const router = useRouter();
const templates = ref([]);
const loading = ref(true);
const selectedTemplate = ref(null);

onMounted(async () => {
    try {
        const data = await SubscriptionTemplateService.getTemplates();
        templates.value = data;
    } catch (error) {
        console.error('Ошибка загрузки шаблонов:', error);
        alert(`Ошибка загрузки шаблонов: ${error.message}`);
    } finally {
        loading.value = false;
    }
});

const getTypeLabel = (type) => {
    switch (type) {
        case 'WALLET': return 'Кошелек';
        case 'TRAVEL': return 'Поездочный';
        case 'UNLIMITED': return 'Безлимитный';
        default: return type;
    }
};

const getValidTimeTypeLabel = (type) => {
    switch (type) {
        case 'INTERVAL': return 'Календарный месяц';
        case 'DAYS': return 'Дни от первой поездки';
        case 'INTERVAL_AND_DAYS': return 'Дни с ограничением';
        default: return type;
    }
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'inactive': return 'secondary';
        case 'draft': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'inactive': return 'Неактивный';
        case 'draft': return 'Черновик';
        default: return status;
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const createTemplate = () => {
    router.push('/abt/templates/create');
};

const editTemplate = (template) => {
    router.push(`/abt/templates/${template.id}/edit`);
};

const viewTemplate = (template) => {
    router.push(`/abt/templates/${template.id}`);
};

const deleteTemplate = async (template) => {
    try {
        // Запрашиваем подтверждение удаления
        if (!confirm(`Вы уверены, что хотите удалить шаблон "${template.stName}"?`)) {
            return;
        }
        
        await SubscriptionTemplateService.deleteTemplate(template.id);
        
        // Обновляем список после удаления
        const data = await SubscriptionTemplateService.getTemplates();
        templates.value = data;
        
        // Показываем уведомление об успехе
        alert('Шаблон успешно удален');
    } catch (error) {
        console.error('Ошибка удаления шаблона:', error);
        alert(`Ошибка удаления шаблона: ${error.message}`);
    }
};
</script>

<template>
    <div class="subscription-template-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Шаблоны абонементов</h1>
                <p class="text-color-secondary m-0">Управление шаблонами абонементов и их счетчиками</p>
            </div>
            <Button 
                label="Создать шаблон" 
                icon="pi pi-plus" 
                @click="createTemplate"
                class="p-button-success"
            />
        </div>

        <div class="card">
            <DataTable 
                :value="templates" 
                :loading="loading" 
                :paginator="true" 
                :rows="10" 
                responsiveLayout="scroll"
                v-model:selection="selectedTemplate"
                selectionMode="single"
                dataKey="id"
            >
                <Column field="stName" header="Название" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold cursor-pointer" @click="viewTemplate(data)">
                            {{ data.stName }}
                        </div>
                    </template>
                </Column>
                
                <Column field="description" header="Описание" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm text-color-secondary">
                            {{ data.description }}
                        </div>
                    </template>
                </Column>
                
                <Column field="type" header="Тип" :sortable="true">
                    <template #body="{ data }">
                        <Tag :value="getTypeLabel(data.type)" severity="info" />
                    </template>
                </Column>
                
                <Column field="isSocial" header="Социальный" :sortable="true">
                    <template #body="{ data }">
                        <i v-if="data.isSocial" class="pi pi-check text-green-500"></i>
                        <i v-else class="pi pi-times text-red-500"></i>
                    </template>
                </Column>
                
                <Column field="validTimeType" header="Срок действия" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div>{{ getValidTimeTypeLabel(data.validTimeType) }}</div>
                            <div v-if="data.validTimeDays" class="text-color-secondary">
                                {{ data.validTimeDays }} дней
                            </div>
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="activeFrom" header="Период действия" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div>С: {{ formatDate(data.activeFrom) }}</div>
                            <div>По: {{ formatDate(data.activeTill) }}</div>
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-eye" 
                                class="p-button-text p-button-sm" 
                                @click="viewTemplate(data)"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                class="p-button-text p-button-sm p-button-success" 
                                @click="editTemplate(data)"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                class="p-button-text p-button-sm p-button-danger" 
                                @click="deleteTemplate(data)"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.subscription-template-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-pointer:hover {
    text-decoration: underline;
}
</style> 