<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ServiceService } from '@/service/ServiceService';
import { SaleRuleService } from '@/service/SaleRuleService';
import { ServiceAgentService } from '@/service/ServiceAgentService';
import { SubscriptionTemplateService } from '@/service/SubscriptionTemplateService';

const router = useRouter();
const route = useRoute();
const loading = ref(true);
const service = ref(null);
const activeTab = ref(0);
const selectedTemplate = ref(null);

// Данные для вкладок
const saleRules = ref([]);
const serviceAgents = ref([]);
const loadingRules = ref(false);
const loadingAgents = ref(false);

// Модальное окно для создания правила продаж
const showRuleModal = ref(false);
const savingRule = ref(false);
const ruleForm = ref({
    serviceId: '',
    ruleType: 'MONTHLY_RANGE',
    ruleLogic: 'AND',
    startDay: null,
    endDay: null,
    startMonth: null,
    endMonth: null,
    startWeek: null,
    endWeek: null,
    minCardBalance: null,
    maxCardBalance: null,
    isActive: true
});

// Модальное окно для создания связи услуги с агентом
const showServiceAgentModal = ref(false);
const savingServiceAgent = ref(false);
const serviceAgentForm = ref({
    serviceId: '',
    agentId: null,
    agentVersion: 1
});

// Опции для формы
const ruleTypeOptions = ref([]);
const ruleLogicOptions = ref([]);
const agentOptions = ref([]);

onMounted(async () => {
    try {
        const data = await ServiceService.getService(route.params.id);
        service.value = data;
        
        // Загружаем связанный шаблон, если есть
        if (data.templateId) {
            await loadSelectedTemplate(data.templateId);
        }
        
        // Загружаем связанные данные
        await loadSaleRules();
        await loadServiceAgents();
        
        // Загружаем опции для формы правила продаж
        ruleTypeOptions.value = await SaleRuleService.getRuleTypes();
        ruleLogicOptions.value = await SaleRuleService.getRuleLogics();
        
        // Загружаем опции для формы связи услуги с агентом
        agentOptions.value = await ServiceAgentService.getAgents();
    } catch (error) {
        console.error('Ошибка загрузки услуги:', error);
    } finally {
        loading.value = false;
    }
});

// Загрузка выбранного шаблона
const loadSelectedTemplate = async (templateId) => {
    try {
        const template = await SubscriptionTemplateService.getTemplate(templateId);
        selectedTemplate.value = template;
    } catch (error) {
        console.error('Ошибка загрузки шаблона:', error);
    }
};

const loadSaleRules = async () => {
    try {
        loadingRules.value = true;
        const rules = await SaleRuleService.getSaleRules({ serviceId: service.value.id });
        saleRules.value = rules;
    } catch (error) {
        console.error('Ошибка загрузки правил продаж:', error);
    } finally {
        loadingRules.value = false;
    }
};

const loadServiceAgents = async () => {
    try {
        loadingAgents.value = true;
        const agents = await ServiceAgentService.getServiceAgentsByService(service.value.id);
        serviceAgents.value = agents;
    } catch (error) {
        console.error('Ошибка загрузки связей услуги с агентами:', error);
    } finally {
        loadingAgents.value = false;
    }
};

const getAgentName = (agentId) => {
    const agent = agentOptions.value.find(a => a.id === agentId);
    return agent ? agent.name : agentId;
};

const getSubscriptionTypeLabel = (type) => {
    switch (type) {
        case 'WALLET': return 'Кошелек';
        case 'TRAVEL': return 'Поездочный';
        case 'UNLIMITED': return 'Безлимитный';
        default: return type;
    }
};

const getSubscriptionTypeSeverity = (type) => {
    switch (type) {
        case 'WALLET': return 'info';
        case 'TRAVEL': return 'success';
        case 'UNLIMITED': return 'warning';
        default: return 'secondary';
    }
};

const formatCurrency = (amount) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount / 100); // Конвертируем из копеек в рубли
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const editService = () => {
    router.push(`/abt/services/${service.value.id}/edit`);
};

const backToList = () => {
    router.push('/abt/services');
};

// Функции для работы с правилами продаж
const getRuleTypeLabel = (type) => {
    switch (type) {
        case 'MONTHLY_RANGE': return 'Месячный диапазон';
        case 'WEEKLY_RANGE': return 'Недельный диапазон';
        case 'DAILY_RANGE': return 'Дневной диапазон';
        case 'BALANCE_RANGE': return 'Диапазон баланса';
        default: return type;
    }
};

const getRuleLogicLabel = (logic) => {
    switch (logic) {
        case 'AND': return 'И';
        case 'OR': return 'ИЛИ';
        default: return logic;
    }
};

const getRuleDescription = (rule) => {
    switch (rule.ruleType) {
        case 'MONTHLY_RANGE':
            return `Дни месяца: ${rule.startDay} - ${rule.endDay}`;
        case 'WEEKLY_RANGE':
            return `Недели: ${rule.startWeek} - ${rule.endWeek}`;
        case 'DAILY_RANGE':
            return `Дни: ${rule.startDay} - ${rule.endDay}`;
        case 'BALANCE_RANGE':
            return `Баланс: ${formatCurrency(rule.minCardBalance)} - ${formatCurrency(rule.maxCardBalance)}`;
        default:
            return 'Не определено';
    }
};

const deleteRule = async (rule) => {
    try {
        await SaleRuleService.deleteSaleRule(rule.id);
        await loadSaleRules();
    } catch (error) {
        console.error('Ошибка удаления правила:', error);
    }
};

const createRule = () => {
    // Инициализируем форму с данными текущей услуги
    ruleForm.value = {
        serviceId: service.value.id,
        ruleType: 'MONTHLY_RANGE',
        ruleLogic: 'AND',
        startDay: null,
        endDay: null,
        startMonth: null,
        endMonth: null,
        startWeek: null,
        endWeek: null,
        minCardBalance: null,
        maxCardBalance: null,
        isActive: true
    };
    showRuleModal.value = true;
};

const saveRule = async () => {
    try {
        savingRule.value = true;
        await SaleRuleService.createSaleRule(ruleForm.value);
        showRuleModal.value = false;
        await loadSaleRules(); // Перезагружаем список правил
    } catch (error) {
        console.error('Ошибка создания правила:', error);
    } finally {
        savingRule.value = false;
    }
};

const cancelRule = () => {
    showRuleModal.value = false;
};

// Функции для работы с доступностью услуг
const createServiceAgent = () => {
    serviceAgentForm.value = {
        serviceId: service.value.id,
        agentId: null,
        agentVersion: 1
    };
    showServiceAgentModal.value = true;
};

const saveServiceAgent = async () => {
    try {
        savingServiceAgent.value = true;
        await ServiceAgentService.createServiceAgent(serviceAgentForm.value);
        showServiceAgentModal.value = false;
        await loadServiceAgents(); // Перезагружаем список связей
    } catch (error) {
        console.error('Ошибка создания связи услуги с агентом:', error);
    } finally {
        savingServiceAgent.value = false;
    }
};

const cancelServiceAgent = () => {
    showServiceAgentModal.value = false;
};

const deleteServiceAgent = async (serviceAgent) => {
    try {
        await ServiceAgentService.deleteServiceAgent(serviceAgent.id);
        await loadServiceAgents();
    } catch (error) {
        console.error('Ошибка удаления доступности:', error);
    }
};
</script>

<template>
    <div class="service-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Детали услуги</h1>
                <p class="text-color-secondary m-0">Просмотр информации об услуге</p>
            </div>
            <div class="flex gap-2">
                <Button 
                    label="Назад к списку" 
                    icon="pi pi-arrow-left" 
                    @click="backToList"
                    class="p-button-secondary"
                />
                <Button 
                    label="Редактировать" 
                    icon="pi pi-pencil" 
                    @click="editService"
                    class="p-button-success"
                />
            </div>
        </div>

        <div class="card" v-if="!loading && service">
            <!-- Вкладки -->
            <TabView v-model:activeIndex="activeTab">
                <!-- Вкладка "Основная информация" -->
                <TabPanel header="Основная информация">
                    <!-- Основная информация -->
                    <div class="mb-4">
                        <h3>Основная информация</h3>
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Код услуги</label>
                                    <div class="text-lg">{{ service.serviceCode }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Название</label>
                                    <div class="text-lg">{{ service.name }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12" v-if="service.description">
                                <div class="field">
                                    <label class="font-semibold">Описание</label>
                                    <div>{{ service.description }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Тип подписки</label>
                                    <Tag 
                                        :value="getSubscriptionTypeLabel(service.subscriptionType)" 
                                        :severity="getSubscriptionTypeSeverity(service.subscriptionType)" 
                                    />
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Социальная категория</label>
                                    <div>
                                        <i v-if="service.isSocial" class="pi pi-check text-green-500"></i>
                                        <i v-else class="pi pi-times text-red-500"></i>
                                        {{ service.isSocial ? 'Социальная услуга' : 'Обычная услуга' }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Шаблон абонемента</label>
                                    <div v-if="service.templateId" class="text-lg">
                                        {{ service.templateId }}
                                    </div>
                                    <div v-else class="text-color-secondary">
                                        Не указан
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Параметры для абонементов -->
                    <div class="mb-4" v-if="service.subscriptionType !== 'WALLET'">
                        <h3>Параметры абонемента</h3>
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Стоимость</label>
                                    <div class="text-lg">{{ formatCurrency(service.cost) }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Дата начала действия</label>
                                    <div>{{ formatDate(service.actionStartDate) }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Дата окончания действия</label>
                                    <div>{{ formatDate(service.actionEndDate) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Параметры для кошельков -->
                    <div class="mb-4" v-if="service.subscriptionType === 'WALLET'">
                        <h3>Параметры пополнения</h3>
                        <div class="grid">
                            <div class="col-12 md:col-4">
                                <div class="field">
                                    <label class="font-semibold">Минимальная сумма пополнения</label>
                                    <div class="text-lg">{{ formatCurrency(service.minReplenishmentAmount) }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-4">
                                <div class="field">
                                    <label class="font-semibold">Максимальная сумма пополнения</label>
                                    <div class="text-lg">{{ formatCurrency(service.maxReplenishmentAmount) }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-4">
                                <div class="field">
                                    <label class="font-semibold">Рекомендуемая сумма</label>
                                    <div class="text-lg">{{ formatCurrency(service.recommendedAmount) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Техническая информация -->
                    <div class="mb-4">
                        <h3>Техническая информация</h3>
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">ID проекта</label>
                                    <div>{{ service.projectId }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">ID шаблона</label>
                                    <div>{{ service.templateId }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Дата создания</label>
                                    <div>{{ formatDate(service.createdAt) }}</div>
                                </div>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label class="font-semibold">Дата обновления</label>
                                    <div>{{ formatDate(service.updatedAt) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Информация о шаблоне -->
                    <div class="mb-4" v-if="selectedTemplate">
                        <h3>Информация о шаблоне</h3>
                        <div class="p-3 border-round surface-100">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Название шаблона</label>
                                        <div>{{ selectedTemplate.stName }}</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-6">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Тип</label>
                                        <div>{{ selectedTemplate.type }}</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-6">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Социальный</label>
                                        <div>{{ selectedTemplate.isSocial ? 'Да' : 'Нет' }}</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-6">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Срок действия</label>
                                        <div>{{ selectedTemplate.validTimeDays }} дней</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-6">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Активен с</label>
                                        <div>{{ formatDate(selectedTemplate.activeFrom) }}</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-6">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Активен до</label>
                                        <div>{{ formatDate(selectedTemplate.activeTill) }}</div>
                                    </div>
                                </div>
                                <div class="col-12" v-if="selectedTemplate.description">
                                    <div class="field">
                                        <label class="font-semibold text-sm">Описание шаблона</label>
                                        <div>{{ selectedTemplate.description }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </TabPanel>

                <!-- Вкладка "Правила продаж" -->
                <TabPanel header="Правила продаж">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h3>Правила продаж для услуги</h3>
                        <Button 
                            label="Добавить правило" 
                            icon="pi pi-plus" 
                            @click="createRule"
                            class="p-button-success"
                        />
                    </div>

                    <div v-if="loadingRules">
                        <ProgressSpinner />
                    </div>
                    
                    <div v-else-if="saleRules.length === 0">
                        <div class="text-center p-4">
                            <i class="pi pi-info-circle text-4xl text-blue-500 mb-3"></i>
                            <h4>Правила продаж не найдены</h4>
                            <p>Для данной услуги не настроены правила продаж.</p>
                        </div>
                    </div>
                    
                    <div v-else>
                        <DataTable 
                            :value="saleRules" 
                            :paginator="true" 
                            :rows="10" 
                            responsiveLayout="scroll"
                        >
                            <Column field="ruleType" header="Тип правила" :sortable="true">
                                <template #body="{ data }">
                                    <Tag 
                                        :value="getRuleTypeLabel(data.ruleType)" 
                                        severity="info"
                                    />
                                </template>
                            </Column>
                            
                            <Column field="ruleLogic" header="Логика" :sortable="true">
                                <template #body="{ data }">
                                    <Tag 
                                        :value="getRuleLogicLabel(data.ruleLogic)" 
                                        severity="warning"
                                    />
                                </template>
                            </Column>
                            
                            <Column header="Условия" :sortable="false">
                                <template #body="{ data }">
                                    <div class="text-sm">
                                        {{ getRuleDescription(data) }}
                                    </div>
                                </template>
                            </Column>
                            
                            <Column field="isActive" header="Статус" :sortable="true">
                                <template #body="{ data }">
                                    <Tag 
                                        :value="data.isActive ? 'Активно' : 'Неактивно'" 
                                        :severity="data.isActive ? 'success' : 'danger'"
                                    />
                                </template>
                            </Column>
                            
                            <Column header="Действия" :exportable="false" style="min-width:8rem">
                                <template #body="{ data }">
                                    <div class="flex gap-2">
                                        <Button 
                                            icon="pi pi-trash" 
                                            class="p-button-text p-button-sm p-button-danger" 
                                            @click="deleteRule(data)"
                                        />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>
                    </div>
                </TabPanel>

                <!-- Вкладка "Доступность агентам" -->
                <TabPanel header="Доступность агентам">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h3>Доступность услуги для агентов</h3>
                        <Button 
                            label="Добавить агента" 
                            icon="pi pi-plus" 
                            @click="createServiceAgent"
                            class="p-button-success"
                        />
                    </div>

                    <div v-if="loadingAgents">
                        <ProgressSpinner />
                    </div>
                    
                    <div v-else-if="serviceAgents.length === 0">
                        <div class="text-center p-4">
                            <i class="pi pi-info-circle text-4xl text-blue-500 mb-3"></i>
                            <h4>Доступность не настроена</h4>
                            <p>Данная услуга не доступна ни одному агенту.</p>
                        </div>
                    </div>
                    
                    <div v-else>
                        <DataTable 
                            :value="serviceAgents" 
                            :paginator="true" 
                            :rows="10" 
                            responsiveLayout="scroll"
                        >
                            <Column field="agentName" header="Агент" :sortable="true">
                                <template #body="{ data }">
                                    <div class="font-semibold">{{ getAgentName(data.agentId) }}</div>
                                </template>
                            </Column>
                            
                            <Column field="createdAt" header="Дата создания" :sortable="true">
                                <template #body="{ data }">
                                    <div class="text-sm">
                                        {{ formatDate(data.createdAt) }}
                                    </div>
                                </template>
                            </Column>
                            
                            <Column header="Действия" :exportable="false" style="min-width:8rem">
                                <template #body="{ data }">
                                    <div class="flex gap-2">
                                        <Button 
                                            icon="pi pi-trash" 
                                            class="p-button-text p-button-sm p-button-danger" 
                                            @click="deleteServiceAgent(data)"
                                        />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>
                    </div>
                </TabPanel>
            </TabView>
        </div>

        <div class="card" v-else-if="loading">
            <ProgressSpinner />
        </div>

        <div class="card" v-else>
            <div class="text-center">
                <i class="pi pi-exclamation-triangle text-4xl text-orange-500 mb-3"></i>
                <h3>Услуга не найдена</h3>
                <p>Запрашиваемая услуга не существует или была удалена.</p>
                <Button 
                    label="Вернуться к списку" 
                    icon="pi pi-arrow-left" 
                    @click="backToList"
                    class="mt-3"
                />
            </div>
        </div>
    </div>

    <!-- Модальное окно для создания правила продаж -->
    <Dialog 
        v-model:visible="showRuleModal" 
        modal 
        header="Создание правила продаж" 
        :style="{ width: '50rem' }"
        :closable="false"
    >
        <form @submit.prevent="saveRule">
            <!-- Основная информация -->
            <div class="mb-4">
                <h3>Основная информация</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Тип правила *</label>
                        <Dropdown 
                            v-model="ruleForm.ruleType" 
                            :options="ruleTypeOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите тип правила"
                            class="w-full"
                            required
                        />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Логика правила *</label>
                        <Dropdown 
                            v-model="ruleForm.ruleLogic" 
                            :options="ruleLogicOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите логику"
                            class="w-full"
                            required
                        />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Статус</label>
                        <div class="flex align-items-center mt-2">
                            <Checkbox 
                                v-model="ruleForm.isActive" 
                                :binary="true"
                                inputId="isActive"
                            />
                            <label for="isActive" class="ml-2">Активное правило</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Параметры для временных диапазонов -->
            <div class="mb-4" v-if="['MONTHLY_RANGE', 'DAILY_RANGE'].includes(ruleForm.ruleType)">
                <h3>Временные параметры</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Начальный день</label>
                        <InputNumber 
                            v-model="ruleForm.startDay" 
                            :min="1"
                            :max="31"
                            placeholder="1-31"
                            class="w-full"
                        />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Конечный день</label>
                        <InputNumber 
                            v-model="ruleForm.endDay" 
                            :min="1"
                            :max="31"
                            placeholder="1-31"
                            class="w-full"
                        />
                    </div>
                </div>
            </div>

            <!-- Параметры для месячных диапазонов -->
            <div class="mb-4" v-if="ruleForm.ruleType === 'MONTHLY_RANGE'">
                <h3>Месячные параметры</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Начальный месяц</label>
                        <InputNumber 
                            v-model="ruleForm.startMonth" 
                            :min="1"
                            :max="12"
                            placeholder="1-12"
                            class="w-full"
                        />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Конечный месяц</label>
                        <InputNumber 
                            v-model="ruleForm.endMonth" 
                            :min="1"
                            :max="12"
                            placeholder="1-12"
                            class="w-full"
                        />
                    </div>
                </div>
            </div>

            <!-- Параметры для недельных диапазонов -->
            <div class="mb-4" v-if="ruleForm.ruleType === 'WEEKLY_RANGE'">
                <h3>Недельные параметры</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Начальная неделя</label>
                        <InputNumber 
                            v-model="ruleForm.startWeek" 
                            :min="1"
                            :max="53"
                            placeholder="1-53"
                            class="w-full"
                        />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Конечная неделя</label>
                        <InputNumber 
                            v-model="ruleForm.endWeek" 
                            :min="1"
                            :max="53"
                            placeholder="1-53"
                            class="w-full"
                        />
                    </div>
                </div>
            </div>

            <!-- Параметры для диапазона баланса -->
            <div class="mb-4" v-if="ruleForm.ruleType === 'BALANCE_RANGE'">
                <h3>Параметры баланса</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Минимальный баланс карты (руб.)</label>
                        <InputNumber 
                            v-model="ruleForm.minCardBalance" 
                            :minFractionDigits="2"
                            :maxFractionDigits="2"
                            placeholder="0.00"
                            class="w-full"
                            mode="currency"
                            currency="RUB"
                            locale="ru-RU"
                        />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Максимальный баланс карты (руб.)</label>
                        <InputNumber 
                            v-model="ruleForm.maxCardBalance" 
                            :minFractionDigits="2"
                            :maxFractionDigits="2"
                            placeholder="0.00"
                            class="w-full"
                            mode="currency"
                            currency="RUB"
                            locale="ru-RU"
                        />
                    </div>
                </div>
            </div>
        </form>

        <template #footer>
            <div class="flex justify-content-end gap-2">
                <Button 
                    type="button"
                    label="Отмена" 
                    icon="pi pi-times" 
                    @click="cancelRule"
                    class="p-button-secondary"
                />
                <Button 
                    type="submit"
                    label="Создать" 
                    icon="pi pi-check" 
                    @click="saveRule"
                    :loading="savingRule"
                    class="p-button-success"
                />
            </div>
        </template>
    </Dialog>

    <!-- Модальное окно для создания связи услуги с агентом -->
    <Dialog 
        v-model:visible="showServiceAgentModal" 
        modal 
        header="Добавление агента к услуге" 
        :style="{ width: '40rem' }"
        :closable="false"
    >
        <form @submit.prevent="saveServiceAgent">
            <div class="grid">
                <div class="col-12">
                    <label class="font-semibold">Агент *</label>
                    <Dropdown 
                        v-model="serviceAgentForm.agentId" 
                        :options="agentOptions"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Выберите агента"
                        class="w-full"
                        :class="{ 'p-invalid': !serviceAgentForm.agentId }"
                        required
                    />
                    <small v-if="!serviceAgentForm.agentId" class="p-error">Агент обязателен для выбора</small>
                </div>
                

            </div>
        </form>

        <template #footer>
            <div class="flex justify-content-end gap-2">
                <Button 
                    type="button"
                    label="Отмена" 
                    icon="pi pi-times" 
                    @click="cancelServiceAgent"
                    class="p-button-secondary"
                />
                <Button 
                    type="submit"
                    label="Добавить" 
                    icon="pi pi-check" 
                    @click="saveServiceAgent"
                    :loading="savingServiceAgent"
                    class="p-button-success"
                />
            </div>
        </template>
    </Dialog>
</template>

<style scoped>
.service-detail {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color-secondary);
}

.field div {
    color: var(--text-color);
}
</style> 