<script setup>
import { FilterMatchMode, FilterOperator } from '@primevue/core/api';
import { onBeforeMount, reactive, ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import {ProjectService} from "@/service/ProjectService";

const router = useRouter();
const projects = ref(null);
const filters1 = ref(null);
const loading1 = ref(null);
const products = ref(null);
const expandedRows = ref([]);
const statuses = reactive(['ACTIVE']);

onBeforeMount(() => {
    ProjectService.getProjects().then((data) => {
        projects.value = data;
        loading1.value = false;
    })

    initFilters1();
});

function initFilters1() {
    filters1.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        contractNumber: { value: null, matchMode: FilterMatchMode.EQUALS },
        'operatorOrganization.name': { value: null, matchMode: FilterMatchMode.EQUALS }
    };
}

// Вычисляемые свойства для списков фильтров
const contractOptions = computed(() => {
    if (!projects.value) return [];
    const contracts = [...new Set(projects.value.map(p => p.contractNumber))];
    return contracts.map(contract => ({ label: contract, value: contract }));
});

const operatorOptions = computed(() => {
    if (!projects.value) return [];
    const operators = [...new Set(projects.value.map(p => p.operatorOrganization?.name).filter(Boolean))];
    return operators.map(operator => ({ label: operator, value: operator }));
});

// Функция для быстрого фильтра по оператору
const filterByOperator = (operatorName) => {
    if (operatorName) {
        filters1.value['operatorOrganization.name'].value = operatorName;
    }
};

// Функция для очистки фильтра по оператору
const clearOperatorFilter = () => {
    filters1.value['operatorOrganization.name'].value = null;
};

function getSeverity(status) {
    switch (status) {
        case 'unqualified':
            return 'danger';

        case 'ACTIVE':
            return 'success';

        case 'new':
            return 'info';

        case 'negotiation':
            return 'warn';

        case 'renewal':
            return null;
    }
}

function clearFilter() {
    initFilters1();
}

function onRowSelect(event) {
    console.log('Row selected:', event);
    const selectedProject = event.data;
    console.log('Selected project:', selectedProject);

    if (selectedProject && selectedProject.id) {
        console.log('Navigating to:', `/pro/${selectedProject.id}`);
        router.push(`/pro/${selectedProject.id}`);
    } else {
        console.log('No id found in selected project');
    }
}

const createProject = () => {
    router.push('/pro/create');
};

const viewProject = (project) => {
    router.push(`/pro/${project.id}`);
};

const editProject = (project) => {
    router.push(`/pro/${project.id}/edit`);
};

const syncProject = async (project) => {
    if (confirm(`Синхронизировать проект "${project.name}" с договором?`)) {
        try {
            const result = await ProjectService.syncProjectWithContract(project.id);
            console.log('Проект синхронизирован:', result.message);
            // Перезагружаем данные
            ProjectService.getProjects().then((data) => {
                projects.value = data;
            });
        } catch (error) {
            console.error('Ошибка синхронизации:', error);
        }
    }
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'warning';
        case 'expiring': return 'danger';
        case 'completed': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'expiring': return 'Истекает';
        case 'completed': return 'Завершен';
        default: return status;
    }
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatNumber = (number) => {
    return new Intl.NumberFormat('ru-RU').format(number);
};

</script>

<template>
    <div class="pro-main">
        <div class="flex justify-content-between align-items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold m-0 mb-2">СберТройка ПРО</h1>
                <p class="text-xl text-color-secondary m-0">Система управления транспортными картами и платежами</p>
            </div>
        </div>

        <div class="card">
            <div class="flex justify-content-between align-items-center mb-4">
                <h2 class="text-xl font-semibold m-0">Транспортные проекты СберТройка ПРО</h2>
                <Button
                    label="Создать проект"
                    icon="pi pi-plus"
                    @click="createProject"
                />
            </div>
        <DataTable
            :value="projects"
            :paginator="true"
            :rows="10"
            dataKey="id"
            :rowHover="true"
            v-model:filters="filters1"
            filterDisplay="menu"
            :loading="loading1"
            :filters="filters1"
            :globalFilterFields="['name', 'contractNumber', 'operatorOrganization.name']"
            showGridlines
            selectionMode="single"
            @rowSelect="onRowSelect"
        >
            <template #header>
                <div class="flex justify-between">
                    <Button type="button" icon="pi pi-filter-slash" label="Clear" outlined @click="clearFilter()" />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText v-model="filters1['global'].value" placeholder="Keyword Search" />
                    </IconField>
                </div>
            </template>
            <template #empty> No projects found. </template>
            <template #loading> Loading projects data. Please wait. </template>
            <Column field="name" header="Название проекта" :sortable="true" :showFilterMatchModes="false" style="min-width: 250px">
                <template #body="{ data }">
                    <div>
                        <div class="font-semibold">{{ data.name }}</div>
                        <small class="text-color-secondary">{{ data.code }}</small>
                    </div>
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="text" placeholder="Поиск по названию" />
                </template>
            </Column>

            <Column field="contractNumber" header="Договор" :sortable="true" :showFilterMatchModes="false" style="min-width: 200px">
                <template #body="{ data }">
                    <div>
                        <div class="font-mono font-semibold">{{ data.contractNumber }}</div>
                        <small class="text-color-secondary">{{ data.contractName }}</small>
                    </div>
                </template>
                <template #filter="{ filterModel }">
                    <Dropdown
                        v-model="filterModel.value"
                        :options="contractOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите договор"
                        showClear
                        class="w-full"
                    />
                </template>
            </Column>

            <Column field="operatorOrganization.name" header="Оператор" :sortable="true" :showFilterMatchModes="false" style="min-width: 200px">
                <template #body="{ data }">
                    <div class="flex align-items-center">
                        <i class="pi pi-building mr-2 text-color-secondary"></i>
                        <span
                            class="cursor-pointer hover:text-primary"
                            @click="filterByOperator(data.operatorOrganization?.name)"
                            v-tooltip.top="'Нажмите для фильтрации'"
                        >
                            {{ data.operatorOrganization?.name || 'Не указан' }}
                        </span>
                    </div>
                </template>
                <template #filter="{ filterModel }">
                    <div class="flex gap-2">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="operatorOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите оператора"
                            showClear
                            class="flex-1"
                        />
                        <Button
                            icon="pi pi-times"
                            size="small"
                            text
                            @click="clearOperatorFilter"
                            v-tooltip.top="'Очистить фильтр'"
                        />
                    </div>
                </template>
            </Column>

            <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                <template #body="{ data }">
                    <Tag
                        :value="getStatusLabel(data.status)"
                        :severity="getStatusSeverity(data.status)"
                    />
                </template>
            </Column>

            <!-- Убираем колонку "Прогресс" -->

            <Column header="Инфраструктура" style="min-width: 150px">
                <template #body="{ data }">
                    <div class="text-sm">
                        <div>Терминалы: {{ data.activeTerminals }}/{{ data.terminals }}</div>
                        <div>ТС: {{ data.vehicles }}</div>
                        <div>Маршруты: {{ data.routes.length }}</div>
                    </div>
                </template>
            </Column>

            <Column header="Показатели" style="min-width: 180px">
                <template #body="{ data }">
                    <div class="text-sm">
                        <div>{{ formatNumber(data.monthlyTransactions) }} транз./мес</div>
                        <div class="font-semibold">{{ formatAmount(data.monthlyRevenue) }}/мес</div>
                    </div>
                </template>
            </Column>

            <Column header="Действия" style="min-width: 150px">
                <template #body="{ data }">
                    <div class="flex gap-1">
                        <Button
                            icon="pi pi-eye"
                            size="small"
                            text
                            @click="viewProject(data)"
                            v-tooltip.top="'Просмотр'"
                        />
                        <Button
                            icon="pi pi-pencil"
                            size="small"
                            text
                            @click="editProject(data)"
                            v-tooltip.top="'Редактировать'"
                        />
                        <Button
                            icon="pi pi-sync"
                            size="small"
                            text
                            @click="syncProject(data)"
                            v-tooltip.top="'Синхронизировать'"
                        />
                    </div>
                </template>
            </Column>
        </DataTable>
        </div>
    </div>
</template>

<style scoped lang="scss">
.pro-main {
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
}

:deep(.p-datatable-frozen-tbody) {
    font-weight: bold;
}

:deep(.p-datatable-scrollable .p-frozen-column) {
    font-weight: bold;
}
</style>
