import {abtApiClient} from './ApiClient.js';

/**
 * Сервис для работы с абонементами
 */
export class SubscriptionService {

    /**
     * Получить список абонементов
     * @param {Object} filters - Фильтры для поиска
     * @returns {Promise<Object>} Объект с данными и пагинацией
     */
    static async getSubscriptions(filters = {}) {
        try {
            const params = {
                page: filters.page || 0,
                size: filters.size || 20
            };

            if (filters.type) {
                params.type = filters.type;
            }

            if (filters.excludeType) {
                params.excludeType = filters.excludeType;
            }

            if (filters.isSocial !== undefined) {
                params.isSocial = filters.isSocial;
            }

            if (filters.projectId) {
                params.projectId = filters.projectId;
            }

            if (filters.crdId) {
                params.crdId = filters.crdId;
            }

            const response = await abtApiClient.get('/api/v1/abt/subscriptions', params);

            if (response.success) {
                return response.data || { content: [], pagination: {} };
            } else {
                throw new Error(response.message || 'Ошибка получения абонементов');
            }
        } catch (error) {
            console.error('Ошибка получения абонементов:', error);
            throw error;
        }
    }

    /**
     * Получить абонемент по ID
     * @param {string} id - ID абонемента
     * @returns {Promise<Object>} Абонемент
     */
    static async getSubscription(id) {
        try {
            const response = await abtApiClient.get(`/api/v1/abt/subscriptions/${id}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Абонемент не найден');
            }
        } catch (error) {
            console.error('Ошибка получения абонемента:', error);
            throw error;
        }
    }

    /**
     * Получить абонементы по карте
     * @param {string} crdId - ID карты
     * @returns {Promise<Array>} Список абонементов
     */
    static async getSubscriptionsByCard(crdId) {
        try {
            const response = await abtApiClient.get(`/api/v1/abt/subscriptions/by-card/${crdId}`);

            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения абонементов по карте');
            }
        } catch (error) {
            console.error('Ошибка получения абонементов по карте:', error);
            throw error;
        }
    }

    /**
     * Получить абонементы по типу
     * @param {string} type - Тип абонемента (WALLET, TRAVEL, UNLIMITED)
     * @returns {Promise<Array>} Список абонементов
     */
    static async getSubscriptionsByType(type) {
        try {
            const response = await abtApiClient.get(`/api/v1/abt/subscriptions/type/${type}`);

            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения абонементов по типу');
            }
        } catch (error) {
            console.error('Ошибка получения абонементов по типу:', error);
            throw error;
        }
    }

    /**
     * Получить абонементы по социальному статусу
     * @param {boolean} isSocial - Социальный статус
     * @returns {Promise<Array>} Список абонементов
     */
    static async getSubscriptionsBySocialStatus(isSocial) {
        try {
            const response = await abtApiClient.get(`/api/v1/abt/subscriptions/social/${isSocial}`);

            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения абонементов по социальному статусу');
            }
        } catch (error) {
            console.error('Ошибка получения абонементов по социальному статусу:', error);
            throw error;
        }
    }

    /**
     * Получить активные абонементы
     * @returns {Promise<Array>} Список активных абонементов
     */
    static async getActiveSubscriptions() {
        try {
            const response = await abtApiClient.get('/api/v1/abt/subscriptions/active');

            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения активных абонементов');
            }
        } catch (error) {
            console.error('Ошибка получения активных абонементов:', error);
            throw error;
        }
    }

    /**
     * Получить абонементы по проекту
     * @param {string} projectId - ID проекта
     * @returns {Promise<Array>} Список абонементов
     */
    static async getSubscriptionsByProject(projectId) {
        try {
            const response = await abtApiClient.get(`/api/v1/abt/subscriptions/project/${projectId}`);

            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения абонементов по проекту');
            }
        } catch (error) {
            console.error('Ошибка получения абонементов по проекту:', error);
            throw error;
        }
    }

    /**
     * Получить счетчики абонемента
     * @param {string} subscriptionId - ID абонемента
     * @returns {Promise<Array>} Список счетчиков
     */
    static async getSubscriptionCounters(subscriptionId) {
        try {
            const response = await abtApiClient.get(`/api/v1/abt/subscription-counters/subscription/${subscriptionId}`);

            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения счетчиков абонемента');
            }
        } catch (error) {
            console.error('Ошибка получения счетчиков абонемента:', error);
            throw error;
        }
    }

    /**
     * Получить типы абонементов
     * @returns {Promise<Array>} Список типов
     */
    static async getSubscriptionTypes() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'Кошелек', value: 'WALLET' },
                    { label: 'Поездочный', value: 'TRAVEL' },
                    { label: 'Безлимитный', value: 'UNLIMITED' }
                ]);
            }, 100);
        });
    }
}
