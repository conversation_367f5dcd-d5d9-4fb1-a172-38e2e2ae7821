/**
 * Базовый HTTP клиент для работы с REST API
 */
class ApiClient {
    constructor(baseURL = 'http://localhost:8083') {
        this.baseURL = baseURL;
    }

    /**
     * Выполнить HTTP запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} options - Опции запроса
     * @returns {Promise<Object>} Ответ от сервера
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const config = {
            ...defaultOptions,
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    /**
     * GET запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} params - Параметры запроса
     * @returns {Promise<Object>} Ответ от сервера
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }

    /**
     * POST запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} data - Данные для отправки
     * @returns {Promise<Object>} Ответ от сервера
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} data - Данные для отправки
     * @returns {Promise<Object>} Ответ от сервера
     */
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE запрос
     * @param {string} endpoint - Конечная точка API
     * @returns {Promise<Object>} Ответ от сервера
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
}

// Создаем экземпляр клиента для abt-sbol-controller (билетное меню)
export const sbolApiClient = new ApiClient('http://localhost:8083');

// Создаем экземпляр клиента для abt-controller (абонементы, кошельки, шаблоны)
export const abtApiClient = new ApiClient('http://localhost:8086'); 