export const ProjectService = {
    getData: () => {
        return [
            {
                id: 1,
                name: 'СберТройка ПРО г. Москва',
                code: 'ST-PRO-MSK-001',
                projectType: 'transport_system',
                status: 'active',
                startDate: '2024-01-01T00:00:00Z',
                endDate: '2025-12-31T23:59:59Z',
                description: 'Комплексная система транспортных карт и платежей для г. Москва',
                operatorOrganization: {
                    id: 1,
                    name: 'ООО "Городской транспорт"',
                    role: 'operator'
                },
                contractId: 1,
                contractNumber: 'СТ-ПРО-2024-001',
                contractName: 'Правила системы СберТройка ПРО г. Москва',
                totalBudget: 25000000,
                spentBudget: 12500000,
                progress: 75,
                manager: 'Транспортов Транспорт Транспортович',
                routes: ['М1', 'М2', 'М3', 'М5'],
                vehicles: 156,
                terminals: 142,
                activeTerminals: 128,
                monthlyTransactions: 1250000,
                monthlyRevenue: 57500000,
                participantOrganizations: [
                    { id: 1, role: 'operator' },
                    { id: 3, role: 'carrier' },
                    { id: 5, role: 'processing_center' }
                ],
                createdDate: '2023-12-01T09:00:00Z',
                lastSyncDate: '2024-01-20T14:30:00Z',
                syncStatus: 'synced'
            },
            {
                id: 2,
                name: 'СберТройка ПРО Метрополитен',
                code: 'ST-PRO-METRO-001',
                projectType: 'metro_system',
                status: 'active',
                startDate: '2024-01-15T00:00:00Z',
                endDate: '2025-12-31T23:59:59Z',
                description: 'Система оплаты для метрополитена',
                operatorOrganization: {
                    id: 2,
                    name: 'АО "Метрополитен"',
                    role: 'operator'
                },
                contractId: 2,
                contractNumber: 'СТ-ПРО-2024-002',
                contractName: 'Правила системы СберТройка ПРО Метрополитен',
                totalBudget: 18000000,
                spentBudget: 9500000,
                progress: 65,
                manager: 'Метров Станция Подземная',
                routes: ['Сокольническая', 'Замоскворецкая', 'Арбатско-Покровская'],
                vehicles: 89,
                terminals: 67,
                activeTerminals: 65,
                monthlyTransactions: 2100000,
                monthlyRevenue: 134400000,
                participantOrganizations: [
                    { id: 2, role: 'operator' },
                    { id: 5, role: 'processing_center' }
                ],
                createdDate: '2024-01-10T11:30:00Z',
                lastSyncDate: '2024-01-20T14:25:00Z',
                syncStatus: 'synced'
            },
            {
                id: 3,
                name: 'СберТройка ПРО Автобусы',
                code: 'ST-PRO-BUS-001',
                projectType: 'bus_system',
                status: 'expiring',
                startDate: '2023-06-01T00:00:00Z',
                endDate: '2024-05-31T23:59:59Z',
                description: 'Автобусная система оплаты',
                operatorOrganization: {
                    id: 3,
                    name: 'ООО "Автобусный парк №1"',
                    role: 'operator'
                },
                contractId: 3,
                contractNumber: 'СТ-ПРО-2023-045',
                contractName: 'Правила системы СберТройка ПРО Автобусы',
                totalBudget: 12000000,
                spentBudget: 11200000,
                progress: 95,
                manager: 'Автобусов Маршрут Городской',
                routes: ['А1', 'А5', 'А12', 'А25'],
                vehicles: 78,
                terminals: 45,
                activeTerminals: 42,
                monthlyTransactions: 850000,
                monthlyRevenue: 39100000,
                participantOrganizations: [
                    { id: 3, role: 'carrier' },
                    { id: 1, role: 'operator' }
                ],
                createdDate: '2023-05-20T13:15:00Z',
                lastSyncDate: '2024-01-19T16:45:00Z',
                syncStatus: 'synced'
            },
            {
                id: 4,
                name: 'СберТройка ПРО Инновации',
                code: 'ST-PRO-INNOV-001',
                projectType: 'innovation_system',
                status: 'draft',
                startDate: '2024-03-01T00:00:00Z',
                endDate: '2025-02-28T23:59:59Z',
                description: 'Пилотный проект инновационных решений',
                operatorOrganization: {
                    id: 4,
                    name: 'ГУП "Мосгортранс"',
                    role: 'operator'
                },
                contractId: 4,
                contractNumber: 'СТ-ПРО-2024-004',
                contractName: 'Договор на внедрение инноваций',
                totalBudget: 8000000,
                spentBudget: 0,
                progress: 5,
                manager: 'Инновационов Проект Технологический',
                routes: ['И1'],
                vehicles: 12,
                terminals: 8,
                activeTerminals: 0,
                monthlyTransactions: 0,
                monthlyRevenue: 0,
                participantOrganizations: [
                    { id: 4, role: 'contractor' },
                    { id: 1, role: 'customer' }
                ],
                createdDate: '2024-01-25T15:20:00Z',
                lastSyncDate: null,
                syncStatus: 'pending'
            },
            {
                id: 5,
                name: 'СберТройка ПРО Троллейбусы',
                code: 'ST-PRO-TROLL-001',
                projectType: 'transport_system',
                status: 'active',
                startDate: '2023-09-01T00:00:00Z',
                endDate: '2024-08-31T23:59:59Z',
                description: 'Троллейбусная система оплаты',
                operatorOrganization: {
                    id: 5,
                    name: 'ООО "Троллейбусный парк"',
                    role: 'operator'
                },
                contractId: 5,
                contractNumber: 'СТ-ПРО-2023-055',
                contractName: 'Правила системы СберТройка ПРО Троллейбусы',
                totalBudget: 9500000,
                spentBudget: 7200000,
                progress: 85,
                manager: 'Троллейбусов Электрический Токовый',
                routes: ['Т1', 'Т3', 'Т7'],
                vehicles: 45,
                terminals: 32,
                activeTerminals: 30,
                monthlyTransactions: 420000,
                monthlyRevenue: 19320000,
                participantOrganizations: [
                    { id: 5, role: 'operator' },
                    { id: 1, role: 'coordinator' }
                ],
                createdDate: '2023-08-15T10:00:00Z',
                lastSyncDate: '2024-01-20T12:15:00Z',
                syncStatus: 'synced'
            },
            {
                id: 6,
                name: 'СберТройка ПРО Пригород',
                code: 'ST-PRO-SUBURB-001',
                projectType: 'transport_system',
                status: 'active',
                startDate: '2024-02-01T00:00:00Z',
                endDate: '2025-01-31T23:59:59Z',
                description: 'Пригородные маршруты',
                operatorOrganization: {
                    id: 6,
                    name: 'АО "Пригородные перевозки"',
                    role: 'operator'
                },
                contractId: 6,
                contractNumber: 'СТ-ПРО-2024-006',
                contractName: 'Договор пригородного сообщения',
                totalBudget: 22000000,
                spentBudget: 5500000,
                progress: 25,
                manager: 'Пригородов Дальний Маршрутный',
                routes: ['П1', 'П2', 'П5', 'П8'],
                vehicles: 67,
                terminals: 18,
                activeTerminals: 15,
                monthlyTransactions: 180000,
                monthlyRevenue: 9720000,
                participantOrganizations: [
                    { id: 6, role: 'operator' },
                    { id: 2, role: 'partner' }
                ],
                createdDate: '2024-01-20T14:00:00Z',
                lastSyncDate: '2024-01-20T16:30:00Z',
                syncStatus: 'synced'
            },
            {
                id: 7,
                name: 'СберТройка ПРО Экспресс',
                code: 'ST-PRO-EXPRESS-001',
                projectType: 'transport_system',
                status: 'completed',
                startDate: '2023-01-01T00:00:00Z',
                endDate: '2023-12-31T23:59:59Z',
                description: 'Экспресс маршруты',
                operatorOrganization: {
                    id: 1,
                    name: 'ООО "Городской транспорт"',
                    role: 'operator'
                },
                contractId: 7,
                contractNumber: 'СТ-ПРО-2023-007',
                contractName: 'Договор экспресс сообщения',
                totalBudget: 15000000,
                spentBudget: 15000000,
                progress: 100,
                manager: 'Экспрессов Быстрый Скоростной',
                routes: ['Э1', 'Э2'],
                vehicles: 25,
                terminals: 12,
                activeTerminals: 12,
                monthlyTransactions: 95000,
                monthlyRevenue: 6650000,
                participantOrganizations: [
                    { id: 1, role: 'operator' }
                ],
                createdDate: '2022-12-15T09:00:00Z',
                lastSyncDate: '2024-01-01T00:00:00Z',
                syncStatus: 'synced'
            },
            {
                id: 8,
                name: 'СберТройка ПРО Речной транспорт',
                code: 'ST-PRO-RIVER-001',
                projectType: 'transport_system',
                status: 'draft',
                startDate: '2024-05-01T00:00:00Z',
                endDate: '2025-04-30T23:59:59Z',
                description: 'Речные перевозки',
                operatorOrganization: {
                    id: 7,
                    name: 'ООО "Речфлот"',
                    role: 'operator'
                },
                contractId: 8,
                contractNumber: 'СТ-ПРО-2024-008',
                contractName: 'Договор речного сообщения',
                totalBudget: 12000000,
                spentBudget: 0,
                progress: 0,
                manager: 'Речников Водный Плавательный',
                routes: ['Р1', 'Р2', 'Р3'],
                vehicles: 8,
                terminals: 6,
                activeTerminals: 0,
                monthlyTransactions: 0,
                monthlyRevenue: 0,
                participantOrganizations: [
                    { id: 7, role: 'operator' },
                    { id: 1, role: 'coordinator' }
                ],
                createdDate: '2024-01-30T11:00:00Z',
                lastSyncDate: null,
                syncStatus: 'pending'
            }
        ]
    },

    getProjects() {
        return Promise.resolve(this.getData());
    },

    getProjectById(id) {
        const projects = this.getData();
        return Promise.resolve(projects.find(project => project.id === parseInt(id)));
    },

    getProjectsByOrganization(organizationId) {
        const projects = this.getData();
        return Promise.resolve(projects.filter(project => project.organizationId === parseInt(organizationId)));
    },

    getProjectsByStatus(status) {
        const projects = this.getData();
        return Promise.resolve(projects.filter(project => project.status === status));
    },

    createProjectFromContract(contractId, projectData) {
        console.log('Creating project from contract:', contractId, projectData);

        // Находим оператора среди организаций (для примера берем первую)
        const operatorOrganization = {
            id: 1,
            name: 'ООО "Городской транспорт"',
            role: 'operator'
        };

        const newProject = {
            ...projectData,
            id: Date.now(),
            contractId: parseInt(contractId),
            projectType: 'transport_system', // Всегда СберТройка ПРО
            status: 'draft',
            progress: 0,
            // Убираем totalBudget и spentBudget
            terminals: 0,
            activeTerminals: 0,
            vehicles: 0, // Будет считаться автоматически
            monthlyTransactions: 0,
            monthlyRevenue: 0,
            // Добавляем оператора
            operatorOrganization: operatorOrganization,
            participantOrganizations: [operatorOrganization],
            createdDate: new Date().toISOString(),
            lastSyncDate: null,
            syncStatus: 'pending'
        };

        return Promise.resolve(newProject);
    },

    updateProject(id, projectData) {
        console.log('Updating project:', id, projectData);

        const updatedProject = {
            ...projectData,
            id: parseInt(id),
            lastSyncDate: new Date().toISOString()
        };

        return Promise.resolve(updatedProject);
    },

    deleteProject(id) {
        console.log('Deleting project:', id);
        return Promise.resolve({ success: true });
    },

    syncProjectWithContract(projectId) {
        console.log('Syncing project with contract:', projectId);
        return Promise.resolve({
            success: true,
            message: `Проект ${projectId} синхронизирован с договором`,
            syncDate: new Date().toISOString()
        });
    },

    activateProject(id) {
        console.log('Activating project:', id);
        return Promise.resolve({
            success: true,
            message: `Проект ${id} активирован`,
            activationDate: new Date().toISOString()
        });
    },

    completeProject(id) {
        console.log('Completing project:', id);
        return Promise.resolve({
            success: true,
            message: `Проект ${id} завершен`,
            completionDate: new Date().toISOString()
        });
    },

    async getProjectOrganizations(projectId) {
        console.log('Getting organizations for project:', projectId);

        // Получаем проект по ID
        const projects = this.getData();
        const project = projects.find(p => p.id == projectId);

        if (!project || !project.participantOrganizations) {
            return Promise.resolve([]);
        }

        // Получаем ID организаций из проекта
        const organizationIds = project.participantOrganizations.map(org => org.id);

        // Импортируем OrganizationService динамически для избежания циклических зависимостей
        const { OrganizationService } = await import('./OrganizationService.js');

        // Получаем полные данные организаций из OrganizationService
        const organizations = await OrganizationService.getOrganizationsByIds(organizationIds);

        // Объединяем данные организаций с их ролями в проекте
        const organizationsWithRoles = organizations.map(org => {
            const projectOrg = project.participantOrganizations.find(po => po.id === org.id);
            return {
                ...org,
                role: projectOrg ? projectOrg.role : 'unknown'
            };
        });

        return Promise.resolve(organizationsWithRoles);
    }
}
