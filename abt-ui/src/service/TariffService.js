export const TariffService = {
    getData: () => {
        return [
            {
                id: 1,
                name: 'Базовый тариф автобус',
                code: 'BUS_BASE',
                transportType: 'bus',
                fareType: 'single',
                amount: 46.00,
                currency: 'RUB',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-12-31T23:59:59Z',
                status: 'active',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                description: 'Стандартный тариф для разовой поездки на автобусе',
                zones: ['A'],
                discountCategories: [],
                createdDate: '2023-12-01T09:00:00Z'
            },
            {
                id: 2,
                name: 'Льготный тариф студенты',
                code: 'STUDENT_DISCOUNT',
                transportType: 'all',
                fareType: 'single',
                amount: 23.00,
                currency: 'RUB',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-12-31T23:59:59Z',
                status: 'active',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                description: 'Льготный тариф для студентов (50% скидка)',
                zones: ['A', 'B'],
                discountCategories: ['student'],
                createdDate: '2023-12-01T09:00:00Z'
            },
            {
                id: 3,
                name: 'Метро базовый',
                code: 'METRO_BASE',
                transportType: 'metro',
                fareType: 'single',
                amount: 64.00,
                currency: 'RUB',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-12-31T23:59:59Z',
                status: 'active',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                description: 'Базовый тариф для поездки в метрополитене',
                zones: ['METRO'],
                discountCategories: [],
                createdDate: '2023-11-15T10:30:00Z'
            },
            {
                id: 4,
                name: 'Месячный проездной автобус',
                code: 'BUS_MONTHLY',
                transportType: 'bus',
                fareType: 'subscription',
                amount: 2300.00,
                currency: 'RUB',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-12-31T23:59:59Z',
                status: 'active',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                description: 'Месячный проездной билет на автобус',
                zones: ['A', 'B'],
                discountCategories: [],
                createdDate: '2023-12-01T09:00:00Z'
            },
            {
                id: 5,
                name: 'Пенсионный льготный',
                code: 'PENSION_DISCOUNT',
                transportType: 'all',
                fareType: 'single',
                amount: 0.00,
                currency: 'RUB',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-12-31T23:59:59Z',
                status: 'active',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                description: 'Бесплатный проезд для пенсионеров',
                zones: ['A'],
                discountCategories: ['pension'],
                createdDate: '2023-12-01T09:00:00Z'
            },
            {
                id: 6,
                name: 'Экспресс автобус',
                code: 'BUS_EXPRESS',
                transportType: 'bus',
                fareType: 'single',
                amount: 92.00,
                currency: 'RUB',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-12-31T23:59:59Z',
                status: 'draft',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"',
                description: 'Тариф для экспресс-автобусов (повышенный комфорт)',
                zones: ['A', 'B', 'C'],
                discountCategories: [],
                createdDate: '2024-01-05T11:10:00Z'
            }
        ];
    },

    getTariffs() {
        return Promise.resolve(this.getData());
    },

    getTariffById(id) {
        const tariffs = this.getData();
        return Promise.resolve(tariffs.find(tariff => tariff.id === parseInt(id)));
    },

    getTariffsByOrganization(organizationId) {
        const tariffs = this.getData();
        return Promise.resolve(tariffs.filter(tariff => tariff.organizationId === parseInt(organizationId)));
    },

    getTariffsByTransportType(transportType) {
        const tariffs = this.getData();
        return Promise.resolve(tariffs.filter(tariff => 
            tariff.transportType === transportType || tariff.transportType === 'all'
        ));
    },

    createTariff(tariffData) {
        console.log('Creating tariff:', tariffData);
        
        const newTariff = {
            ...tariffData,
            id: Date.now(),
            status: 'draft',
            createdDate: new Date().toISOString()
        };
        
        return Promise.resolve(newTariff);
    },

    updateTariff(id, tariffData) {
        console.log('Updating tariff:', id, tariffData);
        
        const updatedTariff = {
            ...tariffData,
            id: parseInt(id)
        };
        
        return Promise.resolve(updatedTariff);
    },

    deleteTariff(id) {
        console.log('Deleting tariff:', id);
        return Promise.resolve({ success: true });
    },

    activateTariff(id) {
        console.log('Activating tariff:', id);
        return Promise.resolve({ 
            success: true, 
            message: `Тариф ${id} активирован`,
            activationDate: new Date().toISOString()
        });
    },

    deactivateTariff(id) {
        console.log('Deactivating tariff:', id);
        return Promise.resolve({ 
            success: true, 
            message: `Тариф ${id} деактивирован`,
            deactivationDate: new Date().toISOString()
        });
    },

    calculateFare(tariffId, zones, discountCategory) {
        console.log('Calculating fare for tariff:', tariffId, 'zones:', zones, 'discount:', discountCategory);
        
        const tariff = this.getData().find(t => t.id === parseInt(tariffId));
        if (!tariff) {
            return Promise.resolve({ error: 'Тариф не найден' });
        }

        let amount = tariff.amount;
        
        // Применяем скидки
        if (discountCategory && tariff.discountCategories.includes(discountCategory)) {
            switch (discountCategory) {
                case 'student':
                    amount = amount * 0.5; // 50% скидка
                    break;
                case 'pension':
                    amount = 0; // Бесплатно
                    break;
            }
        }

        return Promise.resolve({
            tariffId: tariffId,
            originalAmount: tariff.amount,
            finalAmount: amount,
            discount: tariff.amount - amount,
            discountCategory: discountCategory,
            zones: zones,
            currency: tariff.currency
        });
    }
};
