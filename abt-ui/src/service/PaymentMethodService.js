export const PaymentMethodService = {
    getData: () => {
        return [
            {
                id: 1,
                contractId: 1,
                code: 'BANK_CARD',
                name: '<PERSON>а<PERSON><PERSON>овская карта'
            },
            {
                id: 2,
                contractId: 1,
                code: 'CASH',
                name: 'Наличные денежные средства'
            },
            {
                id: 3,
                contractId: 1,
                code: 'TROIKA_SINGLE',
                name: 'Транспортная карта "Тройка" (разовые поездки)'
            },
            {
                id: 4,
                contractId: 1,
                code: 'TROIKA_SUBSCRIPTION',
                name: 'Транспортная карта "Тройка" (абонемент)'
            },
            {
                id: 5,
                contractId: 1,
                code: 'MPC_DISCOUNT',
                name: 'М<PERSON><PERSON> Дисконт'
            },
            {
                id: 6,
                contractId: 1,
                code: 'MPC_SOCIAL',
                name: '<PERSON><PERSON><PERSON> Социальная карта'
            },
            {
                id: 7,
                contractId: 1,
                code: 'MPC_SCHOOL',
                name: '<PERSON><PERSON><PERSON> "Карта Школьника"'
            },
            {
                id: 8,
                contractId: 1,
                code: 'MPC_STUDENT_SINGLE',
                name: 'МПК "Карта Студента" (разовые поездки)'
            },
            {
                id: 9,
                contractId: 1,
                code: 'MPC_STUDENT_SUBSCRIPTION',
                name: 'МПК "Карта Студента" (абонемент)'
            },
            {
                id: 10,
                contractId: 1,
                code: 'TC_RESIDENT',
                name: 'ТК Карта жителя'
            },
            {
                id: 11,
                contractId: 1,
                code: 'MOBILE_BC',
                name: 'Мобильное приложение БК'
            },
            {
                id: 12,
                contractId: 1,
                code: 'MOBILE_VIRTUAL_TC',
                name: 'Мобильное приложение Виртуальная ТК'
            },
            {
                id: 13,
                contractId: 1,
                code: 'MOBILE_SBP',
                name: 'Мобильное приложение СБП'
            },
            {
                id: 14,
                contractId: 1,
                code: 'REGIONAL_TC',
                name: 'Транспортная карта региона'
            },
            {
                id: 15,
                contractId: 1,
                code: 'SOCIAL_TC',
                name: 'Социальная транспортная карта'
            },
            {
                id: 16,
                contractId: 1,
                code: 'OTHER_CARDS',
                name: 'Иные карты, предусмотренные договором'
            },
            // Способы оплаты для других договоров
            {
                id: 17,
                contractId: 2,
                code: 'BANK_CARD',
                name: 'Банковская карта'
            },
            {
                id: 18,
                contractId: 2,
                code: 'TROIKA_SINGLE',
                name: 'Транспортная карта "Тройка" (разовые поездки)'
            },
            {
                id: 19,
                contractId: 2,
                code: 'MOBILE_SBP',
                name: 'Мобильное приложение СБП'
            },
            {
                id: 20,
                contractId: 3,
                code: 'CASH',
                name: 'Наличные денежные средства'
            },
            {
                id: 21,
                contractId: 3,
                code: 'BANK_CARD',
                name: 'Банковская карта'
            },
            {
                id: 22,
                contractId: 4,
                code: 'BANK_CARD',
                name: 'Банковская карта'
            },
            {
                id: 23,
                contractId: 4,
                code: 'MOBILE_BC',
                name: 'Мобильное приложение БК'
            },
            {
                id: 24,
                contractId: 4,
                code: 'MOBILE_SBP',
                name: 'Мобильное приложение СБП'
            }
        ]
    },

    getPaymentMethods() {
        return Promise.resolve(this.getData());
    },

    getPaymentMethodsByContract(contractId) {
        const methods = this.getData().filter(m => m.contractId == contractId);
        return Promise.resolve(methods);
    },

    getPaymentMethodById(methodId) {
        const methods = this.getData();
        const method = methods.find(m => m.id == methodId);
        return Promise.resolve(method);
    },

    createPaymentMethod(contractId, methodData) {
        console.log('Creating payment method for contract:', contractId, methodData);

        const newMethod = {
            id: Date.now(),
            contractId: contractId,
            code: methodData.code,
            name: methodData.name
        };

        return Promise.resolve(newMethod);
    },

    updatePaymentMethod(methodId, methodData) {
        console.log('Updating payment method:', methodId, methodData);

        const updatedMethod = {
            id: methodId,
            contractId: methodData.contractId,
            code: methodData.code,
            name: methodData.name
        };

        return Promise.resolve(updatedMethod);
    },

    deletePaymentMethod(methodId) {
        console.log('Deleting payment method:', methodId);
        return Promise.resolve({ success: true });
    }
}
