import { sbolApiClient } from './ApiClient.js';

/**
 * Сервис для работы с услугами
 */
export class ServiceService {
    
    /**
     * Получить список услуг
     * @param {Object} filters - Фильтры для поиска
     * @returns {Promise<Array>} Список услуг
     */
    static async getServices(filters = {}) {
        try {
            const params = {
                page: filters.page || 0,
                size: filters.size || 20
            };
            
            const response = await sbolApiClient.get('/api/v1/abt/sbol/services', params);
            
            if (response.success) {
                return response.data.content || response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения услуг');
            }
        } catch (error) {
            console.error('Ошибка получения услуг:', error);
            throw error;
        }
    }
    
    /**
     * Получить услугу по ID
     * @param {string} id - ID услуги
     * @returns {Promise<Object>} Услуга
     */
    static async getService(id) {
        try {
            const response = await sbolApiClient.get(`/api/v1/abt/sbol/services/${id}`);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Услуга не найдена');
            }
        } catch (error) {
            console.error('Ошибка получения услуги:', error);
            throw error;
        }
    }
    
    /**
     * Получить услугу по коду
     * @param {string} serviceCode - Код услуги
     * @returns {Promise<Object>} Услуга
     */
    static async getServiceByCode(serviceCode) {
        try {
            const response = await sbolApiClient.get(`/api/v1/abt/sbol/services/code/${serviceCode}`);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Услуга не найдена');
            }
        } catch (error) {
            console.error('Ошибка получения услуги по коду:', error);
            throw error;
        }
    }
    
    /**
     * Создать новую услугу
     * @param {Object} service - Данные услуги
     * @returns {Promise<Object>} Созданная услуга
     */
    static async createService(service) {
        try {
            const response = await sbolApiClient.post('/api/v1/abt/sbol/services', service);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания услуги');
            }
        } catch (error) {
            console.error('Ошибка создания услуги:', error);
            throw error;
        }
    }
    
    /**
     * Обновить услугу
     * @param {string} id - ID услуги
     * @param {Object} service - Данные услуги
     * @returns {Promise<Object>} Обновленная услуга
     */
    static async updateService(id, service) {
        try {
            const response = await sbolApiClient.put(`/api/v1/abt/sbol/services/${id}`, service);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка обновления услуги');
            }
        } catch (error) {
            console.error('Ошибка обновления услуги:', error);
            throw error;
        }
    }
    
    /**
     * Удалить услугу
     * @param {string} id - ID услуги
     * @returns {Promise<boolean>} Результат удаления
     */
    static async deleteService(id) {
        try {
            const response = await sbolApiClient.delete(`/api/v1/abt/sbol/services/${id}`);
            
            if (response.success) {
                return true;
            } else {
                throw new Error(response.message || 'Ошибка удаления услуги');
            }
        } catch (error) {
            console.error('Ошибка удаления услуги:', error);
            throw error;
        }
    }
    
    /**
     * Получить услуги по проекту
     * @param {string} projectId - ID проекта
     * @returns {Promise<Array>} Список услуг
     */
    static async getServicesByProject(projectId) {
        try {
            const response = await sbolApiClient.get(`/api/v1/abt/sbol/services/project/${projectId}`);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения услуг по проекту');
            }
        } catch (error) {
            console.error('Ошибка получения услуг по проекту:', error);
            throw error;
        }
    }
    
    /**
     * Получить услуги по типу подписки
     * @param {string} subscriptionType - Тип подписки
     * @returns {Promise<Array>} Список услуг
     */
    static async getServicesBySubscriptionType(subscriptionType) {
        try {
            const response = await sbolApiClient.get(`/api/v1/abt/sbol/services/subscription-type/${subscriptionType}`);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения услуг по типу подписки');
            }
        } catch (error) {
            console.error('Ошибка получения услуг по типу подписки:', error);
            throw error;
        }
    }
    
    /**
     * Получить услуги по социальному статусу
     * @param {boolean} isSocial - Социальный статус
     * @returns {Promise<Array>} Список услуг
     */
    static async getServicesByIsSocial(isSocial) {
        try {
            const response = await sbolApiClient.get(`/api/v1/abt/sbol/services/social/${isSocial}`);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения услуг по социальному статусу');
            }
        } catch (error) {
            console.error('Ошибка получения услуг по социальному статусу:', error);
            throw error;
        }
    }
    
    /**
     * Получить типы подписок
     * @returns {Promise<Array>} Список типов
     */
    static async getSubscriptionTypes() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'Кошелек', value: 'WALLET' },
                    { label: 'Поездочный', value: 'TRAVEL' },
                    { label: 'Безлимитный', value: 'UNLIMITED' }
                ]);
            }, 100);
        });
    }
} 