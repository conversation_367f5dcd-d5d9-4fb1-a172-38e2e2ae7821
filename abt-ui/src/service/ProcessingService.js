export const ProcessingService = {
    getData: () => {
        return [
            {
                id: 1,
                transactionId: 'TXN-CASH-001234',
                processingType: 'cash',
                operationType: 'payment',
                amount: 46.00,
                currency: 'RUB',
                status: 'completed',
                timestamp: '2024-01-20T14:30:15Z',
                terminalId: 'TRM-MSK-001',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                paymentDetails: {
                    cashReceived: 50.00,
                    changeGiven: 4.00,
                    denomination: [
                        { value: 50, count: 1 }
                    ]
                },
                createdDate: '2024-01-20T14:30:15Z'
            },
            {
                id: 2,
                transactionId: 'TXN-EMV-001235',
                processingType: 'emv',
                operationType: 'payment',
                amount: 64.00,
                currency: 'RUB',
                status: 'completed',
                timestamp: '2024-01-20T15:45:22Z',
                terminalId: 'TRM-MSK-002',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                paymentDetails: {
                    cardNumber: '****1234',
                    cardType: 'VISA',
                    authCode: 'AUTH123456',
                    rrn: 'RRN123456789012',
                    acquirerBank: 'ПАО Сбербанк',
                    merchantId: 'MERCH001'
                },
                createdDate: '2024-01-20T15:45:22Z'
            },
            {
                id: 3,
                transactionId: 'TXN-CBT-001236',
                processingType: 'cbt',
                operationType: 'payment',
                amount: 46.00,
                currency: 'RUB',
                status: 'completed',
                timestamp: '2024-01-20T16:12:08Z',
                terminalId: 'TRM-MSK-003',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                paymentDetails: {
                    cardNumber: '****5678',
                    cardType: 'Тройка',
                    balanceBefore: 150.00,
                    balanceAfter: 104.00,
                    cardId: 'TROIKA123456789'
                },
                createdDate: '2024-01-20T16:12:08Z'
            },
            {
                id: 4,
                transactionId: 'TXN-ABT-001237',
                processingType: 'abt',
                operationType: 'payment',
                amount: 92.00,
                currency: 'RUB',
                status: 'completed',
                timestamp: '2024-01-20T17:20:33Z',
                terminalId: 'TRM-MSK-004',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"',
                paymentDetails: {
                    phoneNumber: '+7900*****67',
                    operatorCode: 'MTS',
                    accountId: 'ACC789012345',
                    smsConfirmation: true
                },
                createdDate: '2024-01-20T17:20:33Z'
            },
            {
                id: 5,
                transactionId: 'TXN-EMV-001238',
                processingType: 'emv',
                operationType: 'refund',
                amount: -46.00,
                currency: 'RUB',
                status: 'processing',
                timestamp: '2024-01-20T18:05:17Z',
                terminalId: 'TRM-MSK-005',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                paymentDetails: {
                    cardNumber: '****9012',
                    cardType: 'MasterCard',
                    originalTransactionId: 'TXN-EMV-001200',
                    refundReason: 'Отмена поездки'
                },
                createdDate: '2024-01-20T18:05:17Z'
            },
            {
                id: 6,
                transactionId: 'TXN-CASH-001239',
                processingType: 'cash',
                operationType: 'payment',
                amount: 23.00,
                currency: 'RUB',
                status: 'error',
                timestamp: '2024-01-20T18:30:45Z',
                terminalId: 'TRM-MSK-001',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                paymentDetails: {
                    cashReceived: 25.00,
                    changeGiven: 2.00,
                    errorCode: 'CASH_DISPENSER_ERROR',
                    errorMessage: 'Ошибка выдачи сдачи'
                },
                createdDate: '2024-01-20T18:30:45Z'
            }
        ];
    },

    getTransactions() {
        return Promise.resolve(this.getData());
    },

    getTransactionById(id) {
        const transactions = this.getData();
        return Promise.resolve(transactions.find(transaction => transaction.id === parseInt(id)));
    },

    getTransactionsByType(processingType) {
        const transactions = this.getData();
        return Promise.resolve(transactions.filter(transaction => transaction.processingType === processingType));
    },

    getTransactionsByTerminal(terminalId) {
        const transactions = this.getData();
        return Promise.resolve(transactions.filter(transaction => transaction.terminalId === terminalId));
    },

    getTransactionsByOrganization(organizationId) {
        const transactions = this.getData();
        return Promise.resolve(transactions.filter(transaction => transaction.organizationId === parseInt(organizationId)));
    },

    getTransactionsByDateRange(dateFrom, dateTo) {
        const transactions = this.getData();
        return Promise.resolve(transactions.filter(transaction => {
            const txnDate = new Date(transaction.timestamp);
            return txnDate >= new Date(dateFrom) && txnDate <= new Date(dateTo);
        }));
    },

    processPayment(paymentData) {
        console.log('Processing payment:', paymentData);

        const newTransaction = {
            ...paymentData,
            id: Date.now(),
            transactionId: `TXN-${paymentData.processingType.toUpperCase()}-${String(Date.now()).slice(-6)}`,
            status: 'processing',
            timestamp: new Date().toISOString(),
            createdDate: new Date().toISOString()
        };

        // Симуляция обработки
        setTimeout(() => {
            newTransaction.status = Math.random() > 0.1 ? 'completed' : 'error';
        }, 2000);

        return Promise.resolve(newTransaction);
    },

    processRefund(refundData) {
        console.log('Processing refund:', refundData);

        const newTransaction = {
            ...refundData,
            id: Date.now(),
            transactionId: `TXN-${refundData.processingType.toUpperCase()}-${String(Date.now()).slice(-6)}`,
            operationType: 'refund',
            amount: -Math.abs(refundData.amount),
            status: 'processing',
            timestamp: new Date().toISOString(),
            createdDate: new Date().toISOString()
        };

        return Promise.resolve(newTransaction);
    },

    retryTransaction(id) {
        console.log('Retrying transaction:', id);
        return Promise.resolve({
            success: true,
            message: `Транзакция ${id} поставлена в очередь на повторную обработку`,
            retryDate: new Date().toISOString()
        });
    },

    cancelTransaction(id, reason) {
        console.log('Cancelling transaction:', id, 'reason:', reason);
        return Promise.resolve({
            success: true,
            message: `Транзакция ${id} отменена. Причина: ${reason}`,
            cancellationDate: new Date().toISOString()
        });
    },

    getProcessingStatistics(processingType, dateFrom, dateTo) {
        console.log('Getting processing statistics:', { processingType, dateFrom, dateTo });

        const transactions = this.getData().filter(txn =>
            (!processingType || txn.processingType === processingType) &&
            new Date(txn.timestamp) >= new Date(dateFrom) &&
            new Date(txn.timestamp) <= new Date(dateTo)
        );

        const completed = transactions.filter(txn => txn.status === 'completed');
        const errors = transactions.filter(txn => txn.status === 'error');
        const processing = transactions.filter(txn => txn.status === 'processing');

        return Promise.resolve({
            processingType: processingType || 'all',
            dateFrom: dateFrom,
            dateTo: dateTo,
            totalTransactions: transactions.length,
            completedTransactions: completed.length,
            errorTransactions: errors.length,
            processingTransactions: processing.length,
            totalAmount: completed.reduce((sum, txn) => sum + txn.amount, 0),
            successRate: transactions.length > 0 ? (completed.length / transactions.length * 100).toFixed(2) : 0,
            averageAmount: completed.length > 0 ? (completed.reduce((sum, txn) => sum + txn.amount, 0) / completed.length).toFixed(2) : 0
        });
    },

    reconcileTransactions(processingType, date) {
        console.log('Reconciling transactions:', { processingType, date });

        return Promise.resolve({
            processingType: processingType,
            reconciliationDate: date,
            processedDate: new Date().toISOString(),
            totalTransactions: Math.floor(Math.random() * 1000) + 100,
            reconciledTransactions: Math.floor(Math.random() * 950) + 95,
            discrepancies: Math.floor(Math.random() * 5),
            status: 'completed',
            reportUrl: `/reports/reconciliation_${processingType}_${date}.pdf`
        });
    },

    // Генерация мок-данных для платежей проекта
    generateProjectPayments(projectId, count = 2000) {
        const payments = [];
        const paymentTypes = ['cash', 'emv', 'troika'];
        const routes = ['101', '102К', '205', '301', '150'];
        const stations = [
            'Центральный автовокзал', 'Площадь Революции', 'Красная площадь',
            'Невский проспект', 'Дворцовая площадь', 'Вокзальная площадь'
        ];
        const terminals = ['TRM-MSK-001', 'TRM-MSK-002', 'TRM-MSK-003', 'TRM-MSK-004', 'TRM-MSK-005'];
        const carriers = ['ООО "Городской транспорт"', 'АО "Метрополитен"', 'ООО "Автобусный парк №1"'];
        const vehicleTypes = ['автобус', 'троллейбус', 'трамвай', 'метро'];
        const tariffTypes = ['разовый', 'льготный', 'абонемент', 'пересадочный'];
        const cardCategories = ['стандартная', 'золотая', 'платиновая', 'корпоративная'];
        const directions = ['прямое', 'обратное'];
        const drivers = ['Иванов И.И.', 'Петров П.П.', 'Сидоров С.С.', 'Козлов К.К.', 'Морозов М.М.'];

        for (let i = 1; i <= count; i++) {
            const paymentType = paymentTypes[Math.floor(Math.random() * paymentTypes.length)];
            const operationDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Последние 30 дней
            const receivedDate = new Date(operationDate.getTime() + Math.random() * 60 * 60 * 1000); // До 1 часа задержки
            const terminalRegTime = new Date(operationDate.getTime() + Math.random() * 5000); // Время регистрации на терминале
            const serverRegTime = new Date(terminalRegTime.getTime() + Math.random() * 30000); // Время регистрации на сервере

            const ticketCount = Math.floor(Math.random() * 4) + 1; // 1-4 билета
            const ticketPrice = [32, 46, 64, 78][Math.floor(Math.random() * 4)]; // Разные тарифы
            const totalAmount = ticketCount * ticketPrice;
            const route = routes[Math.floor(Math.random() * routes.length)];
            const station = stations[Math.floor(Math.random() * stations.length)];
            const entryStation = stations[Math.floor(Math.random() * stations.length)];
            const exitStation = Math.random() > 0.3 ? stations[Math.floor(Math.random() * stations.length)] : null;
            const entryTime = new Date(operationDate.getTime() - Math.random() * 3600000); // Вход до операции
            const exitTime = exitStation ? new Date(operationDate.getTime() + Math.random() * 3600000) : null;

            const payment = {
                id: i,
                transactionId: `TXN-${paymentType.toUpperCase()}-${String(i).padStart(6, '0')}`,
                projectId: projectId,
                paymentType: paymentType,
                operationType: 'payment',
                amount: totalAmount,
                currency: 'RUB',
                status: Math.random() > 0.05 ? 'completed' : (Math.random() > 0.5 ? 'processing' : 'error'),
                operationDate: operationDate.toISOString(),
                receivedDate: receivedDate.toISOString(),
                terminalRegTime: terminalRegTime.toISOString(),
                serverRegTime: serverRegTime.toISOString(),

                // Основные атрибуты транзакции
                terminalId: terminals[Math.floor(Math.random() * terminals.length)],
                route: route,
                station: station,
                ticketCount: ticketCount,
                ticketPrice: ticketPrice,
                tickets: this.generateTickets(ticketCount, i),

                // Расширенные атрибуты
                tidNspk: paymentType === 'emv' ? `TID${String(Math.floor(Math.random() * 90000000) + 10000000)}` : null,
                cardCategory: paymentType === 'emv' ? cardCategories[Math.floor(Math.random() * cardCategories.length)] : null,
                tariff: ticketPrice,
                bin: paymentType === 'emv' ? String(Math.floor(Math.random() * 900000) + 100000) : null,
                transportBenefitCode: Math.random() > 0.8 ? `TB${Math.floor(Math.random() * 100)}` : null,
                socialBenefitCode: Math.random() > 0.9 ? `SB${Math.floor(Math.random() * 50)}` : null,

                // Транспорт
                vehicleType: vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)],
                licensePlate: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 900) + 100}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
                boardNumber: String(Math.floor(Math.random() * 9000) + 1000),
                tariffType: tariffTypes[Math.floor(Math.random() * tariffTypes.length)],
                carrier: carriers[Math.floor(Math.random() * carriers.length)],
                isTransfer: Math.random() > 0.7,

                // Тройка
                troikaUid: paymentType === 'troika' ? `UID${String(Math.floor(Math.random() * 900000000) + 100000000)}` : null,

                // Маршрут и движение
                direction: directions[Math.floor(Math.random() * directions.length)],
                entryTime: entryTime.toISOString(),
                entryStation: entryStation,
                exitTime: exitTime ? exitTime.toISOString() : null,
                exitStation: exitStation,

                // Рейс и водитель
                tripNumber: `${route}-${String(Math.floor(Math.random() * 100) + 1).padStart(3, '0')}`,
                driver: drivers[Math.floor(Math.random() * drivers.length)],

                // Карта (для EMV)
                cardNumber: paymentType === 'emv' ? `****${Math.floor(Math.random() * 9000) + 1000}` : null,

                // Абонемент
                subscription: Math.random() > 0.85 ? {
                    type: ['месячный', 'недельный', 'студенческий'][Math.floor(Math.random() * 3)],
                    validUntil: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                    ridesLeft: Math.floor(Math.random() * 50) + 1
                } : null,

                // ERN и RRN
                ern: paymentType === 'emv' ? `ERN${String(Math.floor(Math.random() * ************) + ************)}` : null,
                rrn: paymentType === 'emv' ? `RRN${String(Math.floor(Math.random() * ************) + ************)}` : null,

                shift: {
                    id: Math.floor(Math.random() * 100) + 1,
                    startTime: new Date(operationDate.getTime() - Math.random() * 8 * 60 * 60 * 1000).toISOString(),
                    isOpen: Math.random() > 0.1
                }
            };

            // Добавляем специфичные для типа платежа данные
            if (paymentType === 'cash') {
                const cashReceived = totalAmount + [0, 4, 6, 14, 36][Math.floor(Math.random() * 5)]; // Сдача
                payment.paymentDetails = {
                    cashReceived: cashReceived,
                    changeGiven: cashReceived - totalAmount,
                    denomination: this.generateDenomination(cashReceived)
                };
            } else if (paymentType === 'emv') {
                payment.paymentDetails = {
                    cardNumber: `****${Math.floor(Math.random() * 9000) + 1000}`,
                    cardType: ['VISA', 'MASTERCARD', 'МИР'][Math.floor(Math.random() * 3)],
                    authCode: `AUTH${Math.floor(Math.random() * 900000) + 100000}`,
                    rrn: `RRN${Math.floor(Math.random() * ************) + ************}`,
                    acquirerBank: ['ПАО Сбербанк', 'ВТБ', 'Альфа-Банк'][Math.floor(Math.random() * 3)],
                    merchantId: `MERCH${String(Math.floor(Math.random() * 900) + 100).padStart(3, '0')}`
                };
            }

            payments.push(payment);
        }

        return payments.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate));
    },

    generateTickets(count, baseId) {
        const tickets = [];
        const series = ['АА', 'БВ', 'ГД', 'ЕЖ'];

        for (let i = 0; i < count; i++) {
            tickets.push({
                series: series[Math.floor(Math.random() * series.length)],
                number: String(baseId * 10 + i).padStart(8, '0'),
                type: Math.random() > 0.7 ? 'льготный' : 'полный'
            });
        }

        return tickets;
    },

    generateDenomination(amount) {
        const denominations = [5000, 2000, 1000, 500, 200, 100, 50, 10, 5, 1];
        const result = [];
        let remaining = amount;

        for (const denom of denominations) {
            if (remaining >= denom) {
                const count = Math.floor(remaining / denom);
                if (count > 0) {
                    result.push({ value: denom, count: count });
                    remaining -= count * denom;
                }
            }
        }

        return result;
    },

    getProjectPayments(projectId) {
        const payments = this.generateProjectPayments(projectId, 2000);
        return Promise.resolve(payments);
    },

    getProjectPaymentsByType(projectId, paymentType) {
        const allPayments = this.generateProjectPayments(projectId, 2000);
        const filteredPayments = allPayments.filter(payment => payment.paymentType === paymentType);
        return Promise.resolve(filteredPayments);
    },

    // TMS - первичный источник всех транзакций с терминалов
    getTMSTransactions() {
        // TMS получает все транзакции со всех терминалов всех проектов
        const allTransactions = [];

        // Генерируем транзакции для всех проектов
        for (let projectId = 1; projectId <= 8; projectId++) {
            const projectPayments = this.generateProjectPayments(projectId, 250); // По 250 на проект
            allTransactions.push(...projectPayments);
        }

        // TMS видит транзакции в "сыром" виде с дополнительными техническими данными
        const tmsTransactions = allTransactions.map(payment => ({
            ...payment,
            // TMS-специфичные поля
            terminalStatus: Math.random() > 0.95 ? 'offline' : 'online',
            signalStrength: Math.floor(Math.random() * 5) + 1, // 1-5
            batteryLevel: Math.floor(Math.random() * 100) + 1, // 1-100%
            lastHeartbeat: new Date(Date.now() - Math.random() * 300000).toISOString(), // Последние 5 минут
            firmwareVersion: ['v2.1.3', 'v2.1.4', 'v2.2.0'][Math.floor(Math.random() * 3)],
            processingStatus: payment.status === 'completed' ? 'sent_to_processing' : 'pending'
        }));

        return Promise.resolve(tmsTransactions.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate)));
    },

    // CASH - обработанные транзакции наличными
    getTransactionsByType(type) {
        if (type === 'cash') {
            // CASH система получает только транзакции наличными от TMS
            const allTransactions = [];
            for (let projectId = 1; projectId <= 8; projectId++) {
                const projectPayments = this.generateProjectPayments(projectId, 250);
                const cashPayments = projectPayments.filter(p => p.paymentType === 'cash');
                allTransactions.push(...cashPayments);
            }

            return Promise.resolve(allTransactions.map(payment => ({
                ...payment,
                // CASH-специфичные поля
                cashboxStatus: Math.random() > 0.9 ? 'full' : 'normal',
                denominationStatus: Math.random() > 0.95 ? 'low_change' : 'normal',
                lastCollection: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
                collectionRequired: Math.random() > 0.8
            })).sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate)));

        } else if (type === 'emv') {
            // EMV система получает только транзакции картами от TMS
            const allTransactions = [];
            for (let projectId = 1; projectId <= 8; projectId++) {
                const projectPayments = this.generateProjectPayments(projectId, 250);
                const emvPayments = projectPayments.filter(p => p.paymentType === 'emv');
                allTransactions.push(...emvPayments);
            }

            return Promise.resolve(allTransactions.map(payment => ({
                ...payment,
                // EMV-специфичные поля
                acquirerStatus: Math.random() > 0.95 ? 'offline' : 'online',
                settlementStatus: Math.random() > 0.1 ? 'settled' : 'pending',
                lastSettlement: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
                chargeback: Math.random() > 0.99 ? true : false
            })).sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate)));
        }

        return Promise.resolve([]);
    }
};
