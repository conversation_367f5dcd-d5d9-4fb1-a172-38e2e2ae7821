export const CashFlowService = {
    getData: () => {
        return [
            {
                id: 1,
                paymentMethodId: 1,
                name: 'Поступление от банковских карт',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 98.5,
                description: 'Зачисление средств за вычетом комиссии банка',
                isActive: true,
                priority: 1,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 2,
                paymentMethodId: 1,
                name: 'Комиссия банка',
                flowType: 'expense',
                accountCode: '91.02',
                accountName: 'Прочие расходы',
                percentage: 1.5,
                description: 'Комиссия банка за эквайринг',
                isActive: true,
                priority: 2,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 3,
                paymentMethodId: 2,
                name: 'Поступление от СберПэй',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 99.2,
                description: 'Зачисление средств от платежей СберПэй',
                isActive: true,
                priority: 1,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 4,
                paymentMethodId: 2,
                name: 'Комиссия СберПэй',
                flowType: 'expense',
                accountCode: '91.02',
                accountName: 'Прочие расходы',
                percentage: 0.8,
                description: 'Комиссия за обработку платежей СберПэй',
                isActive: true,
                priority: 2,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 5,
                paymentMethodId: 3,
                name: 'Поступление от СБП',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 99.6,
                description: 'Зачисление средств через СБП',
                isActive: true,
                priority: 1,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 6,
                paymentMethodId: 3,
                name: 'Комиссия СБП',
                flowType: 'expense',
                accountCode: '91.02',
                accountName: 'Прочие расходы',
                percentage: 0.4,
                description: 'Комиссия за переводы через СБП',
                isActive: true,
                priority: 2,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 7,
                paymentMethodId: 4,
                name: 'Поступление от транспортных карт',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 100.0,
                description: 'Полное зачисление средств от транспортных карт',
                isActive: true,
                priority: 1,
                createdDate: '2024-01-01T00:00:00Z'
            },
            {
                id: 8,
                paymentMethodId: 5,
                name: 'Поступление от карт в метро',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 98.8,
                description: 'Зачисление средств от банковских карт в метро',
                isActive: true,
                priority: 1,
                createdDate: '2024-02-01T00:00:00Z'
            },
            {
                id: 9,
                paymentMethodId: 5,
                name: 'Комиссия банка (метро)',
                flowType: 'expense',
                accountCode: '91.02',
                accountName: 'Прочие расходы',
                percentage: 1.2,
                description: 'Комиссия банка за эквайринг в метро',
                isActive: true,
                priority: 2,
                createdDate: '2024-02-01T00:00:00Z'
            },
            {
                id: 10,
                paymentMethodId: 6,
                name: 'Поступление от карт Тройка',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 100.0,
                description: 'Полное зачисление средств от карт Тройка',
                isActive: true,
                priority: 1,
                createdDate: '2024-02-01T00:00:00Z'
            },
            {
                id: 11,
                paymentMethodId: 7,
                name: 'Поступление наличных',
                flowType: 'income',
                accountCode: '50.01',
                accountName: 'Касса организации',
                percentage: 100.0,
                description: 'Поступление наличных денежных средств',
                isActive: false,
                priority: 1,
                createdDate: '2023-06-01T00:00:00Z'
            },
            {
                id: 12,
                paymentMethodId: 8,
                name: 'Поступление от карт в автобусах',
                flowType: 'income',
                accountCode: '51.01',
                accountName: 'Расчетные счета в банках',
                percentage: 98.2,
                description: 'Зачисление средств от банковских карт в автобусах',
                isActive: true,
                priority: 1,
                createdDate: '2023-06-01T00:00:00Z'
            },
            {
                id: 13,
                paymentMethodId: 8,
                name: 'Комиссия банка (автобусы)',
                flowType: 'expense',
                accountCode: '91.02',
                accountName: 'Прочие расходы',
                percentage: 1.8,
                description: 'Комиссия банка за эквайринг в автобусах',
                isActive: true,
                priority: 2,
                createdDate: '2023-06-01T00:00:00Z'
            }
        ]
    },

    getCashFlows() {
        return Promise.resolve(this.getData());
    },

    getCashFlowsByPaymentMethod(paymentMethodId) {
        const flows = this.getData().filter(f => f.paymentMethodId == paymentMethodId);
        return Promise.resolve(flows);
    },

    getCashFlowById(flowId) {
        const flows = this.getData();
        const flow = flows.find(f => f.id == flowId);
        return Promise.resolve(flow);
    },

    createCashFlow(paymentMethodId, flowData) {
        console.log('Creating cash flow for payment method:', paymentMethodId, flowData);
        
        const newFlow = {
            ...flowData,
            id: Date.now(),
            paymentMethodId: paymentMethodId,
            createdDate: new Date().toISOString()
        };
        
        return Promise.resolve(newFlow);
    },

    updateCashFlow(flowId, flowData) {
        console.log('Updating cash flow:', flowId, flowData);
        
        const updatedFlow = {
            ...flowData,
            id: flowId
        };
        
        return Promise.resolve(updatedFlow);
    },

    deleteCashFlow(flowId) {
        console.log('Deleting cash flow:', flowId);
        return Promise.resolve({ success: true });
    },

    validateCashFlows(paymentMethodId) {
        // Проверяем, что сумма процентов равна 100%
        const flows = this.getData().filter(f => f.paymentMethodId == paymentMethodId && f.isActive);
        const totalPercentage = flows.reduce((sum, flow) => sum + flow.percentage, 0);
        
        return Promise.resolve({
            isValid: Math.abs(totalPercentage - 100) < 0.01,
            totalPercentage,
            message: totalPercentage === 100 
                ? 'Настройка корректна' 
                : `Сумма процентов составляет ${totalPercentage}%, должна быть 100%`
        });
    }
}
