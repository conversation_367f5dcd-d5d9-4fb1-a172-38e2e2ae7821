export const FiscalService = {
    getData: () => {
        return [
            {
                id: 1,
                operationType: 'sale',
                transactionId: 'TXN-2024-001234',
                fiscalDocumentNumber: 'ФД-001234',
                fiscalSign: 'ФП-567890123',
                amount: 46.00,
                vatAmount: 7.67,
                vatRate: 20,
                currency: 'RUB',
                timestamp: '2024-01-20T14:30:15Z',
                terminalId: 'TRM-MSK-001',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                fiscalRegisterId: 'FR001',
                fiscalRegisterSerial: '0000123456789012',
                status: 'completed',
                fnsStatus: 'sent',
                fnsResponse: 'OK',
                receiptUrl: '/receipts/fd-001234.pdf',
                createdDate: '2024-01-20T14:30:15Z'
            },
            {
                id: 2,
                operationType: 'refund',
                transactionId: 'TXN-2024-001235',
                fiscalDocumentNumber: 'ФД-001235',
                fiscalSign: 'ФП-567890124',
                amount: -23.00,
                vatAmount: -3.83,
                vatRate: 20,
                currency: 'RUB',
                timestamp: '2024-01-20T15:45:22Z',
                terminalId: 'TRM-MSK-002',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                fiscalRegisterId: 'FR002',
                fiscalRegisterSerial: '0000123456789013',
                status: 'completed',
                fnsStatus: 'sent',
                fnsResponse: 'OK',
                receiptUrl: '/receipts/fd-001235.pdf',
                createdDate: '2024-01-20T15:45:22Z'
            },
            {
                id: 3,
                operationType: 'sale',
                transactionId: 'TXN-2024-001236',
                fiscalDocumentNumber: 'ФД-001236',
                fiscalSign: null,
                amount: 64.00,
                vatAmount: 10.67,
                vatRate: 20,
                currency: 'RUB',
                timestamp: '2024-01-20T16:12:08Z',
                terminalId: 'TRM-MSK-003',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                fiscalRegisterId: 'FR003',
                fiscalRegisterSerial: '0000123456789014',
                status: 'processing',
                fnsStatus: 'pending',
                fnsResponse: null,
                receiptUrl: null,
                createdDate: '2024-01-20T16:12:08Z'
            },
            {
                id: 4,
                operationType: 'sale',
                transactionId: 'TXN-2024-001237',
                fiscalDocumentNumber: 'ФД-001237',
                fiscalSign: 'ФП-567890125',
                amount: 92.00,
                vatAmount: 15.33,
                vatRate: 20,
                currency: 'RUB',
                timestamp: '2024-01-20T17:20:33Z',
                terminalId: 'TRM-MSK-004',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"',
                fiscalRegisterId: 'FR004',
                fiscalRegisterSerial: '0000123456789015',
                status: 'completed',
                fnsStatus: 'sent',
                fnsResponse: 'OK',
                receiptUrl: '/receipts/fd-001237.pdf',
                createdDate: '2024-01-20T17:20:33Z'
            },
            {
                id: 5,
                operationType: 'sale',
                transactionId: 'TXN-2024-001238',
                fiscalDocumentNumber: 'ФД-001238',
                fiscalSign: null,
                amount: 46.00,
                vatAmount: 7.67,
                vatRate: 20,
                currency: 'RUB',
                timestamp: '2024-01-20T18:05:17Z',
                terminalId: 'TRM-MSK-005',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                fiscalRegisterId: 'FR005',
                fiscalRegisterSerial: '0000123456789016',
                status: 'error',
                fnsStatus: 'error',
                fnsResponse: 'Ошибка связи с ФНС',
                receiptUrl: null,
                createdDate: '2024-01-20T18:05:17Z'
            }
        ];
    },

    getFiscalOperations() {
        return Promise.resolve(this.getData());
    },

    getFiscalOperationById(id) {
        const operations = this.getData();
        return Promise.resolve(operations.find(operation => operation.id === parseInt(id)));
    },

    getFiscalOperationsByTerminal(terminalId) {
        const operations = this.getData();
        return Promise.resolve(operations.filter(operation => operation.terminalId === terminalId));
    },

    getFiscalOperationsByOrganization(organizationId) {
        const operations = this.getData();
        return Promise.resolve(operations.filter(operation => operation.organizationId === parseInt(organizationId)));
    },

    getFiscalOperationsByDateRange(dateFrom, dateTo) {
        const operations = this.getData();
        return Promise.resolve(operations.filter(operation => {
            const opDate = new Date(operation.timestamp);
            return opDate >= new Date(dateFrom) && opDate <= new Date(dateTo);
        }));
    },

    createFiscalOperation(operationData) {
        console.log('Creating fiscal operation:', operationData);
        
        const newOperation = {
            ...operationData,
            id: Date.now(),
            fiscalDocumentNumber: `ФД-${String(Date.now()).slice(-6)}`,
            fiscalSign: null,
            status: 'processing',
            fnsStatus: 'pending',
            fnsResponse: null,
            receiptUrl: null,
            createdDate: new Date().toISOString()
        };
        
        return Promise.resolve(newOperation);
    },

    retryFiscalOperation(id) {
        console.log('Retrying fiscal operation:', id);
        return Promise.resolve({ 
            success: true, 
            message: `Фискальная операция ${id} поставлена в очередь на повторную обработку`,
            retryDate: new Date().toISOString()
        });
    },

    cancelFiscalOperation(id, reason) {
        console.log('Cancelling fiscal operation:', id, 'reason:', reason);
        return Promise.resolve({ 
            success: true, 
            message: `Фискальная операция ${id} отменена. Причина: ${reason}`,
            cancellationDate: new Date().toISOString()
        });
    },

    getFiscalRegisters() {
        return Promise.resolve([
            {
                id: 'FR001',
                serialNumber: '0000123456789012',
                model: 'АТОЛ 15Ф',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                status: 'active',
                lastOperation: '2024-01-20T14:30:15Z',
                fiscalMemoryExpiry: '2025-12-31T23:59:59Z',
                location: 'Терминал TRM-MSK-001'
            },
            {
                id: 'FR002',
                serialNumber: '0000123456789013',
                model: 'АТОЛ 15Ф',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                status: 'active',
                lastOperation: '2024-01-20T15:45:22Z',
                fiscalMemoryExpiry: '2025-11-30T23:59:59Z',
                location: 'Терминал TRM-MSK-002'
            },
            {
                id: 'FR003',
                serialNumber: '0000123456789014',
                model: 'ШТРИХ-М ФР-К',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                status: 'warning',
                lastOperation: '2024-01-20T16:12:08Z',
                fiscalMemoryExpiry: '2024-03-15T23:59:59Z',
                location: 'Терминал TRM-MSK-003'
            }
        ]);
    },

    getFiscalRegisterById(id) {
        return this.getFiscalRegisters().then(registers => 
            registers.find(register => register.id === id)
        );
    },

    generateFiscalReport(organizationId, reportType, dateFrom, dateTo) {
        console.log('Generating fiscal report:', { organizationId, reportType, dateFrom, dateTo });
        
        return Promise.resolve({
            reportId: `FR_${Date.now()}`,
            organizationId: organizationId,
            reportType: reportType,
            dateFrom: dateFrom,
            dateTo: dateTo,
            generatedDate: new Date().toISOString(),
            totalOperations: Math.floor(Math.random() * 1000) + 100,
            totalAmount: Math.floor(Math.random() * 1000000) + 50000,
            totalVat: Math.floor(Math.random() * 200000) + 8333,
            downloadUrl: `/reports/fiscal_${organizationId}_${reportType}_${dateFrom}_${dateTo}.pdf`,
            status: 'ready'
        });
    },

    sendToFNS(operationIds) {
        console.log('Sending operations to FNS:', operationIds);
        
        return Promise.resolve({
            success: true,
            message: `${operationIds.length} операций отправлено в ФНС`,
            sentDate: new Date().toISOString(),
            operationIds: operationIds
        });
    }
};
