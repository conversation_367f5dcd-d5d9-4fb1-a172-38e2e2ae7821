<script setup>
import { ref } from 'vue';

import AppMenuItem from './AppMenuItem.vue';

const model = ref([
    {
        label: 'Продукты',
        items: [
            { label: 'СберТройка ПРО', icon: 'pi pi-fw pi-star', to: '/pro' },
        ]
    },
    {
        label: 'АС TMS',
        items: [
            { label: 'Терминалы', icon: 'pi pi-fw pi-tablet', to: '/tms/terminals' },
            { label: 'ПО Терминалов', icon: 'pi pi-fw pi-download', to: '/tms/software' },
            { label: 'Конфигурации', icon: 'pi pi-fw pi-cog', to: '/tms/configurations' },
            { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/tms/transactions' },
            { label: 'Телеметрия', icon: 'pi pi-fw pi-chart-line', to: '/tms/telemetry' },
        ]
    },
    {
        label: 'АС ПАСИВ',
        items: [
            { label: 'Организации', icon: 'pi pi-fw pi-building', to: '/organizations' },
            { label: 'Договоры', icon: 'pi pi-fw pi-file-edit', to: '/contracts' },
            { label: 'Тарифы', icon: 'pi pi-fw pi-dollar', to: '/pasiv/tariffs' },
            { label: 'Биллинг', icon: 'pi pi-fw pi-calculator', to: '/pasiv/billing' },
            { label: 'Взаиморасчеты', icon: 'pi pi-fw pi-money-bill', to: '/pasiv/settlements' },
            { label: 'Финансовые отчеты', icon: 'pi pi-fw pi-chart-bar', to: '/pasiv/reports' },
            { label: 'Клиринг', icon: 'pi pi-fw pi-refresh', to: '/pasiv/clearing' },
        ]
    },
    {
        label: 'АС Агент',
        items: [
            { label: 'Реестр агентов', icon: 'pi pi-fw pi-users', to: '/agent/registry' },
            { label: 'Точки обслуживания', icon: 'pi pi-fw pi-map-marker', to: '/agent/service-points' },
            { label: 'Операции агентов', icon: 'pi pi-fw pi-shopping-cart', to: '/agent/operations' },
            { label: 'Отчеты агентов', icon: 'pi pi-fw pi-file', to: '/agent/reports' },
        ]
    },
    {
        label: 'АС CASH',
        items: [
            { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/cash/transactions' },
            { label: 'Билеты (наличные)', icon: 'pi pi-fw pi-ticket', to: '/cash/tickets' },
            { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/cash/statistics' },
        ]
    },
    {
        label: 'АС EMV',
        items: [
            { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/emv/transactions' },
            { label: 'Билеты (банк. карты)', icon: 'pi pi-fw pi-ticket', to: '/emv/tickets' },
            { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/emv/statistics' },
        ]
    },
    {
        label: 'АС CBT',
        items: [
            { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/cbt/transactions' },
            { label: 'Билеты (Тройка)', icon: 'pi pi-fw pi-ticket', to: '/cbt/tickets' },
            { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/cbt/statistics' },
        ]
    },
    {
        label: 'АС ABT',
        items: [
            { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/abt/transactions' },
            { label: 'Билеты (мобильные)', icon: 'pi pi-fw pi-ticket', to: '/abt/tickets' },
            { label: 'Шаблоны абонементов', icon: 'pi pi-fw pi-cog', to: '/abt/templates' },
            { label: 'Абонементы', icon: 'pi pi-fw pi-id-card', to: '/abt/subscriptions' },
            { label: 'Кошельки', icon: 'pi pi-fw pi-wallet', to: '/abt/wallets' },
            { label: 'Билетное меню', icon: 'pi pi-fw pi-shopping-cart', to: '/abt/services' },
            { label: 'Продажи', icon: 'pi pi-fw pi-credit-card', to: '/abt/sales' },
            { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/abt/statistics' },
        ]
    },
    {
        label: 'АС FISCAL',
        items: [
            { label: 'Фискальные операции', icon: 'pi pi-fw pi-receipt', to: '/fiscal/operations' },
            { label: 'Фискальные регистраторы', icon: 'pi pi-fw pi-print', to: '/fiscal/registers' },
            { label: 'Чеки и документы', icon: 'pi pi-fw pi-file-pdf', to: '/fiscal/documents' },
            { label: 'Отчеты ФНС', icon: 'pi pi-fw pi-send', to: '/fiscal/fns-reports' },
        ]
    }
]);
</script>

<template>
    <ul class="layout-menu">
        <template v-for="(item, i) in model" :key="item">
            <app-menu-item v-if="!item.separator" :item="item" :index="i"></app-menu-item>
            <li v-if="item.separator" class="menu-separator"></li>
        </template>
    </ul>
</template>

<style lang="scss" scoped></style>
