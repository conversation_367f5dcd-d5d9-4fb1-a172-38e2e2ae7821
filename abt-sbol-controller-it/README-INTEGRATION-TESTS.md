# Интеграционные тесты для abt-sbol-controller

## Описание

Этот модуль содержит комплексные интеграционные тесты для микросервиса `abt-sbol-controller`. Тесты проверяют функциональность API, создают тестовые данные при их отсутствии и тестируют производительность системы.

## Структура тестов

### 1. DictionaryIntegrationTest
Основной тест для проверки справочников:
- Проверка и создание услуг
- Проверка и создание региональных проектов  
- Проверка и создание правил продаж
- Тестирование CRUD операций
- Проверка пагинации

### 2. SBOLApiIntegrationTest
Тест для проверки SBOL API эндпоинтов:
- Тестирование операций с транспортными картами
- Тестирование операций с банковскими картами
- Тестирование создания счетов
- Тестирование обновления статусов
- Тестирование различных платежных систем и регионов

### 3. DictionaryDataIntegrationTest
Тест для проверки и создания справочных данных:
- Автоматическое создание тестовых данных при их отсутствии
- Проверка консистентности данных
- Тестирование поиска по различным критериям

### 4. PerformanceIntegrationTest
Тест производительности:
- Массовое создание и получение данных
- Конкурентные операции
- Тестирование времени отклика SBOL API
- Тестирование использования памяти

## Предварительные требования

1. **Запущенный abt-sbol-controller** на порту 8083:
   ```bash
   # Убедитесь, что контроллер запущен
   curl http://localhost:8083/actuator/health
   ```

2. **Настроенная база данных** с необходимыми таблицами

3. **Java 17+** и **Gradle**

## Запуск тестов

### Запуск всех тестов
```bash
./gradlew :abt-sbol-controller-it:test
```

### Запуск конкретного теста
```bash
# Тест справочников
./gradlew :abt-sbol-controller-it:test --tests DictionaryIntegrationTest

# Тест SBOL API
./gradlew :abt-sbol-controller-it:test --tests SBOLApiIntegrationTest

# Тест справочных данных
./gradlew :abt-sbol-controller-it:test --tests DictionaryDataIntegrationTest

# Тест производительности
./gradlew :abt-sbol-controller-it:test --tests PerformanceIntegrationTest
```

### Запуск с подробным логированием
```bash
./gradlew :abt-sbol-controller-it:test --info
```

## Конфигурация

### Основные настройки (application.yml)
```yaml
integration:
  controller:
    base-url: http://localhost:8083
    timeout:
      connect: 5000
      read: 30000
    retry:
      max-attempts: 3
      backoff:
        initial: 1000
        multiplier: 2.0
        max-delay: 10000
```

### Переменные окружения
- `CONTROLLER_BASE_URL` - базовый URL контроллера (по умолчанию: http://localhost:8083)

## Что тестируется

### Справочники
- ✅ Создание, чтение, обновление, удаление услуг
- ✅ Создание, чтение, обновление, удаление региональных проектов
- ✅ Создание, чтение, обновление, удаление правил продаж
- ✅ Поиск по различным критериям
- ✅ Пагинация результатов

### SBOL API
- ✅ Операции с транспортными картами
- ✅ Операции с банковскими картами (VISA, MASTERCARD, MIR)
- ✅ Создание счетов для пополнения
- ✅ Создание счетов для покупки услуг
- ✅ Обновление статусов счетов
- ✅ Обработка ошибок и неверных данных

### Производительность
- ✅ Массовые операции
- ✅ Конкурентные запросы
- ✅ Время отклика API
- ✅ Использование памяти
- ✅ Обработка таймаутов

## Автоматическое создание тестовых данных

Тесты автоматически создают необходимые тестовые данные, если они отсутствуют в системе:

1. **Услуги**: Создается минимум 3 услуги с различными типами подписок
2. **Региональные проекты**: Создается минимум 2 проекта для разных регионов
3. **Правила продаж**: Создается минимум 2 правила для разных услуг

## Очистка данных

После выполнения тестов все созданные тестовые данные автоматически удаляются в методах `@AfterAll`.

## Логирование

Тесты используют структурированное логирование с помощью `mu.KotlinLogging`. Уровни логирования:

- `INFO` - основная информация о выполнении тестов
- `ERROR` - ошибки и исключения
- `DEBUG` - детальная отладочная информация

## Мониторинг

### Метрики производительности
Тесты собирают следующие метрики:
- Время создания/получения данных
- Время отклика SBOL API
- Количество успешных/неуспешных операций
- Использование памяти

### Health Check
Перед каждым тестом выполняется проверка здоровья контроллера:
```bash
GET /actuator/health
```

## Устранение неполадок

### Контроллер не отвечает
```bash
# Проверьте, что контроллер запущен
curl http://localhost:8083/actuator/health

# Проверьте логи контроллера
docker logs <container_name>
```

### Ошибки базы данных
```bash
# Проверьте подключение к БД
# Проверьте наличие необходимых таблиц
# Проверьте права доступа
```

### Таймауты
```bash
# Увеличьте таймауты в application.yml
timeout:
  connect: 10000
  read: 60000
```

## Примеры использования

### Создание тестовой услуги
```kotlin
val serviceDto = ServiceDto(
    projectId = UUID.randomUUID(),
    templateId = UUID.randomUUID(),
    serviceCode = "TEST_SERVICE_001",
    name = "Тестовая услуга",
    description = "Описание тестовой услуги",
    isSocial = false,
    subscriptionType = SubscriptionType.TRAVEL,
    cost = 150000L,
    actionStartDate = LocalDateTime.now(),
    actionEndDate = LocalDateTime.now().plusMonths(12),
    minReplenishmentAmount = 10000L,
    maxReplenishmentAmount = 1000000L,
    recommendedAmount = 50000L
)

val service = testDataManager.createTestServiceWithData(serviceDto).block()
```

### Тестирование SBOL API
```kotlin
val response = webClient.get()
    .uri("/sbol/regions/{regionId}/cards/transport/{pan}/available-operations", "47", "2200700160584933")
    .header("X-Agent-ID", "test-agent-123")
    .header("X-Client-Cert", "test-cert-456")
    .retrieve()
    .bodyToMono(String::class.java)
    .timeout(Duration.ofSeconds(10))
    .block()
```

## Поддержка

При возникновении проблем:
1. Проверьте логи тестов
2. Убедитесь, что контроллер запущен и доступен
3. Проверьте конфигурацию базы данных
4. Обратитесь к команде разработки 