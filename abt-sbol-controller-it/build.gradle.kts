import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    kotlin("jvm")
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
    `maven-publish`
}

group = "ru.sbertroika.abt"
version = rootProject.version

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    // Модели и контроллер
    implementation(project(":abt-sbol-model"))
    implementation(project(":abt-sbol-controller"))
    
    // Spring Boot
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    
    // HTTP клиент
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("io.projectreactor.netty:reactor-netty")
    
    // Kotlin
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    
    // Тестирование
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("org.awaitility:awaitility:4.2.0")
    testImplementation("io.projectreactor:reactor-test")
    
    // Логирование
    implementation("io.github.microutils:kotlin-logging-jvm:3.0.5")
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test>().configureEach {
    useJUnitPlatform()
    testLogging {
        showStandardStreams = true
        events("passed", "skipped", "failed")
    }
}

// Настройка публикации в Nexus
publishing {
    publications {
        create<MavenPublication>("maven") {
            from(components["java"])

            pom {
                name.set("ABT SBOL Controller Integration Tests")
                description.set("Integration tests for ABT SBOL Controller")
                url.set("https://github.com/your-org/abt-domain")

                licenses {
                    license {
                        name.set("The Apache License, Version 2.0")
                        url.set("http://www.apache.org/licenses/LICENSE-2.0.txt")
                    }
                }

                developers {
                    developer {
                        id.set("sbertroika")
                        name.set("Sbertroika Team")
                        email.set("<EMAIL>")
                    }
                }
            }
        }
    }

    repositories {
        maven {
            name = "nexusReleases"
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            name = "nexusSnapshots"
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenPassword").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }
}

// Задача для публикации в Nexus
tasks.register("publishToNexus") {
    dependsOn("publishMavenPublicationToNexusReleasesRepository")
    onlyIf {
        !version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Releases")
    }
    group = "publishing"
    description = "Публикует релизную версию в Nexus"
}

tasks.register("publishToNexusSnapshot") {
    dependsOn("publishMavenPublicationToNexusSnapshotsRepository")
    onlyIf {
        version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Snapshots")
    }
    group = "publishing"
    description = "Публикует SNAPSHOT версию в Nexus"
} 