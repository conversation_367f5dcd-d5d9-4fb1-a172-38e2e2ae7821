package ru.sbertroika.abt.sbol.controller.it.data

import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.model.enums.SaleRuleLogic
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

object TestData {

    // Тестовые UUID для консистентности
    val TEST_PROJECT_ID = UUID.fromString("550e8400-e29b-41d4-a716-************")
    val TEST_TEMPLATE_ID = UUID.fromString("550e8400-e29b-41d4-a716-************")
    val TEST_SERVICE_ID = UUID.fromString("550e8400-e29b-41d4-a716-************")

    fun createTestService(): ServiceDto = ServiceDto(
        projectId = TEST_PROJECT_ID,
        templateId = TEST_TEMPLATE_ID,
        serviceCode = "TEST_SERVICE_001",
        name = "Тестовая услуга",
        description = "Описание тестовой услуги",
        isSocial = false,
        subscriptionType = SubscriptionType.TRAVEL,
        cost = 150000L,
        actionStartDate = LocalDate.now(),
        actionEndDate = LocalDate.now().plusMonths(12),
        minReplenishmentAmount = 10000L,
        maxReplenishmentAmount = 1000000L,
        recommendedAmount = 50000L
    )

    fun createTestServiceWithCode(serviceCode: String): ServiceDto = createTestService().copy(
        serviceCode = serviceCode
    )

    fun createTestRegionProject(): RegionProjectDto = RegionProjectDto(
        regionId = "47",
        projectId = TEST_PROJECT_ID,
        canDecodeHash = true,
        panDecodeKey = "test-key-123"
    )

    fun createTestRegionProjectWithRegion(regionId: String): RegionProjectDto = createTestRegionProject().copy(
        regionId = regionId
    )

    fun createTestSaleRule(serviceId: UUID = TEST_SERVICE_ID): SaleRuleDto = SaleRuleDto(
        serviceId = serviceId,
        ruleType = SaleRuleType.MONTHLY_RANGE,
        ruleLogic = SaleRuleLogic.AND,
        startDay = 1,
        endDay = 10,
        startMonth = 1,
        endMonth = 12,
        isActive = true
    )

    fun createTestSaleRuleWithType(ruleType: SaleRuleType, serviceId: UUID = TEST_SERVICE_ID): SaleRuleDto = createTestSaleRule(serviceId).copy(
        ruleType = ruleType
    )

    fun createTestSaleRuleWithLogic(ruleLogic: SaleRuleLogic, serviceId: UUID = TEST_SERVICE_ID): SaleRuleDto = createTestSaleRule(serviceId).copy(
        ruleLogic = ruleLogic
    )

    fun createTestSaleRuleInactive(serviceId: UUID = TEST_SERVICE_ID): SaleRuleDto = createTestSaleRule(serviceId).copy(
        isActive = false
    )

    // Генерация уникальных кодов для тестов
    fun generateUniqueServiceCode(): String = "TEST_SERVICE_${System.currentTimeMillis()}"
    fun generateUniqueRegionId(): String = "TEST_REGION_${System.currentTimeMillis()}"
} 