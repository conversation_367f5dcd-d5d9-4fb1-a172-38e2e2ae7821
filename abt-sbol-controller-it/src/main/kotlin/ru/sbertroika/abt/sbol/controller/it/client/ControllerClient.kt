package ru.sbertroika.abt.sbol.controller.it.client

import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.controller.output.model.ApiResponse
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import ru.sbertroika.abt.sbol.model.entities.Service
import java.time.Duration
import java.util.UUID

@Component
class ControllerClient(
    @Value("\${integration.controller.base-url}")
    private val baseUrl: String,
    @Value("\${integration.controller.timeout.connect}")
    private val connectTimeout: Int,
    @Value("\${integration.controller.timeout.read}")
    private val readTimeout: Int
) {

    private val logger = KotlinLogging.logger {}

    private val webClient: WebClient = WebClient.builder()
        .baseUrl(baseUrl)
        .codecs { configurer ->
            configurer.defaultCodecs().maxInMemorySize(1024 * 1024) // 1MB
        }
        .build()

    // Services API

    fun createService(serviceDto: ServiceDto): Mono<Service> {
        logger.info { "Creating service with code: ${serviceDto.serviceCode}" }
        return webClient.post()
            .uri("/api/v1/services")
            .bodyValue(serviceDto)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as Service }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Service created successfully: ${it.id}" } }
            .doOnError { logger.error(it) { "Failed to create service" } }
    }

    fun getServiceById(id: UUID): Mono<Service?> {
        logger.info { "Getting service by ID: $id" }
        return webClient.get()
            .uri("/api/v1/services/{id}", id)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as Service? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Service retrieved successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to get service by ID: $id" } }
    }

    fun getServiceByCode(serviceCode: String): Mono<Service?> {
        logger.info { "Getting service by code: $serviceCode" }
        return webClient.get()
            .uri("/api/v1/services/code/{serviceCode}", serviceCode)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as Service? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Service retrieved by code successfully: $serviceCode" } }
            .doOnError { logger.error(it) { "Failed to get service by code: $serviceCode" } }
    }

    fun getAllServices(page: Int = 0, size: Int = 20): Mono<PageResponse<Service>> {
        logger.info { "Getting all services, page: $page, size: $size" }
        return webClient.get()
            .uri("/api/v1/services?page=$page&size=$size")
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as PageResponse<Service> }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Services retrieved successfully, total: ${it.totalElements}" } }
            .doOnError { logger.error(it) { "Failed to get services" } }
    }

    fun updateService(id: UUID, serviceDto: ServiceDto): Mono<Service?> {
        logger.info { "Updating service with ID: $id" }
        return webClient.put()
            .uri("/api/v1/services/{id}", id)
            .bodyValue(serviceDto)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as Service? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Service updated successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to update service: $id" } }
    }

    fun deleteService(id: UUID): Mono<Boolean> {
        logger.info { "Deleting service with ID: $id" }
        return webClient.delete()
            .uri("/api/v1/services/{id}", id)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.success }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Service deleted successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to delete service: $id" } }
    }

    // Region Projects API

    fun createRegionProject(regionProjectDto: RegionProjectDto): Mono<RegionProject> {
        logger.info { "Creating region project for region: ${regionProjectDto.regionId}" }
        return webClient.post()
            .uri("/api/v1/region-projects")
            .bodyValue(regionProjectDto)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as RegionProject }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Region project created successfully: ${it.id}" } }
            .doOnError { logger.error(it) { "Failed to create region project" } }
    }

    fun getRegionProjectById(id: UUID): Mono<RegionProject?> {
        logger.info { "Getting region project by ID: $id" }
        return webClient.get()
            .uri("/api/v1/region-projects/{id}", id)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as RegionProject? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Region project retrieved successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to get region project by ID: $id" } }
    }

    fun getAllRegionProjects(page: Int = 0, size: Int = 20): Mono<PageResponse<RegionProject>> {
        logger.info { "Getting all region projects, page: $page, size: $size" }
        return webClient.get()
            .uri("/api/v1/region-projects?page=$page&size=$size")
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as PageResponse<RegionProject> }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Region projects retrieved successfully, total: ${it.totalElements}" } }
            .doOnError { logger.error(it) { "Failed to get region projects" } }
    }

    fun updateRegionProject(id: UUID, regionProjectDto: RegionProjectDto): Mono<RegionProject?> {
        logger.info { "Updating region project with ID: $id" }
        return webClient.put()
            .uri("/api/v1/region-projects/{id}", id)
            .bodyValue(regionProjectDto)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as RegionProject? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Region project updated successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to update region project: $id" } }
    }

    fun deleteRegionProject(id: UUID): Mono<Boolean> {
        logger.info { "Deleting region project with ID: $id" }
        return webClient.delete()
            .uri("/api/v1/region-projects/{id}", id)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.success }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Region project deleted successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to delete region project: $id" } }
    }

    // Sale Rules API

    fun createSaleRule(saleRuleDto: SaleRuleDto): Mono<SaleRule> {
        logger.info { "Creating sale rule for service: ${saleRuleDto.serviceId}" }
        return webClient.post()
            .uri("/api/v1/sale-rules")
            .bodyValue(saleRuleDto)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as SaleRule }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Sale rule created successfully: ${it.id}" } }
            .doOnError { logger.error(it) { "Failed to create sale rule" } }
    }

    fun getSaleRuleById(id: UUID): Mono<SaleRule?> {
        logger.info { "Getting sale rule by ID: $id" }
        return webClient.get()
            .uri("/api/v1/sale-rules/{id}", id)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as SaleRule? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Sale rule retrieved successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to get sale rule by ID: $id" } }
    }

    fun getAllSaleRules(page: Int = 0, size: Int = 20): Mono<PageResponse<SaleRule>> {
        logger.info { "Getting all sale rules, page: $page, size: $size" }
        return webClient.get()
            .uri("/api/v1/sale-rules?page=$page&size=$size")
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as PageResponse<SaleRule> }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Sale rules retrieved successfully, total: ${it.totalElements}" } }
            .doOnError { logger.error(it) { "Failed to get sale rules" } }
    }

    fun updateSaleRule(id: UUID, saleRuleDto: SaleRuleDto): Mono<SaleRule?> {
        logger.info { "Updating sale rule with ID: $id" }
        return webClient.put()
            .uri("/api/v1/sale-rules/{id}", id)
            .bodyValue(saleRuleDto)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.data as SaleRule? }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Sale rule updated successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to update sale rule: $id" } }
    }

    fun deleteSaleRule(id: UUID): Mono<Boolean> {
        logger.info { "Deleting sale rule with ID: $id" }
        return webClient.delete()
            .uri("/api/v1/sale-rules/{id}", id)
            .retrieve()
            .bodyToMono(ApiResponse::class.java)
            .map { it.success }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Sale rule deleted successfully: $id" } }
            .doOnError { logger.error(it) { "Failed to delete sale rule: $id" } }
    }

    // Health check
    fun checkHealth(): Mono<Boolean> {
        logger.info { "Checking controller health" }
        return webClient.get()
            .uri("/actuator/health")
            .retrieve()
            .bodyToMono(String::class.java)
            .map { it.contains("\"status\":\"UP\"") }
            .timeout(Duration.ofMillis(readTimeout.toLong()))
            .doOnSuccess { logger.info { "Health check completed: $it" } }
            .doOnError { logger.error(it) { "Health check failed" } }
    }
} 