server:
  port: 8084

spring:
  application:
    name: abt-sbol-controller-it
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Конфигурация для интеграционных тестов
integration:
  controller:
    base-url: ${CONTROLLER_BASE_URL:http://localhost:8083}
    timeout:
      connect: 5000
      read: 30000
    retry:
      max-attempts: 3
      backoff:
        initial: 1000
        multiplier: 2.0
        max-delay: 10000

logging:
  level:
    ru.sbertroika.abt.sbol.controller.it: DEBUG
    org.springframework.web: DEBUG
    reactor.netty: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n" 