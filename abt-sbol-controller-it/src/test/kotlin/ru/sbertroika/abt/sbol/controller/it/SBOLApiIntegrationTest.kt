package ru.sbertroika.abt.sbol.controller.it

import mu.KotlinLogging
import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.web.reactive.function.client.WebClient
import reactor.test.StepVerifier
import ru.sbertroika.abt.sbol.controller.it.test.TestDataManager
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SBOLApiIntegrationTest {

    @Autowired
    private lateinit var testDataManager: TestDataManager

    private val logger = KotlinLogging.logger {}

    private val webClient = WebClient.builder()
        .baseUrl("http://localhost:8083")
        .build()

    companion object {
        private var testServiceId: UUID? = null
        private var testServiceCode: String = ""
    }

    @Test
    @Order(1)
    fun `should check controller health before SBOL tests`() {
        logger.info { "Проверка здоровья контроллера перед SBOL тестами" }
        
        StepVerifier.create(testDataManager.checkControllerHealth())
            .expectNext(true)
            .verifyComplete()
    }

    @Test
    @Order(2)
    fun `should create test service for SBOL operations`() {
        logger.info { "Создание тестовой услуги для SBOL операций" }
        
        testServiceCode = "SBOL_TEST_${System.currentTimeMillis()}"
        
        val serviceDto = ServiceDto(
            projectId = UUID.randomUUID(),
            templateId = UUID.randomUUID(),
            serviceCode = testServiceCode,
            name = "SBOL Тестовая услуга",
            description = "Тестовая услуга для проверки SBOL интеграции",
            isSocial = false,
            subscriptionType = SubscriptionType.TRAVEL,
            cost = 50000L,
            actionStartDate = LocalDate.now(),
            actionEndDate = LocalDate.now().plusMonths(6),
            minReplenishmentAmount = 5000L,
            maxReplenishmentAmount = 500000L,
            recommendedAmount = 25000L
        )

        StepVerifier.create(testDataManager.createTestServiceWithData(serviceDto))
            .expectNextMatches { service ->
                testServiceId = service.id
                logger.info { "Создана SBOL тестовая услуга с ID: ${service.id}, кодом: ${service.serviceCode}" }
                service.serviceCode == testServiceCode &&
                service.name == "SBOL Тестовая услуга" &&
                service.cost == 50000L
            }
            .verifyComplete()
    }

    @Test
    @Order(3)
    fun `should test transport card available operations endpoint`() {
        logger.info { "Тестирование эндпоинта доступных операций для транспортной карты" }
        
        val pan = "2200700160584933"
        val regionId = "47"
        
        StepVerifier.create(
            webClient.get()
                .uri("/sbol/regions/{regionId}/cards/transport/{pan}/available-operations", regionId, pan)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ от эндпоинта транспортной карты: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(4)
    fun `should test bank card available operations endpoint`() {
        logger.info { "Тестирование эндпоинта доступных операций для банковской карты" }
        
        val panHash = "0009B988BDE0A5B322C0685C5D806D2632C09324"
        val regionId = "47"
        val ipsName = "MIR"
        
        StepVerifier.create(
            webClient.post()
                .uri("/sbol/regions/{regionId}/cards/ips/{ipsName}/{panHash}/available-operations", regionId, ipsName, panHash)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ от эндпоинта банковской карты: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(5)
    fun `should test create invoice endpoint`() {
        logger.info { "Тестирование эндпоинта создания счета" }
        
        val regionId = "47"
        val requestBody = """
            {
                "agentTransactionId": "trx-${System.currentTimeMillis()}",
                "order": {
                    "orderCards": [
                        {
                            "transportCard": {
                                "pan": "2200700160584933"
                            },
                            "replenishment": {
                                "amount": 10000,
                                "type": "VALUE"
                            }
                        }
                    ]
                }
            }
        """.trimIndent()
        
        StepVerifier.create(
            webClient.post()
                .uri("/sbol/regions/{regionId}/invoices/one-click", regionId)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ от эндпоинта создания счета: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(6)
    fun `should test update invoice status endpoint`() {
        logger.info { "Тестирование эндпоинта обновления статуса счета" }
        
        val regionId = "47"
        val invoiceId = "4cdf8fbe-a89f-4862-be83-5e44aab2ef7a"
        val requestBody = """
            {
                "agentTransactionId": "trx-${System.currentTimeMillis()}",
                "invoiceStatus": "PAID"
            }
        """.trimIndent()
        
        StepVerifier.create(
            webClient.post()
                .uri("/sbol/regions/{regionId}/invoices/{invoiceId}/status", regionId, invoiceId)
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ от эндпоинта обновления статуса: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(7)
    fun `should test transport card operations with invalid PAN`() {
        logger.info { "Тестирование эндпоинта транспортной карты с неверным PAN" }
        
        val invalidPan = "0000000000000000"
        val regionId = "47"
        
        StepVerifier.create(
            webClient.get()
                .uri("/sbol/regions/{regionId}/cards/transport/{pan}/available-operations", regionId, invalidPan)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ для неверного PAN: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(8)
    fun `should test bank card operations with invalid hash`() {
        logger.info { "Тестирование эндпоинта банковской карты с неверным хэшем" }
        
        val invalidPanHash = "0000000000000000000000000000000000000000"
        val regionId = "47"
        val ipsName = "VISA"
        
        StepVerifier.create(
            webClient.post()
                .uri("/sbol/regions/{regionId}/cards/ips/{ipsName}/{panHash}/available-operations", regionId, ipsName, invalidPanHash)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ для неверного хэша: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(9)
    fun `should test different payment systems`() {
        logger.info { "Тестирование различных платежных систем" }
        
        val panHash = "0009B988BDE0A5B322C0685C5D806D2632C09324"
        val regionId = "47"
        val paymentSystems = listOf("VISA", "MASTERCARD", "MIR")
        
        paymentSystems.forEach { ipsName ->
            StepVerifier.create(
                webClient.post()
                    .uri("/sbol/regions/{regionId}/cards/ips/{ipsName}/{panHash}/available-operations", regionId, ipsName, panHash)
                    .header("X-Agent-ID", "test-agent-123")
                    .header("X-Client-Cert", "test-cert-456")
                    .retrieve()
                    .bodyToMono(String::class.java)
                    .timeout(Duration.ofSeconds(10))
            )
                .expectNextMatches { response ->
                    logger.info { "Получен ответ для платежной системы $ipsName: $response" }
                    response.isNotEmpty()
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(10)
    fun `should test different regions`() {
        logger.info { "Тестирование различных регионов" }
        
        val pan = "2200700160584933"
        val regions = listOf("47", "78", "77") // Ленинградская область, Москва, Московская область
        
        regions.forEach { regionId ->
            StepVerifier.create(
                webClient.get()
                    .uri("/sbol/regions/{regionId}/cards/transport/{pan}/available-operations", regionId, pan)
                    .header("X-Agent-ID", "test-agent-123")
                    .header("X-Client-Cert", "test-cert-456")
                    .retrieve()
                    .bodyToMono(String::class.java)
                    .timeout(Duration.ofSeconds(10))
            )
                .expectNextMatches { response ->
                    logger.info { "Получен ответ для региона $regionId: $response" }
                    response.isNotEmpty()
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(11)
    fun `should test service purchase scenario`() {
        logger.info { "Тестирование сценария покупки услуги" }
        
        val regionId = "47"
        val requestBody = """
            {
                "agentTransactionId": "trx-service-${System.currentTimeMillis()}",
                "order": {
                    "orderCards": [
                        {
                            "transportCard": {
                                "pan": "2200700160584933"
                            },
                            "purchaseItems": [
                                {
                                    "serviceId": "${testServiceId}",
                                    "usedCounterAmount": 50000
                                }
                            ]
                        }
                    ]
                }
            }
        """.trimIndent()
        
        StepVerifier.create(
            webClient.post()
                .uri("/sbol/regions/{regionId}/invoices/one-click", regionId)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ для покупки услуги: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(12)
    fun `should test bank card service purchase scenario`() {
        logger.info { "Тестирование сценария покупки услуги через банковскую карту" }
        
        val regionId = "47"
        val requestBody = """
            {
                "agentTransactionId": "trx-bank-service-${System.currentTimeMillis()}",
                "order": {
                    "orderCards": [
                        {
                            "ipsCard": {
                                "panHash": "0009B988BDE0A5B322C0685C5D806D2632C09324",
                                "paymentSystem": "MIR"
                            },
                            "purchaseItems": [
                                {
                                    "serviceId": "${testServiceId}",
                                    "usedCounterAmount": 50000
                                }
                            ]
                        }
                    ]
                }
            }
        """.trimIndent()
        
        StepVerifier.create(
            webClient.post()
                .uri("/sbol/regions/{regionId}/invoices/one-click", regionId)
                .header("X-Agent-ID", "test-agent-123")
                .header("X-Client-Cert", "test-cert-456")
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { response ->
                logger.info { "Получен ответ для покупки услуги через банковскую карту: $response" }
                response.isNotEmpty()
            }
            .verifyComplete()
    }

    @AfterAll
    fun cleanup() {
        logger.info { "Очистка тестовых данных SBOL" }
        
        if (testServiceId != null) {
            StepVerifier.create(testDataManager.deleteTestService(testServiceId!!))
                .expectNext(true)
                .verifyComplete()
        }
    }
} 