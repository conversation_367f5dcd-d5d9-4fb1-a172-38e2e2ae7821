package ru.sbertroika.abt.sbol.controller.it

import mu.KotlinLogging
import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import ru.sbertroika.abt.sbol.controller.it.test.TestDataManager
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PerformanceIntegrationTest {

    @Autowired
    private lateinit var testDataManager: TestDataManager

    private val logger = KotlinLogging.logger {}

    private val webClient = WebClient.builder()
        .baseUrl("http://localhost:8083")
        .build()

    companion object {
        private val createdServiceIds = mutableListOf<UUID>()
        private val performanceMetrics = mutableMapOf<String, Long>()
    }

    @Test
    @Order(1)
    fun `should check controller health before performance tests`() {
        logger.info { "Проверка здоровья контроллера перед тестами производительности" }
        
        StepVerifier.create(testDataManager.checkControllerHealth())
            .expectNext(true)
            .verifyComplete()
    }

    @Test
    @Order(2)
    fun `should test bulk service creation performance`() {
        logger.info { "Тестирование производительности массового создания услуг" }
        
        val startTime = System.currentTimeMillis()
        val serviceCount = 10
        
        val services = Flux.range(0, serviceCount)
            .flatMap { index ->
                val serviceCode = "PERF_SERVICE_${System.currentTimeMillis()}_$index"
                val serviceDto = ServiceDto(
                    projectId = UUID.randomUUID(),
                    templateId = UUID.randomUUID(),
                    serviceCode = serviceCode,
                    name = "Тестовая услуга производительности $index",
                    description = "Описание тестовой услуги производительности $index",
                    isSocial = index % 2 == 0,
                    subscriptionType = if (index % 2 == 0) SubscriptionType.TRAVEL else SubscriptionType.WALLET,
                    cost = (10000L + index * 1000L),
                    actionStartDate = LocalDate.now(),
                    actionEndDate = LocalDate.now().plusMonths(6),
                    minReplenishmentAmount = 1000L,
                    maxReplenishmentAmount = 1000000L,
                    recommendedAmount = 50000L
                )
                
                testDataManager.createTestServiceWithData(serviceDto)
                    .doOnSuccess { service ->
                        createdServiceIds.add(service.id!!)
                        logger.info { "Создана услуга производительности: ${service.serviceCode}" }
                    }
            }
            .collectList()
            .block()

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        performanceMetrics["bulk_service_creation"] = duration
        
        logger.info { "Массовое создание $serviceCount услуг заняло ${duration}ms (${duration / serviceCount}ms на услугу)" }
        
        assert(services != null)
        assert(services?.size == serviceCount)
    }

    @Test
    @Order(3)
    fun `should test bulk service retrieval performance`() {
        logger.info { "Тестирование производительности массового получения услуг" }
        
        val startTime = System.currentTimeMillis()
        
        StepVerifier.create(testDataManager.getAllTestServices(0, 100))
            .expectNextMatches { services ->
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                performanceMetrics["bulk_service_retrieval"] = duration
                
                logger.info { "Получение ${services.size} услуг заняло ${duration}ms" }
                services.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(4)
    fun `should test concurrent service operations`() {
        logger.info { "Тестирование конкурентных операций с услугами" }
        
        val startTime = System.currentTimeMillis()
        val operationCount = 5
        val successCounter = AtomicInteger(0)
        
        val operations = Flux.range(0, operationCount)
            .flatMap { index ->
                val serviceCode = "CONC_SERVICE_${System.currentTimeMillis()}_$index"
                val serviceDto = ServiceDto(
                    projectId = UUID.randomUUID(),
                    templateId = UUID.randomUUID(),
                    serviceCode = serviceCode,
                    name = "Конкурентная услуга $index",
                    description = "Описание конкурентной услуги $index",
                    isSocial = false,
                    subscriptionType = SubscriptionType.TRAVEL,
                    cost = 25000L,
                    actionStartDate = LocalDate.now(),
                    actionEndDate = LocalDate.now().plusMonths(3),
                    minReplenishmentAmount = 5000L,
                    maxReplenishmentAmount = 500000L,
                    recommendedAmount = 25000L
                )
                
                testDataManager.createTestServiceWithData(serviceDto)
                    .flatMap { service ->
                        createdServiceIds.add(service.id!!)
                        successCounter.incrementAndGet()
                        logger.info { "Создана конкурентная услуга: ${service.serviceCode}" }
                        
                        // Сразу получаем созданную услугу
                        testDataManager.getTestService(service.id!!)
                    }
                    .onErrorResume { error ->
                        logger.error(error) { "Ошибка при создании конкурентной услуги $index" }
                        Mono.empty()
                    }
            }
            .collectList()
            .block()

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        performanceMetrics["concurrent_service_operations"] = duration
        
        logger.info { "Конкурентные операции с $operationCount услугами заняли ${duration}ms, успешно: ${successCounter.get()}" }
        
        assert(operations != null)
        assert(successCounter.get() > 0)
    }

    @Test
    @Order(5)
    fun `should test SBOL API response time`() {
        logger.info { "Тестирование времени отклика SBOL API" }
        
        val pan = "2200700160584933"
        val regionId = "47"
        val iterations = 5
        
        val responseTimes = mutableListOf<Long>()
        
        repeat(iterations) { index ->
            val startTime = System.currentTimeMillis()
            
            StepVerifier.create(
                webClient.get()
                    .uri("/sbol/regions/{regionId}/cards/transport/{pan}/available-operations", regionId, pan)
                    .header("X-Agent-ID", "perf-test-agent-${index}")
                    .header("X-Client-Cert", "perf-test-cert-${index}")
                    .retrieve()
                    .bodyToMono(String::class.java)
                    .timeout(Duration.ofSeconds(10))
            )
                .expectNextMatches { response ->
                    val endTime = System.currentTimeMillis()
                    val responseTime = endTime - startTime
                    responseTimes.add(responseTime)
                    
                    logger.info { "Итерация $index: время отклика ${responseTime}ms" }
                    response.isNotEmpty()
                }
                .verifyComplete()
        }
        
        val avgResponseTime = responseTimes.average()
        val maxResponseTime = responseTimes.maxOrNull() ?: 0
        val minResponseTime = responseTimes.minOrNull() ?: 0
        
        performanceMetrics["sbol_api_avg_response_time"] = avgResponseTime.toLong()
        performanceMetrics["sbol_api_max_response_time"] = maxResponseTime
        performanceMetrics["sbol_api_min_response_time"] = minResponseTime
        
        logger.info { "SBOL API статистика: среднее=${avgResponseTime.toLong()}ms, максимум=${maxResponseTime}ms, минимум=${minResponseTime}ms" }
    }

    @Test
    @Order(6)
    fun `should test concurrent SBOL API calls`() {
        logger.info { "Тестирование конкурентных вызовов SBOL API" }
        
        val pan = "2200700160584933"
        val regionId = "47"
        val concurrentCalls = 3
        val startTime = System.currentTimeMillis()
        val successCounter = AtomicInteger(0)
        
        val calls = Flux.range(0, concurrentCalls)
            .flatMap { index ->
                webClient.get()
                    .uri("/sbol/regions/{regionId}/cards/transport/{pan}/available-operations", regionId, pan)
                    .header("X-Agent-ID", "concurrent-test-agent-${index}")
                    .header("X-Client-Cert", "concurrent-test-cert-${index}")
                    .retrieve()
                    .bodyToMono(String::class.java)
                    .timeout(Duration.ofSeconds(10))
                    .doOnSuccess { response ->
                        successCounter.incrementAndGet()
                        logger.info { "Конкурентный вызов $index успешен" }
                    }
                    .onErrorResume { error ->
                        logger.error(error) { "Ошибка в конкурентном вызове $index" }
                        Mono.empty()
                    }
            }
            .collectList()
            .block()

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        performanceMetrics["concurrent_sbol_calls"] = duration
        
        logger.info { "Конкурентные вызовы SBOL API: $concurrentCalls вызовов за ${duration}ms, успешно: ${successCounter.get()}" }
        
        assert(calls != null)
        assert(successCounter.get() > 0)
    }

    @Test
    @Order(7)
    fun `should test database query performance`() {
        logger.info { "Тестирование производительности запросов к базе данных" }
        
        // Тест поиска по коду услуги
        val startTime = System.currentTimeMillis()
        
        StepVerifier.create(
            testDataManager.getAllTestServices(0, 1)
                .flatMap { services ->
                    if (services.isNotEmpty()) {
                        testDataManager.getTestServiceByCode(services[0].serviceCode)
                    } else {
                        Mono.empty()
                    }
                }
        )
            .expectNextMatches { service ->
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                performanceMetrics["service_by_code_query"] = duration
                
                logger.info { "Поиск услуги по коду занял ${duration}ms" }
                service != null
            }
            .verifyComplete()
    }

    @Test
    @Order(8)
    fun `should test memory usage under load`() {
        logger.info { "Тестирование использования памяти под нагрузкой" }
        
        val startTime = System.currentTimeMillis()
        val operationCount = 20
        
        val operations = Flux.range(0, operationCount)
            .flatMap { index ->
                // Создаем временную услугу
                val serviceCode = "MEM_TEST_${System.currentTimeMillis()}_$index"
                val serviceDto = ServiceDto(
                    projectId = UUID.randomUUID(),
                    templateId = UUID.randomUUID(),
                    serviceCode = serviceCode,
                    name = "Тест памяти $index",
                    description = "Описание теста памяти $index",
                    isSocial = false,
                    subscriptionType = SubscriptionType.TRAVEL,
                    cost = 15000L,
                    actionStartDate = LocalDate.now(),
                    actionEndDate = LocalDate.now().plusMonths(1),
                    minReplenishmentAmount = 1000L,
                    maxReplenishmentAmount = 100000L,
                    recommendedAmount = 10000L
                )
                
                testDataManager.createTestServiceWithData(serviceDto)
                    .flatMap { service ->
                        // Сразу удаляем для освобождения памяти
                        testDataManager.deleteTestService(service.id!!)
                    }
                    .onErrorResume { error ->
                        logger.error(error) { "Ошибка в тесте памяти $index" }
                        Mono.empty()
                    }
            }
            .collectList()
            .block()

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        performanceMetrics["memory_usage_test"] = duration
        
        logger.info { "Тест использования памяти: $operationCount операций за ${duration}ms" }
        
        assert(operations != null)
    }

    @Test
    @Order(9)
    fun `should test timeout handling`() {
        logger.info { "Тестирование обработки таймаутов" }
        
        val startTime = System.currentTimeMillis()
        
        // Тестируем с очень коротким таймаутом
        StepVerifier.create(
            webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofMillis(100)) // Очень короткий таймаут
        )
            .expectError()
            .verify()

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        performanceMetrics["timeout_handling"] = duration
        
        logger.info { "Тест обработки таймаутов занял ${duration}ms" }
    }

    @Test
    @Order(10)
    fun `should print performance summary`() {
        logger.info { "=== СВОДКА ПРОИЗВОДИТЕЛЬНОСТИ ===" }
        
        performanceMetrics.forEach { (metric, value) ->
            logger.info { "$metric: ${value}ms" }
        }
        
        val totalTime = performanceMetrics.values.sum()
        logger.info { "Общее время тестов производительности: ${totalTime}ms" }
        
        // Проверяем, что все метрики были собраны
        assert(performanceMetrics.isNotEmpty())
        assert(totalTime > 0)
    }

    @AfterAll
    fun cleanup() {
        logger.info { "Очистка тестовых данных производительности" }
        
        // Удаляем созданные услуги
        createdServiceIds.forEach { serviceId ->
            testDataManager.deleteTestService(serviceId)
                .subscribe { success ->
                    if (success) {
                        logger.info { "Удалена тестовая услуга производительности: $serviceId" }
                    }
                }
        }
    }
} 