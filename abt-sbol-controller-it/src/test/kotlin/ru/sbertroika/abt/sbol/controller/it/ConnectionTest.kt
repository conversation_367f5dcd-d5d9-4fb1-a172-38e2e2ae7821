package ru.sbertroika.abt.sbol.controller.it

import org.junit.jupiter.api.Test
import org.springframework.web.reactive.function.client.WebClient
import reactor.test.StepVerifier
import java.time.Duration

class ConnectionTest {

    private val webClient = WebClient.builder()
        .baseUrl("http://localhost:8083")
        .build()

    @Test
    fun `should check if controller is accessible`() {
        StepVerifier.create(
            webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(5))
        )
            .expectNextMatches { response ->
                println("Контроллер доступен: $response")
                response.contains("UP") || response.contains("status")
            }
            .verifyComplete()
    }

    @Test
    fun `should handle controller not available gracefully`() {
        StepVerifier.create(
            webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(5))
                .onErrorResume { error ->
                    println("Контроллер недоступен: ${error.message}")
                    reactor.core.publisher.Mono.empty()
                }
        )
            .expectNextMatches { response ->
                println("Контроллер доступен: $response")
                response.contains("UP") || response.contains("status")
            }
            .verifyComplete()
    }
} 