package ru.sbertroika.abt.sbol.controller.it.test

import mu.KotlinLogging
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.controller.it.client.ControllerClient
import ru.sbertroika.abt.sbol.controller.it.data.TestData
import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import ru.sbertroika.abt.sbol.model.entities.Service as ServiceEntity
import java.util.UUID

@Service
class TestDataManager(
    private val controllerClient: ControllerClient
) {

    // Геттер для доступа к controllerClient из тестов
    fun getControllerClient(): ControllerClient = controllerClient

    private val logger = KotlinLogging.logger {}

    // Services management

    fun createTestService(serviceCode: String = TestData.generateUniqueServiceCode()): Mono<ServiceEntity> {
        logger.info { "Creating test service with code: $serviceCode" }
        val serviceDto = TestData.createTestServiceWithCode(serviceCode)
        return controllerClient.createService(serviceDto)
    }

    fun createTestServiceWithData(serviceDto: ServiceDto): Mono<ServiceEntity> {
        logger.info { "Creating test service with provided data: ${serviceDto.serviceCode}" }
        return controllerClient.createService(serviceDto)
    }

    fun getTestService(id: UUID): Mono<ServiceEntity?> {
        logger.info { "Getting test service by ID: $id" }
        return controllerClient.getServiceById(id)
    }

    fun getTestServiceByCode(serviceCode: String): Mono<ServiceEntity?> {
        logger.info { "Getting test service by code: $serviceCode" }
        return controllerClient.getServiceByCode(serviceCode)
    }

    fun updateTestService(id: UUID, serviceDto: ServiceDto): Mono<ServiceEntity?> {
        logger.info { "Updating test service with ID: $id" }
        return controllerClient.updateService(id, serviceDto)
    }

    fun deleteTestService(id: UUID): Mono<Boolean> {
        logger.info { "Deleting test service with ID: $id" }
        return controllerClient.deleteService(id)
    }

    fun getAllTestServices(page: Int = 0, size: Int = 20): Mono<List<ServiceEntity>> {
        logger.info { "Getting all test services, page: $page, size: $size" }
        return controllerClient.getAllServices(page, size)
            .map { it.content }
    }

    // Region Projects management

    fun createTestRegionProject(regionId: String = TestData.generateUniqueRegionId()): Mono<RegionProject> {
        logger.info { "Creating test region project for region: $regionId" }
        val regionProjectDto = TestData.createTestRegionProjectWithRegion(regionId)
        return controllerClient.createRegionProject(regionProjectDto)
    }

    fun createTestRegionProjectWithData(regionProjectDto: RegionProjectDto): Mono<RegionProject> {
        logger.info { "Creating test region project with provided data: ${regionProjectDto.regionId}" }
        return controllerClient.createRegionProject(regionProjectDto)
    }

    fun getTestRegionProject(id: UUID): Mono<RegionProject?> {
        logger.info { "Getting test region project by ID: $id" }
        return controllerClient.getRegionProjectById(id)
    }

    fun updateTestRegionProject(id: UUID, regionProjectDto: RegionProjectDto): Mono<RegionProject?> {
        logger.info { "Updating test region project with ID: $id" }
        return controllerClient.updateRegionProject(id, regionProjectDto)
    }

    fun deleteTestRegionProject(id: UUID): Mono<Boolean> {
        logger.info { "Deleting test region project with ID: $id" }
        return controllerClient.deleteRegionProject(id)
    }

    fun getAllTestRegionProjects(page: Int = 0, size: Int = 20): Mono<List<RegionProject>> {
        logger.info { "Getting all test region projects, page: $page, size: $size" }
        return controllerClient.getAllRegionProjects(page, size)
            .map { it.content }
    }

    // Sale Rules management

    fun createTestSaleRule(serviceId: UUID = TestData.TEST_SERVICE_ID): Mono<SaleRule> {
        logger.info { "Creating test sale rule for service: $serviceId" }
        val saleRuleDto = TestData.createTestSaleRule(serviceId)
        return controllerClient.createSaleRule(saleRuleDto)
    }

    fun createTestSaleRuleWithData(saleRuleDto: SaleRuleDto): Mono<SaleRule> {
        logger.info { "Creating test sale rule with provided data for service: ${saleRuleDto.serviceId}" }
        return controllerClient.createSaleRule(saleRuleDto)
    }

    fun getTestSaleRule(id: UUID): Mono<SaleRule?> {
        logger.info { "Getting test sale rule by ID: $id" }
        return controllerClient.getSaleRuleById(id)
    }

    fun updateTestSaleRule(id: UUID, saleRuleDto: SaleRuleDto): Mono<SaleRule?> {
        logger.info { "Updating test sale rule with ID: $id" }
        return controllerClient.updateSaleRule(id, saleRuleDto)
    }

    fun deleteTestSaleRule(id: UUID): Mono<Boolean> {
        logger.info { "Deleting test sale rule with ID: $id" }
        return controllerClient.deleteSaleRule(id)
    }

    fun getAllTestSaleRules(page: Int = 0, size: Int = 20): Mono<List<SaleRule>> {
        logger.info { "Getting all test sale rules, page: $page, size: $size" }
        return controllerClient.getAllSaleRules(page, size)
            .map { it.content }
    }

    // Health check

    fun checkControllerHealth(): Mono<Boolean> {
        logger.info { "Checking controller health" }
        return controllerClient.checkHealth()
    }

    // Bulk operations

    fun createTestDataSet(): Mono<TestDataSet> {
        logger.info { "Creating complete test data set" }
        return createTestService()
            .flatMap { service ->
                createTestRegionProject()
                    .flatMap { regionProject ->
                        createTestSaleRule(service.id!!)
                            .map { saleRule ->
                                TestDataSet(service, regionProject, saleRule)
                            }
                    }
            }
    }

    fun cleanupTestData(serviceId: UUID, regionProjectId: UUID, saleRuleId: UUID): Mono<Boolean> {
        logger.info { "Cleaning up test data" }
        return Mono.zip(
            deleteTestService(serviceId),
            deleteTestRegionProject(regionProjectId),
            deleteTestSaleRule(saleRuleId)
        ).map { tuple ->
            tuple.t1 && tuple.t2 && tuple.t3
        }
    }
}

data class TestDataSet(
    val service: ServiceEntity,
    val regionProject: RegionProject,
    val saleRule: SaleRule
) 