package ru.sbertroika.abt.sbol.controller.it

import mu.KotlinLogging
import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier
import ru.sbertroika.abt.sbol.controller.it.test.TestDataManager
import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import ru.sbertroika.abt.sbol.model.entities.Service
import ru.sbertroika.abt.sbol.model.enums.SaleRuleLogic
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DictionaryDataIntegrationTest {

    @Autowired
    private lateinit var testDataManager: TestDataManager

    private val logger = KotlinLogging.logger {}

    companion object {
        private val createdServices = mutableListOf<Service>()
        private val createdRegionProjects = mutableListOf<RegionProject>()
        private val createdSaleRules = mutableListOf<SaleRule>()
    }

    @Test
    @Order(1)
    fun `should check controller health before dictionary tests`() {
        logger.info { "Проверка здоровья контроллера перед тестами справочников" }
        
        StepVerifier.create(testDataManager.checkControllerHealth())
            .expectNext(true)
            .verifyComplete()
    }

    @Test
    @Order(2)
    fun `should check existing services and create if needed`() {
        logger.info { "Проверка существующих услуг и создание при необходимости" }
        
        // Получаем все существующие услуги
        StepVerifier.create(testDataManager.getAllTestServices(0, 100))
            .expectNextMatches { services ->
                logger.info { "Найдено ${services.size} существующих услуг" }
                
                // Если услуг меньше 3, создаем дополнительные
                if (services.size < 3) {
                    val servicesToCreate = 3 - services.size
                    logger.info { "Создаем $servicesToCreate дополнительных услуг" }
                    
                    repeat(servicesToCreate) { index ->
                        val serviceCode = "DICT_SERVICE_${System.currentTimeMillis()}_$index"
                        val serviceDto = ServiceDto(
                            projectId = UUID.randomUUID(),
                            templateId = UUID.randomUUID(),
                            serviceCode = serviceCode,
                            name = "Справочная услуга $index",
                            description = "Описание справочной услуги $index",
                            isSocial = index % 2 == 0,
                            subscriptionType = if (index % 2 == 0) SubscriptionType.TRAVEL else SubscriptionType.WALLET,
                            cost = (10000L + index * 5000L),
                            actionStartDate = LocalDate.now(),
                            actionEndDate = LocalDate.now().plusMonths(12),
                            minReplenishmentAmount = 1000L,
                            maxReplenishmentAmount = 1000000L,
                            recommendedAmount = 50000L
                        )
                        
                        testDataManager.createTestServiceWithData(serviceDto)
                            .subscribe { service ->
                                createdServices.add(service)
                                logger.info { "Создана справочная услуга: ${service.serviceCode}" }
                            }
                    }
                }
                
                true
            }
            .verifyComplete()
    }

    @Test
    @Order(3)
    fun `should check existing region projects and create if needed`() {
        logger.info { "Проверка существующих региональных проектов и создание при необходимости" }
        
        // Получаем все существующие региональные проекты
        StepVerifier.create(testDataManager.getAllTestRegionProjects(0, 100))
            .expectNextMatches { regionProjects ->
                logger.info { "Найдено ${regionProjects.size} существующих региональных проектов" }
                
                // Если проектов меньше 2, создаем дополнительные
                if (regionProjects.size < 2) {
                    val projectsToCreate = 2 - regionProjects.size
                    logger.info { "Создаем $projectsToCreate дополнительных региональных проектов" }
                    
                    val regions = listOf("47", "78", "77") // Ленинградская область, Москва, Московская область
                    
                    repeat(projectsToCreate) { index ->
                        val regionId = regions.getOrNull(index) ?: "TEST_REGION_$index"
                        val regionProjectDto = RegionProjectDto(
                            regionId = regionId,
                            projectId = UUID.randomUUID(),
                            canDecodeHash = index % 2 == 0,
                            panDecodeKey = "dict-key-${System.currentTimeMillis()}-$index"
                        )
                        
                        testDataManager.createTestRegionProjectWithData(regionProjectDto)
                            .subscribe { regionProject ->
                                createdRegionProjects.add(regionProject)
                                logger.info { "Создан справочный региональный проект: ${regionProject.regionId}" }
                            }
                    }
                }
                
                true
            }
            .verifyComplete()
    }

    @Test
    @Order(4)
    fun `should check existing sale rules and create if needed`() {
        logger.info { "Проверка существующих правил продаж и создание при необходимости" }
        
        // Получаем все существующие правила продаж
        StepVerifier.create(testDataManager.getAllTestSaleRules(0, 100))
            .expectNextMatches { saleRules ->
                logger.info { "Найдено ${saleRules.size} существующих правил продаж" }
                
                // Если правил меньше 2, создаем дополнительные
                if (saleRules.size < 2) {
                    val rulesToCreate = 2 - saleRules.size
                    logger.info { "Создаем $rulesToCreate дополнительных правил продаж" }
                    
                    // Сначала получаем существующие услуги для создания правил
                    testDataManager.getAllTestServices(0, 10)
                        .subscribe { services ->
                            if (services.isNotEmpty()) {
                                repeat(rulesToCreate) { index ->
                                    val serviceId = services[index % services.size].id
                                    val saleRuleDto = SaleRuleDto(
                                        serviceId = serviceId!!,
                                        ruleType = if (index % 2 == 0) SaleRuleType.MONTHLY_RANGE else SaleRuleType.DAILY_RANGE,
                                        ruleLogic = if (index % 2 == 0) SaleRuleLogic.AND else SaleRuleLogic.OR,
                                        startDay = 1 + index,
                                        endDay = 10 + index,
                                        startMonth = 1 + (index % 6),
                                        endMonth = 6 + (index % 6),
                                        isActive = index % 2 == 0
                                    )
                                    
                                    testDataManager.createTestSaleRuleWithData(saleRuleDto)
                                        .subscribe { saleRule ->
                                            createdSaleRules.add(saleRule)
                                            logger.info { "Создано справочное правило продаж для услуги: ${saleRule.serviceId}" }
                                        }
                                }
                            }
                        }
                }
                
                true
            }
            .verifyComplete()
    }

    @Test
    @Order(5)
    fun `should verify all dictionary data exists`() {
        logger.info { "Проверка наличия всех справочных данных" }
        
        // Проверяем услуги
        StepVerifier.create(testDataManager.getAllTestServices(0, 100))
            .expectNextMatches { services ->
                logger.info { "Всего услуг в системе: ${services.size}" }
                services.isNotEmpty()
            }
            .verifyComplete()

        // Проверяем региональные проекты
        StepVerifier.create(testDataManager.getAllTestRegionProjects(0, 100))
            .expectNextMatches { regionProjects ->
                logger.info { "Всего региональных проектов в системе: ${regionProjects.size}" }
                regionProjects.isNotEmpty()
            }
            .verifyComplete()

        // Проверяем правила продаж
        StepVerifier.create(testDataManager.getAllTestSaleRules(0, 100))
            .expectNextMatches { saleRules ->
                logger.info { "Всего правил продаж в системе: ${saleRules.size}" }
                saleRules.isNotEmpty()
            }
            .verifyComplete()
    }

    @Test
    @Order(6)
    fun `should test service search by subscription type`() {
        logger.info { "Тестирование поиска услуг по типу подписки" }
        
        val subscriptionTypes = listOf("TRAVEL", "WALLET", "UNLIMITED")
        
        subscriptionTypes.forEach { subscriptionType ->
            StepVerifier.create(
                testDataManager.getAllTestServices(0, 100)
                    .map { services -> services.filter { service -> service.subscriptionType.name == subscriptionType } }
            )
                .expectNextMatches { services ->
                    logger.info { "Найдено ${services.size} услуг с типом подписки: $subscriptionType" }
                    true
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(7)
    fun `should test service search by social status`() {
        logger.info { "Тестирование поиска услуг по социальному статусу" }
        
        val socialStatuses = listOf(true, false)
        
        socialStatuses.forEach { isSocial ->
            StepVerifier.create(
                testDataManager.getAllTestServices(0, 100)
                    .map { services -> services.filter { service -> service.isSocial == isSocial } }
            )
                .expectNextMatches { services ->
                    logger.info { "Найдено ${services.size} услуг с социальным статусом: $isSocial" }
                    true
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(8)
    fun `should test region project search by region`() {
        logger.info { "Тестирование поиска региональных проектов по региону" }
        
        val testRegions = listOf("47", "78", "77")
        
        testRegions.forEach { regionId ->
            StepVerifier.create(
                testDataManager.getAllTestRegionProjects(0, 100)
                    .map { projects -> projects.filter { project -> project.regionId == regionId } }
            )
                .expectNextMatches { projects ->
                    logger.info { "Найдено ${projects.size} проектов для региона: $regionId" }
                    true
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(9)
    fun `should test sale rule search by service`() {
        logger.info { "Тестирование поиска правил продаж по услуге" }
        
        // Получаем первую услугу для тестирования
        StepVerifier.create(testDataManager.getAllTestServices(0, 1))
            .expectNextMatches { services ->
                if (services.isNotEmpty()) {
                    val serviceId = services[0].id
                    StepVerifier.create(
                        testDataManager.getAllTestSaleRules(0, 100)
                            .map { rules -> rules.filter { rule -> rule.serviceId == serviceId } }
                    )
                        .expectNextMatches { rules ->
                            logger.info { "Найдено ${rules.size} правил продаж для услуги: $serviceId" }
                            true
                        }
                        .verifyComplete()
                }
                true
            }
            .verifyComplete()
    }

    @Test
    @Order(10)
    fun `should test sale rule search by type`() {
        logger.info { "Тестирование поиска правил продаж по типу" }
        
        val ruleTypes = listOf(SaleRuleType.MONTHLY_RANGE, SaleRuleType.DAILY_RANGE)
        
        ruleTypes.forEach { ruleType ->
            StepVerifier.create(
                testDataManager.getAllTestSaleRules(0, 100)
                    .map { rules -> rules.filter { rule -> rule.ruleType == ruleType } }
            )
                .expectNextMatches { rules ->
                    logger.info { "Найдено ${rules.size} правил продаж с типом: $ruleType" }
                    true
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(11)
    fun `should test sale rule search by logic`() {
        logger.info { "Тестирование поиска правил продаж по логике" }
        
        val ruleLogics = listOf(SaleRuleLogic.AND, SaleRuleLogic.OR)
        
        ruleLogics.forEach { ruleLogic ->
            StepVerifier.create(
                testDataManager.getAllTestSaleRules(0, 100)
                    .map { rules -> rules.filter { rule -> rule.ruleLogic == ruleLogic } }
            )
                .expectNextMatches { rules ->
                    logger.info { "Найдено ${rules.size} правил продаж с логикой: $ruleLogic" }
                    true
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(12)
    fun `should test sale rule search by active status`() {
        logger.info { "Тестирование поиска правил продаж по статусу активности" }
        
        val activeStatuses = listOf(true, false)
        
        activeStatuses.forEach { isActive ->
            StepVerifier.create(
                testDataManager.getAllTestSaleRules(0, 100)
                    .map { rules -> rules.filter { rule -> rule.isActive == isActive } }
            )
                .expectNextMatches { rules ->
                    logger.info { "Найдено ${rules.size} правил продаж со статусом активности: $isActive" }
                    true
                }
                .verifyComplete()
        }
    }

    @Test
    @Order(13)
    fun `should test pagination for all entities`() {
        logger.info { "Тестирование пагинации для всех сущностей" }
        
        // Тестируем пагинацию для услуг
        StepVerifier.create(testDataManager.getAllTestServices(0, 5))
            .expectNextMatches { services ->
                logger.info { "Пагинация услуг (страница 0, размер 5): ${services.size} записей" }
                services.size <= 5
            }
            .verifyComplete()

        // Тестируем пагинацию для региональных проектов
        StepVerifier.create(testDataManager.getAllTestRegionProjects(0, 5))
            .expectNextMatches { regionProjects ->
                logger.info { "Пагинация региональных проектов (страница 0, размер 5): ${regionProjects.size} записей" }
                regionProjects.size <= 5
            }
            .verifyComplete()

        // Тестируем пагинацию для правил продаж
        StepVerifier.create(testDataManager.getAllTestSaleRules(0, 5))
            .expectNextMatches { saleRules ->
                logger.info { "Пагинация правил продаж (страница 0, размер 5): ${saleRules.size} записей" }
                saleRules.size <= 5
            }
            .verifyComplete()
    }

    @Test
    @Order(14)
    fun `should test data consistency`() {
        logger.info { "Тестирование консистентности данных" }
        
        // Проверяем, что все созданные услуги существуют
        createdServices.forEach { service ->
            StepVerifier.create(testDataManager.getTestService(service.id!!))
                .expectNextMatches { retrievedService ->
                    retrievedService != null && retrievedService.id == service.id
                }
                .verifyComplete()
        }

        // Проверяем, что все созданные региональные проекты существуют
        createdRegionProjects.forEach { regionProject ->
            StepVerifier.create(testDataManager.getTestRegionProject(regionProject.id!!))
                .expectNextMatches { retrievedProject ->
                    retrievedProject != null && retrievedProject.id == regionProject.id
                }
                .verifyComplete()
        }

        // Проверяем, что все созданные правила продаж существуют
        createdSaleRules.forEach { saleRule ->
            StepVerifier.create(testDataManager.getTestSaleRule(saleRule.id!!))
                .expectNextMatches { retrievedRule ->
                    retrievedRule != null && retrievedRule.id == saleRule.id
                }
                .verifyComplete()
        }
    }

    @AfterAll
    fun cleanup() {
        logger.info { "Очистка созданных справочных данных" }
        
        // Удаляем созданные услуги
        createdServices.forEach { service ->
            testDataManager.deleteTestService(service.id!!)
                .subscribe { success ->
                    if (success) {
                        logger.info { "Удалена справочная услуга: ${service.serviceCode}" }
                    }
                }
        }

        // Удаляем созданные региональные проекты
        createdRegionProjects.forEach { regionProject ->
            testDataManager.deleteTestRegionProject(regionProject.id!!)
                .subscribe { success ->
                    if (success) {
                        logger.info { "Удален справочный региональный проект: ${regionProject.regionId}" }
                    }
                }
        }

        // Удаляем созданные правила продаж
        createdSaleRules.forEach { saleRule ->
            testDataManager.deleteTestSaleRule(saleRule.id!!)
                .subscribe { success ->
                    if (success) {
                        logger.info { "Удалено справочное правило продаж: ${saleRule.id}" }
                    }
                }
        }
    }
} 