package ru.sbertroika.abt.sbol.controller.it.config

import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.web.reactive.function.client.WebClient

@TestConfiguration
@ComponentScan(basePackages = ["ru.sbertroika.abt.sbol.controller.it"])
open class TestConfig {

    @Bean
    open fun webClient(): WebClient {
        return WebClient.builder()
            .codecs { configurer ->
                configurer.defaultCodecs().maxInMemorySize(1024 * 1024) // 1MB
            }
            .build()
    }
} 