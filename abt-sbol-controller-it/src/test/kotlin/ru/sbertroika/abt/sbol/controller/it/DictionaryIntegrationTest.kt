package ru.sbertroika.abt.sbol.controller.it

import mu.KotlinLogging
import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier
import ru.sbertroika.abt.sbol.controller.it.client.ControllerClient
import ru.sbertroika.abt.sbol.controller.it.data.TestData
import ru.sbertroika.abt.sbol.controller.it.test.TestDataManager
import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import ru.sbertroika.abt.sbol.model.entities.Service
import ru.sbertroika.abt.sbol.model.enums.SaleRuleLogic
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DictionaryIntegrationTest {

    @Autowired
    private lateinit var testDataManager: TestDataManager

    @Autowired
    private lateinit var controllerClient: ControllerClient

    private val logger = KotlinLogging.logger {}

    companion object {
        private var testServiceId: UUID? = null
        private var testRegionProjectId: UUID? = null
        private var testSaleRuleId: UUID? = null
        private var testServiceCode: String = ""
        private var testRegionId: String = ""
    }

    @Test
    @Order(1)
    fun `should check controller health`() {
        logger.info { "Проверка здоровья контроллера" }
        
        StepVerifier.create(testDataManager.checkControllerHealth())
            .expectNext(true)
            .verifyComplete()
    }

    @Test
    @Order(2)
    fun `should check and create test service if not exists`() {
        logger.info { "Проверка и создание тестовой услуги" }
        
        testServiceCode = TestData.generateUniqueServiceCode()
        
        // Проверяем, существует ли услуга с таким кодом
        StepVerifier.create(testDataManager.getTestServiceByCode(testServiceCode))
            .expectNext(null) // Ожидаем null, так как услуга не должна существовать
            .verifyComplete()

        // Создаем тестовую услугу
        val serviceDto = TestData.createTestServiceWithCode(testServiceCode)
        StepVerifier.create(testDataManager.createTestServiceWithData(serviceDto))
            .expectNextMatches { service ->
                testServiceId = service.id
                logger.info { "Создана тестовая услуга с ID: ${service.id}, кодом: ${service.serviceCode}" }
                service.serviceCode == testServiceCode &&
                service.name == "Тестовая услуга" &&
                service.subscriptionType == SubscriptionType.TRAVEL &&
                service.cost == 150000L
            }
            .verifyComplete()
    }

    @Test
    @Order(3)
    fun `should verify created service exists`() {
        logger.info { "Проверка созданной услуги" }
        
        requireNotNull(testServiceId) { "ID тестовой услуги не установлен" }
        
        StepVerifier.create(testDataManager.getTestService(testServiceId!!))
            .expectNextMatches { service ->
                service != null &&
                service.serviceCode == testServiceCode &&
                service.name == "Тестовая услуга"
            }
            .verifyComplete()

        // Проверяем поиск по коду
        StepVerifier.create(testDataManager.getTestServiceByCode(testServiceCode))
            .expectNextMatches { service ->
                service != null &&
                service.id == testServiceId
            }
            .verifyComplete()
    }

    @Test
    @Order(4)
    fun `should check and create test region project if not exists`() {
        logger.info { "Проверка и создание тестового регионального проекта" }
        
        testRegionId = TestData.generateUniqueRegionId()
        
        // Создаем тестовый региональный проект
        val regionProjectDto = TestData.createTestRegionProjectWithRegion(testRegionId)
        StepVerifier.create(testDataManager.createTestRegionProjectWithData(regionProjectDto))
            .expectNextMatches { regionProject ->
                testRegionProjectId = regionProject.id
                logger.info { "Создан тестовый региональный проект с ID: ${regionProject.id}, регионом: ${regionProject.regionId}" }
                regionProject.regionId == testRegionId &&
                regionProject.projectId == TestData.TEST_PROJECT_ID &&
                regionProject.canDecodeHash == true
            }
            .verifyComplete()
    }

    @Test
    @Order(5)
    fun `should verify created region project exists`() {
        logger.info { "Проверка созданного регионального проекта" }
        
        requireNotNull(testRegionProjectId) { "ID тестового регионального проекта не установлен" }
        
        StepVerifier.create(testDataManager.getTestRegionProject(testRegionProjectId!!))
            .expectNextMatches { regionProject ->
                regionProject != null &&
                regionProject.regionId == testRegionId &&
                regionProject.projectId == TestData.TEST_PROJECT_ID
            }
            .verifyComplete()
    }

    @Test
    @Order(6)
    fun `should check and create test sale rule if not exists`() {
        logger.info { "Проверка и создание тестового правила продаж" }
        
        requireNotNull(testServiceId) { "ID тестовой услуги не установлен" }
        
        // Создаем тестовое правило продаж
        val saleRuleDto = TestData.createTestSaleRule(testServiceId!!)
        StepVerifier.create(testDataManager.createTestSaleRuleWithData(saleRuleDto))
            .expectNextMatches { saleRule ->
                testSaleRuleId = saleRule.id
                logger.info { "Создано тестовое правило продаж с ID: ${saleRule.id}, для услуги: ${saleRule.serviceId}" }
                saleRule.serviceId == testServiceId &&
                saleRule.ruleType == SaleRuleType.MONTHLY_RANGE &&
                saleRule.ruleLogic == SaleRuleLogic.AND &&
                saleRule.isActive == true
            }
            .verifyComplete()
    }

    @Test
    @Order(7)
    fun `should verify created sale rule exists`() {
        logger.info { "Проверка созданного правила продаж" }
        
        requireNotNull(testSaleRuleId) { "ID тестового правила продаж не установлен" }
        
        StepVerifier.create(testDataManager.getTestSaleRule(testSaleRuleId!!))
            .expectNextMatches { saleRule ->
                saleRule != null &&
                saleRule.serviceId == testServiceId &&
                saleRule.ruleType == SaleRuleType.MONTHLY_RANGE
            }
            .verifyComplete()
    }

    @Test
    @Order(8)
    fun `should get all services and verify test service is present`() {
        logger.info { "Проверка списка всех услуг" }
        
        StepVerifier.create(testDataManager.getAllTestServices(0, 100))
            .expectNextMatches { services ->
                services.isNotEmpty() &&
                services.any { it.serviceCode == testServiceCode }
            }
            .verifyComplete()
    }

    @Test
    @Order(9)
    fun `should get all region projects and verify test project is present`() {
        logger.info { "Проверка списка всех региональных проектов" }
        
        StepVerifier.create(testDataManager.getAllTestRegionProjects(0, 100))
            .expectNextMatches { regionProjects ->
                regionProjects.isNotEmpty() &&
                regionProjects.any { it.regionId == testRegionId }
            }
            .verifyComplete()
    }

    @Test
    @Order(10)
    fun `should get all sale rules and verify test rule is present`() {
        logger.info { "Проверка списка всех правил продаж" }
        
        StepVerifier.create(testDataManager.getAllTestSaleRules(0, 100))
            .expectNextMatches { saleRules ->
                saleRules.isNotEmpty() &&
                saleRules.any { it.serviceId == testServiceId }
            }
            .verifyComplete()
    }

    @Test
    @Order(11)
    fun `should update test service`() {
        logger.info { "Обновление тестовой услуги" }
        
        requireNotNull(testServiceId) { "ID тестовой услуги не установлен" }
        
        val updatedServiceDto = ServiceDto(
            projectId = TestData.TEST_PROJECT_ID,
            templateId = TestData.TEST_TEMPLATE_ID,
            serviceCode = testServiceCode,
            name = "Обновленная тестовая услуга",
            description = "Обновленное описание тестовой услуги",
            isSocial = true,
            subscriptionType = SubscriptionType.TRAVEL,
            cost = 200000L,
            actionStartDate = LocalDate.now(),
            actionEndDate = LocalDate.now().plusMonths(12),
            minReplenishmentAmount = 15000L,
            maxReplenishmentAmount = 1500000L,
            recommendedAmount = 75000L
        )

        StepVerifier.create(testDataManager.updateTestService(testServiceId!!, updatedServiceDto))
            .expectNextMatches { service ->
                service != null &&
                service.name == "Обновленная тестовая услуга" &&
                service.isSocial == true &&
                service.cost == 200000L
            }
            .verifyComplete()
    }

    @Test
    @Order(12)
    fun `should update test region project`() {
        logger.info { "Обновление тестового регионального проекта" }
        
        requireNotNull(testRegionProjectId) { "ID тестового регионального проекта не установлен" }
        
        val updatedRegionProjectDto = RegionProjectDto(
            regionId = testRegionId,
            projectId = TestData.TEST_PROJECT_ID,
            canDecodeHash = false,
            panDecodeKey = "updated-test-key-456"
        )

        StepVerifier.create(testDataManager.updateTestRegionProject(testRegionProjectId!!, updatedRegionProjectDto))
            .expectNextMatches { regionProject ->
                regionProject != null &&
                regionProject.canDecodeHash == false &&
                regionProject.panDecodeKey == "updated-test-key-456"
            }
            .verifyComplete()
    }

    @Test
    @Order(13)
    fun `should update test sale rule`() {
        logger.info { "Обновление тестового правила продаж" }
        
        requireNotNull(testSaleRuleId) { "ID тестового правила продаж не установлен" }
        requireNotNull(testServiceId) { "ID тестовой услуги не установлен" }
        
        val updatedSaleRuleDto = SaleRuleDto(
            serviceId = testServiceId!!,
            ruleType = SaleRuleType.DAILY_RANGE,
            ruleLogic = SaleRuleLogic.OR,
            startDay = 15,
            endDay = 25,
            startMonth = 3,
            endMonth = 9,
            isActive = false
        )

        StepVerifier.create(testDataManager.updateTestSaleRule(testSaleRuleId!!, updatedSaleRuleDto))
            .expectNextMatches { saleRule ->
                saleRule != null &&
                saleRule.ruleType == SaleRuleType.DAILY_RANGE &&
                saleRule.ruleLogic == SaleRuleLogic.OR &&
                saleRule.startDay == 15 &&
                saleRule.isActive == false
            }
            .verifyComplete()
    }

    @Test
    @Order(14)
    fun `should create complete test data set`() {
        logger.info { "Создание полного набора тестовых данных" }
        
        StepVerifier.create(testDataManager.createTestDataSet())
            .expectNextMatches { testDataSet ->
                testDataSet.service.serviceCode.isNotEmpty() &&
                testDataSet.regionProject.regionId.isNotEmpty() &&
                testDataSet.saleRule.serviceId == testDataSet.service.id
            }
            .verifyComplete()
    }

    @Test
    @Order(15)
    fun `should test service not found scenarios`() {
        logger.info { "Тестирование сценариев отсутствия данных" }
        
        val nonExistentId = UUID.randomUUID()
        
        // Проверяем несуществующую услугу
        StepVerifier.create(testDataManager.getTestService(nonExistentId))
            .expectNext(null)
            .verifyComplete()

        // Проверяем несуществующий региональный проект
        StepVerifier.create(testDataManager.getTestRegionProject(nonExistentId))
            .expectNext(null)
            .verifyComplete()

        // Проверяем несуществующее правило продаж
        StepVerifier.create(testDataManager.getTestSaleRule(nonExistentId))
            .expectNext(null)
            .verifyComplete()
    }

    @Test
    @Order(16)
    fun `should test pagination for services`() {
        logger.info { "Тестирование пагинации для услуг" }
        
        StepVerifier.create(testDataManager.getAllTestServices(0, 5))
            .expectNextMatches { services ->
                services.size <= 5
            }
            .verifyComplete()
    }

    @Test
    @Order(17)
    fun `should test pagination for region projects`() {
        logger.info { "Тестирование пагинации для региональных проектов" }
        
        StepVerifier.create(testDataManager.getAllTestRegionProjects(0, 5))
            .expectNextMatches { regionProjects ->
                regionProjects.size <= 5
            }
            .verifyComplete()
    }

    @Test
    @Order(18)
    fun `should test pagination for sale rules`() {
        logger.info { "Тестирование пагинации для правил продаж" }
        
        StepVerifier.create(testDataManager.getAllTestSaleRules(0, 5))
            .expectNextMatches { saleRules ->
                saleRules.size <= 5
            }
            .verifyComplete()
    }

    @AfterAll
    fun cleanup() {
        logger.info { "Очистка тестовых данных" }
        
        // Очищаем созданные тестовые данные
        if (testServiceId != null && testRegionProjectId != null && testSaleRuleId != null) {
            StepVerifier.create(
                testDataManager.cleanupTestData(testServiceId!!, testRegionProjectId!!, testSaleRuleId!!)
            )
                .expectNext(true)
                .verifyComplete()
        }
    }
} 