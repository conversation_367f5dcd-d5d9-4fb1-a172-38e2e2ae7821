package ru.sbertroika.abt.sbol.controller.it

import org.junit.jupiter.api.Test
import org.springframework.web.reactive.function.client.WebClient
import reactor.test.StepVerifier
import java.time.Duration

class SimpleIntegrationTest {

    private val webClient = WebClient.builder()
        .baseUrl("http://localhost:8083")
        .build()

    @Test
    fun `should check controller health`() {
        StepVerifier.create(
            webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String::class.java)
                .timeout(Duration.ofSeconds(10))
        )
            .expectNextMatches { it.contains("\"status\":\"UP\"") }
            .verifyComplete()
    }
} 