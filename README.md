# ABT Domain

Доменная система для работы с ABT (Agent-Based Transport) и SBOL (Smart Bank of Leningrad).

## Микросервисы

- **abt-gate** - основной шлюз для работы с ABT
- **abt-emission** - сервис эмиссии карт
- **abt-processing** - сервис обработки данных
- **abt-sbol-gate** - шлюз для работы с SBOL API

## Быстрый старт

### Локальный запуск с Docker Compose

1. Убедитесь, что у вас установлены Docker и Docker Compose
2. Клонируйте репозиторий
3. Запустите все сервисы:

```bash
make docker-build-and-run
```

Или пошагово:

```bash
# Сборка всех образов
make docker-build

# Запуск инфраструктуры (базы данных, Kafka)
make docker-run-infra

# Запуск всех сервисов
make start
```

### Запуск только abt-sbol-gate локально

Для разработки можно запустить только `abt-sbol-gate` с локальной базой данных:

```bash
# Запуск инфраструктуры
make docker-run-infra

# Запуск abt-sbol-gate с профилем local
./gradlew :abt-sbol-gate:bootRun --args='--spring.profiles.active=local'
```

## Портфолио сервисов

| Сервис | Порт | Описание |
|--------|------|----------|
| abt-gate | 5010 (gRPC), 8080 (HTTP) | Основной шлюз ABT |
| abt-emission | 5008 (gRPC), 8081 (HTTP) | Эмиссия карт |
| abt-processing | 8082 (HTTP) | Обработка данных |
| abt-sbol-gate | 8083 (HTTP) | Шлюз SBOL API |
| PostgreSQL (abt-gate) | 5432 | База данных ABT |
| PostgreSQL (abt-emission) | 5433 | База данных эмиссии |
| PostgreSQL (abt-sbol-gate) | 5434 | База данных SBOL |
| Kafka | 29092 | Брокер сообщений |
| Kafka UI | 8086 | Веб-интерфейс Kafka |

## База данных abt-sbol-gate

Микросервис `abt-sbol-gate` использует PostgreSQL с автоматическими миграциями Flyway. Структура базы данных включает:

- Таблицы для управления услугами и правилами продаж
- Таблицы для работы с SBOL API (счета, заказы, карты)
- Поддержка множественных правил продаж с логикой AND/OR
- Ограничения по балансу карты как правила продаж

## Разработка

### Структура проекта

```
abt-domain/
├── abt-gate/           # Основной шлюз ABT
├── abt-emission/       # Сервис эмиссии
├── abt-processing/     # Обработка данных
├── abt-sbol-gate/      # Шлюз SBOL API
├── abt-sbol-model/     # Модели данных для SBOL
├── charts/             # Helm charts для Kubernetes
├── docs/               # Документация
└── docker-compose.yaml # Локальная разработка
```

### Полезные команды

```bash
# Пересборка и перезапуск всех сервисов
make rebuild-and-restart

# Остановка всех сервисов
make stop

# Очистка контейнеров
make docker-rm

# Запуск только инфраструктуры
make docker-run-infra
```

## API документация

После запуска сервисов документация API доступна по адресам:

- **abt-gate**: http://localhost:8080/swagger-ui.html
- **abt-emission**: http://localhost:8081/swagger-ui.html
- **abt-sbol-gate**: http://localhost:8083/swagger-ui.html

## Мониторинг

Health check endpoints:

- **abt-gate**: http://localhost:8080/actuator/health
- **abt-emission**: http://localhost:8081/actuator/health
- **abt-sbol-gate**: http://localhost:8083/actuator/health
- **Kafka UI**: http://localhost:8086 