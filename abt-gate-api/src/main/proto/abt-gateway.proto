syntax = "proto3";

package ru.sbertroika.abt.gateway.v1;

import "google/protobuf/empty.proto";
import "common.proto";
import "common-manifest.proto";
import "common-manifest-pro.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.abt.gateway.v1";

service AbtGatewayService {
  // Запрос манифеста
  rpc getManifest(common.manifest.v1.ManifestRequest) returns (ManifestResponse);
  // Получение информации о карте
  rpc cardInfo(CardInfoRequest) returns (CardInfoRequestResponse);
  // Эмиссия билета
  rpc createSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  // Получение всех абонементов на карте
  rpc subscriptionList(SubscriptionListRequest) returns (SubscriptionListResponse);
  // Пополнить баланс кошелька
  rpc walletRefill(WalletRefillRequest) returns (WalletRefillResponse);
}

message ManifestResponse {
  oneof response {
    common.v1.OperationError error = 1;
    common.manifest.v1.pro.ManifestProAbt manifest = 2;
  }
}

message CardInfoRequest {
  string pan = 1;                     // CardNumber / pan / panHash
  string projectId = 2;               // Идентификатор проекта
}

message CardInfoRequestResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    CardInfo info = 2;
  }
}

message CardInfo {
  string id = 1;
  string number = 2;
  string uid = 3;
  string projectId = 4;
  CardType type = 5;
}

message CardType {
  string id = 1;
  string name = 2;
}

message CreateSubscriptionRequest {
  string templateId = 1;                                      // Идентификатор шаблона
  string cardId = 2;                                          // Идентификатор карты
  string projectId = 3;                                       // Идентификатор проекта
  optional SubscriptionEmissionSource emissionSource = 4;     // Источник эмиссии (Если не задан по умолчанию ABT)
}

message CreateSubscriptionResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    SubscriptionInfo info = 2;
  }
}

message SubscriptionInfo {
  string subscription_id = 1;         // Идентификатор билета
  string abonementId = 2;             // Числовой Идентификатор абонемента
  string projectId = 3;               // Идентификатор проекта
  string name = 4;                    // Наименование абонемента
  int64 activeFrom = 5;               // Время начала действия абонемента
  int64 activeTill = 6;               // Время окончания действия абонемента
  string description = 7;             // Описание абонемента
  string templateId = 8;              // Идентификатор шаблона
}

enum SubscriptionEmissionSource {
  ABT = 0;
  EMV = 1;
}

message SubscriptionListFilter {
  string cardId = 1; // Идентификатор карты
  optional string projectId = 2;  // Идентификатор проекта
  optional SubscriptionEmissionSource emissionSource = 3;   // Источник эмиссии
  bool isActive = 4;  // Включать только активные на данный момент времени
  bool isActual = 5;  // Включать все актуальные, т.е. все у которых не закончился срок годности (включая те у которых срок еще не наступил)
}

message SubscriptionListRequest {
    SubscriptionListFilter filter = 1;
    optional int32 limit = 2;
}

message SubscriptionList {
  repeated SubscriptionInfo info = 1;
}

message SubscriptionListResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    SubscriptionList list = 2;
  }
}

message WalletRefillRequest {
  string cardId = 1;              // Идентификатор карты
  optional string projectId = 2;  // Идентификатор проекта
  optional string walletId = 3;   // Идентификатор кошелька (на случай если кошельков несколько на карте)
  optional string templateId = 4; // Идентификатор шаблона кошелька для пополнения (на случай если кошельков несколько на карте)
  uint32 value = 5;               // значение на которое необходимо пополнить кошелек
}

message WalletRefillResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    uint32 balance = 2;                                 // Новый баланс кошелька
  }
}