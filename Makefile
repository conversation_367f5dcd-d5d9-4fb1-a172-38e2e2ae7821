docker-build-and-run: docker-build-gate docker-build-emission docker-build-processing docker-build-sbol-gate
	docker-compose up -d

docker-run-infra:
	docker-compose up -d abt_gate_db abt_emission_db abt_sbol_gate_db zoo1 kafka kafka-ui

docker-build: docker-build-gate docker-build-emission docker-build-processing docker-build-sbol-gate

start:
	docker-compose up -d

restart:
	docker-compose stop
	docker-compose rm -vf abt_gate
	docker-compose rm -vf abt_emission
	docker-compose rm -vf abt_processing
	docker-compose rm -vf abt_sbol_gate
	docker-compose up -d

restart-all:
	docker-compose stop && docker-compose rm -vf && docker-compose up -d

docker-rm:
	docker-compose stop && docker-compose rm -vf

docker-rm-all:
	docker-compose rm -vf

stop:
	docker-compose stop

rebuild-and-restart: stop docker-rm docker-build start

rebuild-and-restart-all: stop docker-rm-all docker-build start

docker-build-gate:
	@echo "[INFO] Сборка docker-образа abt-gate с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t abt-gate:local \
		-f abt-gate/Dockerfile .
	rm -rf .ci-gradle

docker-build-emission:
	@echo "[INFO] Сборка docker-образа abt-emission с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t abt-emission:local \
		-f abt-emission/Dockerfile .
	rm -rf .ci-gradle

docker-build-processing:
	@echo "[INFO] Сборка docker-образа abt-processing с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t abt-processing:local \
		-f abt-processing/Dockerfile .
	rm -rf .ci-gradle

docker-build-sbol-gate:
	@echo "[INFO] Сборка docker-образа abt-sbol-gate с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t abt-sbol-gate:local \
		-f abt-sbol-gate/Dockerfile .
	rm -rf .ci-gradle 