package ru.sbertroika.tkp3.abt.gateway.input

import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import ru.sbertroika.abt.gateway.v1.AbtGatewayServiceGrpcKt
import ru.sbertroika.abt.gateway.v1.cardInfoRequest
import ru.sbertroika.abt.gateway.v1.createSubscriptionRequest
import ru.sbertroika.abt.gateway.v1.subscriptionListRequest
import ru.sbertroika.abt.gateway.v1.subscriptionListFilter
import ru.sbertroika.abt.gateway.v1.SubscriptionEmissionSource
import ru.sbertroika.common.manifest.v1.manifestRequest
import java.util.UUID
import java.util.concurrent.TimeUnit

class AbtGatewayServiceGrpcTest {

    companion object {
        private val server = "localhost:5011"
        // Используем один и тот же cardId для всех тестов
        private val testCardId = "550e8400-e29b-41d4-a716-************"
    }

    @Test
    fun getManifest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            val response = client.getManifest(
                manifestRequest {
                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                }
            )
            println("response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.manifest)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun cardInfo(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            val response = client.cardInfo(
                cardInfoRequest {
                    pan = //"9643102703300250150"
                        //"****************"
                        "6477217e73e724b6a529508f117f3d9526499cd773182fa73bcb984a2693630c"
                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                }
            )
            println("response: $response")
            assertFalse(response.hasError())
            assertNotNull(response)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun createSubscription(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            val response = client.createSubscription(
                createSubscriptionRequest {
                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                    cardId = testCardId
                }
            )
            println("response: $response")
            println(response.info.subscriptionId)
            assertFalse(response.hasError())
            assertNotNull(response)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun subscriptionList_AllSubscriptions(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            
//            // Создаем абонемент для тестирования
//            val createResponse = client.createSubscription(
//                createSubscriptionRequest {
//                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
//                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
//                    cardId = testCardId
//                }
//            )
//            assertFalse(createResponse.hasError())
            
            // Получаем все абонементы для карты
            val response = client.subscriptionList(
                subscriptionListRequest {
                    filter = subscriptionListFilter {
                        cardId = testCardId
                        projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                        isActive = false
                        isActual = false
                    }
                }
            )
            println("subscriptionList response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.list)
            assertTrue(response.list.infoCount > 0)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun subscriptionList_ActiveSubscriptions(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            
//            // Создаем абонемент для тестирования
//            val createResponse = client.createSubscription(
//                createSubscriptionRequest {
//                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
//                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
//                    cardId = testCardId
//                }
//            )
//            assertFalse(createResponse.hasError())
            
            // Получаем только активные абонементы
            val response = client.subscriptionList(
                subscriptionListRequest {
                    filter = subscriptionListFilter {
                        cardId = testCardId
                        projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                        isActive = true
                        isActual = false
                    }
                }
            )
            println("subscriptionList active response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.list)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun subscriptionList_ActualSubscriptions(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            
//            // Создаем абонемент для тестирования
//            val createResponse = client.createSubscription(
//                createSubscriptionRequest {
//                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
//                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
//                    cardId = testCardId
//                }
//            )
//            assertFalse(createResponse.hasError())
            
            // Получаем актуальные абонементы (не истекшие)
            val response = client.subscriptionList(
                subscriptionListRequest {
                    filter = subscriptionListFilter {
                        cardId = testCardId
                        projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                        isActive = false
                        isActual = true
                    }
                }
            )
            println("subscriptionList actual response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.list)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun subscriptionList_WithEmissionSourceFilter(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            
//            // Создаем абонемент для тестирования
//            val createResponse = client.createSubscription(
//                createSubscriptionRequest {
//                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
//                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
//                    cardId = testCardId
//                }
//            )
//            assertFalse(createResponse.hasError())
            
            // Получаем абонементы с фильтром по источнику эмиссии
            val response = client.subscriptionList(
                subscriptionListRequest {
                    filter = subscriptionListFilter {
                        cardId = testCardId
                        projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                        emissionSource = SubscriptionEmissionSource.ABT
                        isActive = false
                        isActual = false
                    }
                }
            )
            println("subscriptionList with emission source filter response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.list)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun subscriptionList_WithLimit(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            
//            // Создаем абонемент для тестирования
//            val createResponse = client.createSubscription(
//                createSubscriptionRequest {
//                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
//                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
//                    cardId = testCardId
//                }
//            )
//            assertFalse(createResponse.hasError())
            
            // Получаем абонементы с ограничением по количеству
            val response = client.subscriptionList(
                subscriptionListRequest {
                    filter = subscriptionListFilter {
                        cardId = testCardId
                        projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                        isActive = false
                        isActual = false
                    }
                    limit = 5
                }
            )
            println("subscriptionList with limit response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.list)
            assertTrue(response.list.infoCount <= 5)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun subscriptionList_CombinedFilters(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)
            
//            // Создаем абонемент для тестирования
//            val createResponse = client.createSubscription(
//                createSubscriptionRequest {
//                    templateId = "bbecd48f-6f17-4a1f-b5f0-648582886b38"
//                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
//                    cardId = testCardId
//                }
//            )
//            assertFalse(createResponse.hasError())
            
            // Получаем абонементы с комбинированными фильтрами
            val response = client.subscriptionList(
                subscriptionListRequest {
                    filter = subscriptionListFilter {
                        cardId = testCardId
                        projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                        emissionSource = SubscriptionEmissionSource.ABT
                        isActive = true
                        isActual = true
                    }
                    limit = 10
                }
            )
            println("subscriptionList combined filters response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.list)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
} 