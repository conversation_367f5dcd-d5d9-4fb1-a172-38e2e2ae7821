package ru.sbertroika.abt.sbol.controller.input

import jakarta.validation.Valid
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.output.model.ApiResponse
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.service.SaleRuleService
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import java.util.UUID

@RestController
@RequestMapping("/api/v1/abt/sbol/sale-rules")
class SaleRuleController(
    private val saleRuleService: SaleRuleService
) {

    @PostMapping
    fun createSaleRule(@Valid @RequestBody saleRuleDto: SaleRuleDto): Mono<ApiResponse<SaleRule>> {
        return saleRuleService.createSaleRule(saleRuleDto)
            .map { saleRule ->
                ApiResponse.success(saleRule, "Правило продаж успешно создано")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SaleRule>("Ошибка создания правила продаж: ${error.message}"))
            }
    }

    @PutMapping("/{id}")
    fun updateSaleRule(
        @PathVariable id: UUID,
        @Valid @RequestBody saleRuleDto: SaleRuleDto
    ): Mono<ApiResponse<SaleRule>> {
        return saleRuleService.updateSaleRule(id, saleRuleDto)
            .map { saleRule ->
                ApiResponse.success(saleRule, "Правило продаж успешно обновлено")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SaleRule>("Ошибка обновления правила продаж: ${error.message}"))
            }
    }

    @GetMapping("/{id}")
    fun getSaleRuleById(@PathVariable id: UUID): Mono<ApiResponse<SaleRule>> {
        return saleRuleService.getSaleRuleById(id)
            .map { saleRule ->
                ApiResponse.success(saleRule)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SaleRule>("Правило продаж с id $id не найдено"))
            }
    }

    @GetMapping("/service/{serviceId}")
    fun getSaleRulesByServiceId(@PathVariable serviceId: UUID): Mono<ApiResponse<List<SaleRule>>> {
        return saleRuleService.getSaleRulesByServiceId(serviceId)
            .collectList()
            .map { saleRules ->
                ApiResponse.success(saleRules)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SaleRule>>("Ошибка получения правил продаж по ID услуги: ${error.message}"))
            }
    }

    @GetMapping("/service/{serviceId}/active")
    fun getActiveSaleRulesByServiceId(@PathVariable serviceId: UUID): Mono<ApiResponse<List<SaleRule>>> {
        return saleRuleService.getActiveSaleRulesByServiceId(serviceId)
            .collectList()
            .map { saleRules ->
                ApiResponse.success(saleRules)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SaleRule>>("Ошибка получения активных правил продаж: ${error.message}"))
            }
    }

    @GetMapping("/type/{ruleType}")
    fun getSaleRulesByRuleType(@PathVariable ruleType: String): Mono<ApiResponse<List<SaleRule>>> {
        return saleRuleService.getSaleRulesByRuleType(ruleType)
            .collectList()
            .map { saleRules ->
                ApiResponse.success(saleRules)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SaleRule>>("Ошибка получения правил продаж по типу: ${error.message}"))
            }
    }

    @GetMapping("/active/{isActive}")
    fun getSaleRulesByIsActive(@PathVariable isActive: Boolean): Mono<ApiResponse<List<SaleRule>>> {
        return saleRuleService.getSaleRulesByIsActive(isActive)
            .collectList()
            .map { saleRules ->
                ApiResponse.success(saleRules)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SaleRule>>("Ошибка получения правил продаж по активности: ${error.message}"))
            }
    }

    @GetMapping
    fun getAllSaleRules(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<PageResponse<SaleRule>>> {
        return saleRuleService.getAllSaleRules(page, size)
            .map { saleRules ->
                ApiResponse.success(saleRules)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<PageResponse<SaleRule>>("Ошибка получения списка правил продаж: ${error.message}"))
            }
    }

    @DeleteMapping("/{id}")
    fun deleteSaleRule(@PathVariable id: UUID): Mono<ApiResponse<Unit>> {
        return saleRuleService.deleteSaleRule(id)
            .map { deleted ->
                if (deleted) {
                    ApiResponse.success(Unit, "Правило продаж успешно удалено")
                } else {
                    ApiResponse.error("Правило продаж с id $id не найдено")
                }
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Unit>("Ошибка удаления правила продаж: ${error.message}"))
            }
    }
} 