package ru.sbertroika.abt.sbol.controller.service

import mu.KotlinLogging
import org.springframework.dao.DataAccessException
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.controller.input.model.SaleRuleDto
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.output.repository.SaleRuleRepository
import ru.sbertroika.abt.sbol.controller.service.converter.EnumConverter.toDatabaseValue
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class SaleRuleService(
    private val saleRuleRepository: SaleRuleRepository
) {
    
    private val logger = KotlinLogging.logger {}

    fun createSaleRule(saleRuleDto: SaleRuleDto): Mono<SaleRule> {
        return try {
            val saleRule = SaleRule(
                serviceId = saleRuleDto.serviceId,
                ruleType = saleRuleDto.ruleType.toDatabaseValue(),
                ruleLogic = saleRuleDto.ruleLogic.toDatabaseValue(),
                startDay = saleRuleDto.startDay,
                endDay = saleRuleDto.endDay,
                startMonth = saleRuleDto.startMonth,
                endMonth = saleRuleDto.endMonth,
                startWeek = saleRuleDto.startWeek,
                endWeek = saleRuleDto.endWeek,
                minCardBalance = saleRuleDto.minCardBalance,
                maxCardBalance = saleRuleDto.maxCardBalance,
                isActive = saleRuleDto.isActive
            )
            saleRuleRepository.save(saleRule)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при создании правила продаж: ${e.message}", e))
        }
    }

    fun updateSaleRule(id: UUID, saleRuleDto: SaleRuleDto): Mono<SaleRule> {
        return try {
            saleRuleRepository.findById(id)
                .flatMap { existingSaleRule ->
                    val updatedSaleRule = existingSaleRule.copy(
                        serviceId = saleRuleDto.serviceId,
                        ruleType = saleRuleDto.ruleType.toDatabaseValue(),
                        ruleLogic = saleRuleDto.ruleLogic.toDatabaseValue(),
                        startDay = saleRuleDto.startDay,
                        endDay = saleRuleDto.endDay,
                        startMonth = saleRuleDto.startMonth,
                        endMonth = saleRuleDto.endMonth,
                        startWeek = saleRuleDto.startWeek,
                        endWeek = saleRuleDto.endWeek,
                        minCardBalance = saleRuleDto.minCardBalance,
                        maxCardBalance = saleRuleDto.maxCardBalance,
                        isActive = saleRuleDto.isActive,
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    saleRuleRepository.save(updatedSaleRule)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при обновлении правила продаж: ${e.message}", e))
        }
    }

    fun getSaleRuleById(id: UUID): Mono<SaleRule> {
        return try {
            saleRuleRepository.findById(id)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении правила продаж по ID: ${e.message}", e))
        }
    }

    fun getSaleRulesByServiceId(serviceId: UUID): Flux<SaleRule> {
        return try {
            saleRuleRepository.findByServiceId(serviceId)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении правил продаж по ID услуги: ${e.message}", e))
        }
    }

    fun getActiveSaleRulesByServiceId(serviceId: UUID): Flux<SaleRule> {
        return try {
            saleRuleRepository.findByServiceIdAndIsActive(serviceId, true)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении активных правил продаж: ${e.message}", e))
        }
    }

    fun getSaleRulesByRuleType(ruleType: String): Flux<SaleRule> {
        return try {
            saleRuleRepository.findByRuleType(ruleType)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении правил продаж по типу: ${e.message}", e))
        }
    }

    fun getSaleRulesByIsActive(isActive: Boolean): Flux<SaleRule> {
        return try {
            saleRuleRepository.findByIsActive(isActive)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении правил продаж по активности: ${e.message}", e))
        }
    }

    fun getAllSaleRules(page: Int = 0, size: Int = 20): Mono<PageResponse<SaleRule>> {
        return try {
            val offset = page * size
            val saleRulesFlux = saleRuleRepository.findAllWithPagination(size, offset)
            val countMono = saleRuleRepository.countAll()
            
            Mono.zip(saleRulesFlux.collectList(), countMono)
                .map { tuple ->
                    val saleRules = tuple.t1
                    val totalElements = tuple.t2
                    PageResponse.of(saleRules, totalElements, page + 1, size)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении всех правил продаж: ${e.message}", e))
        }
    }

    fun deleteSaleRule(id: UUID): Mono<Boolean> {
        return try {
            saleRuleRepository.existsById(id)
                .flatMap { exists ->
                    if (exists) {
                        saleRuleRepository.deleteById(id)
                            .then(Mono.just(true))
                    } else {
                        Mono.just(false)
                    }
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при удалении правила продаж: ${e.message}", e))
        }
    }

    fun activateSaleRule(id: UUID): Mono<SaleRule> {
        return try {
            saleRuleRepository.findById(id)
                .flatMap { saleRule ->
                    val activatedSaleRule = saleRule.copy(
                        isActive = true,
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    saleRuleRepository.save(activatedSaleRule)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при активации правила продаж: ${e.message}", e))
        }
    }

    fun deactivateSaleRule(id: UUID): Mono<SaleRule> {
        return try {
            saleRuleRepository.findById(id)
                .flatMap { saleRule ->
                    val deactivatedSaleRule = saleRule.copy(
                        isActive = false,
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    saleRuleRepository.save(deactivatedSaleRule)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при деактивации правила продаж: ${e.message}", e))
        }
    }
} 