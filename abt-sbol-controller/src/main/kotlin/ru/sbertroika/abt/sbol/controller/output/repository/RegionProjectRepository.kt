package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import java.util.UUID

@Repository
interface RegionProjectRepository : ReactiveCrudRepository<RegionProject, UUID> {

    @Query("SELECT * FROM region_project WHERE region_id = :regionId")
    fun findByRegionId(regionId: String): Flux<RegionProject>

    @Query("SELECT * FROM region_project WHERE project_id = :projectId")
    fun findByProjectId(projectId: UUID): Flux<RegionProject>

    @Query("SELECT * FROM region_project WHERE region_id = :regionId AND project_id = :projectId")
    fun findByRegionIdAndProjectId(regionId: String, projectId: UUID): Mono<RegionProject>

    @Query("SELECT * FROM region_project ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    fun findAllWithPagination(limit: Int, offset: Int): Flux<RegionProject>

    @Query("SELECT COUNT(*) FROM region_project")
    fun countAll(): Mono<Long>
} 