package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.ServiceAgent
import java.util.UUID

@Repository
interface ServiceAgentRepository : ReactiveCrudRepository<ServiceAgent, UUID> {

    @Query("SELECT * FROM service_agent WHERE service_id = :serviceId")
    fun findByServiceId(serviceId: UUID): Flux<ServiceAgent>

    @Query("SELECT * FROM service_agent WHERE agent_id = :agentId")
    fun findByAgentId(agentId: UUID): Flux<ServiceAgent>

    @Query("SELECT * FROM service_agent WHERE service_id = :serviceId AND agent_id = :agentId")
    fun findByServiceIdAndAgentId(serviceId: UUID, agentId: UUID): Mono<ServiceAgent>

    @Query("SELECT * FROM service_agent ORDER BY created_at DESC")
    fun findAllOrdered(): Flux<ServiceAgent>

    @Query("SELECT COUNT(*) FROM service_agent")
    fun countAll(): Mono<Long>
} 