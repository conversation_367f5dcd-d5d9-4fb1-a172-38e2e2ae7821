package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.Invoice
import java.time.LocalDate
import java.util.UUID

@Repository
interface InvoiceRepository : ReactiveCrudRepository<Invoice, UUID> {

    @Query("SELECT * FROM invoice ORDER BY created_at DESC LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}")
    fun findAllWithPagination(pageable: Pageable): Flux<Invoice>

    @Query("SELECT * FROM invoice WHERE agent_transaction_id = :agentTransactionId")
    fun findByAgentTransactionId(agentTransactionId: String): Mono<Invoice>

    @Query("SELECT * FROM invoice WHERE region_id = :regionId ORDER BY created_at DESC")
    fun findByRegionId(regionId: String): Flux<Invoice>

    @Query("SELECT * FROM invoice WHERE invoice_status = :status ORDER BY created_at DESC")
    fun findByInvoiceStatus(status: String): Flux<Invoice>
    
    /**
     * Поиск счетов с фильтрацией и пагинацией
     */
    @Query("""
        SELECT * FROM invoice 
        WHERE (:search IS NULL OR agent_transaction_id ILIKE CONCAT('%', :search, '%'))
        AND (:invoiceStatus IS NULL OR invoice_status = :invoiceStatus)
        AND (:regionId IS NULL OR region_id = :regionId)
        AND (:dateFrom IS NULL OR DATE(created_at) >= :dateFrom)
        AND (:dateTo IS NULL OR DATE(created_at) <= :dateTo)
        ORDER BY created_at DESC 
        LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}
    """)
    fun findWithFilters(
        search: String?,
        invoiceStatus: String?,
        regionId: String?,
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
        pageable: Pageable
    ): Flux<Invoice>
    
    /**
     * Подсчет общего количества счетов с фильтрацией
     */
    @Query("""
        SELECT COUNT(*) FROM invoice 
        WHERE (:search IS NULL OR agent_transaction_id ILIKE CONCAT('%', :search, '%'))
        AND (:invoiceStatus IS NULL OR invoice_status = :invoiceStatus)
        AND (:regionId IS NULL OR region_id = :regionId)
        AND (:dateFrom IS NULL OR DATE(created_at) >= :dateFrom)
        AND (:dateTo IS NULL OR DATE(created_at) <= :dateTo)
    """)
    fun countWithFilters(
        search: String?,
        invoiceStatus: String?,
        regionId: String?,
        dateFrom: LocalDate?,
        dateTo: LocalDate?
    ): Mono<Long>
} 