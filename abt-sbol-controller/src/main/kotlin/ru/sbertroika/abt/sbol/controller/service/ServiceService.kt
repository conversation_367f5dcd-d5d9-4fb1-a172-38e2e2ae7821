package ru.sbertroika.abt.sbol.controller.service

import org.slf4j.LoggerFactory
import org.springframework.dao.DataAccessException
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.output.repository.ServiceRepository
import ru.sbertroika.abt.sbol.model.entities.Service as ServiceEntity
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Service
class ServiceService(
    private val serviceRepository: ServiceRepository,
    private val entityTemplate: R2dbcEntityTemplate
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    fun createService(serviceDto: ServiceDto): Mono<ServiceEntity> {
        return try {
            val service = ServiceEntity(
                projectId = serviceDto.projectId,
                templateId = serviceDto.templateId,
                serviceCode = serviceDto.serviceCode,
                name = serviceDto.name,
                description = serviceDto.description,
                isSocial = serviceDto.isSocial,
                subscriptionType = serviceDto.subscriptionType,
                cost = serviceDto.cost,
                actionStartDate = serviceDto.actionStartDate,
                actionEndDate = serviceDto.actionEndDate,
                minReplenishmentAmount = serviceDto.minReplenishmentAmount,
                maxReplenishmentAmount = serviceDto.maxReplenishmentAmount,
                recommendedAmount = serviceDto.recommendedAmount
            )

            serviceRepository.save(service)
        } catch (e: DataAccessException) {
            log.error("Error create service", e)
            Mono.error(RuntimeException("Ошибка при создании услуги: ${e.message}", e))
        }
    }

    fun updateService(id: UUID, serviceDto: ServiceDto): Mono<ServiceEntity> {
        return try {
            serviceRepository.findById(id)
                .flatMap { existingService ->
                    val updatedService = existingService.copy(
                        projectId = serviceDto.projectId,
                        templateId = serviceDto.templateId,
                        serviceCode = serviceDto.serviceCode,
                        name = serviceDto.name,
                        description = serviceDto.description,
                        isSocial = serviceDto.isSocial,
                        subscriptionType = serviceDto.subscriptionType,
                        cost = serviceDto.cost,
                        actionStartDate = serviceDto.actionStartDate,
                        actionEndDate = serviceDto.actionEndDate,
                        minReplenishmentAmount = serviceDto.minReplenishmentAmount,
                        maxReplenishmentAmount = serviceDto.maxReplenishmentAmount,
                        recommendedAmount = serviceDto.recommendedAmount,
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    serviceRepository.save(updatedService)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при обновлении услуги: ${e.message}", e))
        }
    }

    fun getServiceById(id: UUID): Mono<ServiceEntity> {
        return try {
            serviceRepository.findById(id)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении услуги по ID: ${e.message}", e))
        }
    }

    fun getServiceByCode(serviceCode: String): Mono<ServiceEntity> {
        return try {
            serviceRepository.findByServiceCode(serviceCode)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении услуги по коду: ${e.message}", e))
        }
    }

    fun getServicesByProjectId(projectId: UUID): Flux<ServiceEntity> {
        return try {
            serviceRepository.findByProjectId(projectId)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении услуг по проекту: ${e.message}", e))
        }
    }

    fun getServicesBySubscriptionType(subscriptionType: String): Flux<ServiceEntity> {
        return try {
            serviceRepository.findBySubscriptionType(subscriptionType)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении услуг по типу подписки: ${e.message}", e))
        }
    }

    fun getServicesByIsSocial(isSocial: Boolean): Flux<ServiceEntity> {
        return try {
            serviceRepository.findByIsSocial(isSocial)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении услуг по социальному статусу: ${e.message}", e))
        }
    }

    fun getAllServices(page: Int = 0, size: Int = 20): Mono<PageResponse<ServiceEntity>> {
        return try {
            val servicesFlux = serviceRepository.findAllOrdered()
            val countMono = serviceRepository.countAll()
            
            Mono.zip(servicesFlux.collectList(), countMono)
                .map { tuple ->
                    val services = tuple.t1
                    val totalElements = tuple.t2
                    
                    // Применяем пагинацию в памяти
                    val startIndex = page * size
                    val endIndex = minOf(startIndex + size, services.size)
                    val pagedServices = if (startIndex < services.size) {
                        services.subList(startIndex, endIndex)
                    } else {
                        emptyList()
                    }
                    
                    PageResponse.of(pagedServices, totalElements, page + 1, size)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении списка услуг: ${e.message}", e))
        }
    }

    fun deleteService(id: UUID): Mono<Boolean> {
        return try {
            serviceRepository.existsById(id)
                .flatMap { exists ->
                    if (exists) {
                        serviceRepository.deleteById(id)
                            .then(Mono.just(true))
                    } else {
                        Mono.just(false)
                    }
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при удалении услуги: ${e.message}", e))
        }
    }
} 