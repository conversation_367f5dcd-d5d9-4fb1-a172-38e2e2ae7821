package ru.sbertroika.abt.sbol.controller.service

import org.springframework.dao.DataAccessException
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.output.repository.RegionProjectRepository
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Service
class RegionProjectService(
    private val regionProjectRepository: RegionProjectRepository
) {

    fun createRegionProject(regionProjectDto: RegionProjectDto): Mono<RegionProject> {
        return try {
            val regionProject = RegionProject(
                regionId = regionProjectDto.regionId,
                projectId = regionProjectDto.projectId,
                canDecodeHash = regionProjectDto.canDecodeHash,
                panDecodeKey = regionProjectDto.panDecodeKey
            )
            regionProjectRepository.save(regionProject)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при создании проекта региона: ${e.message}", e))
        }
    }

    fun updateRegionProject(id: UUID, regionProjectDto: RegionProjectDto): Mono<RegionProject> {
        return try {
            regionProjectRepository.findById(id)
                .flatMap { existingRegionProject ->
                    val updatedRegionProject = existingRegionProject.copy(
                        regionId = regionProjectDto.regionId,
                        projectId = regionProjectDto.projectId,
                        canDecodeHash = regionProjectDto.canDecodeHash,
                        panDecodeKey = regionProjectDto.panDecodeKey,
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    regionProjectRepository.save(updatedRegionProject)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при обновлении проекта региона: ${e.message}", e))
        }
    }

    fun getRegionProjectById(id: UUID): Mono<RegionProject> {
        return try {
            regionProjectRepository.findById(id)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении проекта региона по ID: ${e.message}", e))
        }
    }

    fun getRegionProjectsByRegionId(regionId: String): Flux<RegionProject> {
        return try {
            regionProjectRepository.findByRegionId(regionId)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении проектов региона: ${e.message}", e))
        }
    }

    fun getRegionProjectsByProjectId(projectId: UUID): Flux<RegionProject> {
        return try {
            regionProjectRepository.findByProjectId(projectId)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении проектов по ID проекта: ${e.message}", e))
        }
    }

    fun getRegionProjectByRegionIdAndProjectId(regionId: String, projectId: UUID): Mono<RegionProject> {
        return try {
            regionProjectRepository.findByRegionIdAndProjectId(regionId, projectId)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении проекта региона: ${e.message}", e))
        }
    }

    fun getAllRegionProjects(page: Int = 0, size: Int = 20): Mono<PageResponse<RegionProject>> {
        return try {
            val offset = page * size
            val regionProjectsFlux = regionProjectRepository.findAllWithPagination(size, offset)
            val countMono = regionProjectRepository.countAll()
            
            Mono.zip(regionProjectsFlux.collectList(), countMono)
                .map { tuple ->
                    val regionProjects = tuple.t1
                    val totalElements = tuple.t2
                    PageResponse.of(regionProjects, totalElements, page + 1, size)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении списка проектов региона: ${e.message}", e))
        }
    }

    fun deleteRegionProject(id: UUID): Mono<Boolean> {
        return try {
            regionProjectRepository.existsById(id)
                .flatMap { exists ->
                    if (exists) {
                        regionProjectRepository.deleteById(id)
                            .then(Mono.just(true))
                    } else {
                        Mono.just(false)
                    }
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при удалении проекта региона: ${e.message}", e))
        }
    }
} 