package ru.sbertroika.abt.sbol.controller.input.model

import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

/**
 * Ответ для списка продаж
 */
data class SalesResponse(
    val invoiceId: UUID,
    val orderId: String,
    val agentTransactionId: String,
    val invoiceStatus: String,
    val invoiceAmount: Long,
    val regionId: String,
    val orderStatus: String,
    val items: List<SalesItemResponse>,
    val cards: List<SalesCardResponse>,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)

/**
 * Ответ для детальной информации о продаже
 */
data class SalesDetailResponse(
    val invoiceId: UUID,
    val orderId: String,
    val agentTransactionId: String,
    val invoiceStatus: String,
    val invoiceAmount: Long,
    val regionId: String,
    val orderStatus: String,
    val items: List<SalesItemDetailResponse>,
    val cards: List<SalesCardDetailResponse>,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)

/**
 * Элемент продажи для списка
 */
data class SalesItemResponse(
    val id: UUID,
    val type: String, // PURCHASE или REPLENISHMENT
    val serviceName: String?,
    val description: String?,
    val cost: Long?
)

/**
 * Элемент продажи для детального просмотра
 */
data class SalesItemDetailResponse(
    val id: UUID,
    val type: String, // PURCHASE или REPLENISHMENT
    val serviceId: UUID?,
    val serviceName: String?,
    val cost: Long?,
    val descriptionText: String?,
    val actionStartDate: LocalDateTime?,
    val actionEndDate: LocalDateTime?,
    val replenishmentAmount: Long?
)

/**
 * Карта для списка продаж
 */
data class SalesCardResponse(
    val cardId: UUID?,
    val cardType: String,
    val pan: String?,
    val paymentSystem: String?
)

/**
 * Карта для детального просмотра
 */
data class SalesCardDetailResponse(
    val cardId: UUID?,
    val cardType: String,
    val pan: String?,
    val panHash: String?,
    val paymentSystem: String?
)

/**
 * Опция для выпадающих списков
 */
data class StatusOption(
    val label: String,
    val value: String?
) 