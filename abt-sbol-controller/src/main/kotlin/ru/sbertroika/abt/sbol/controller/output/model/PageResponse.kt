package ru.sbertroika.abt.sbol.controller.output.model

data class PageResponse<T>(
    val content: List<T>,
    val totalElements: Long,
    val totalPages: Int,
    val currentPage: Int,
    val pageSize: Int,
    val hasNext: <PERSON><PERSON><PERSON>,
    val hasPrevious: Boolean
) {
    companion object {
        fun <T> of(
            content: List<T>,
            totalElements: Long,
            currentPage: Int,
            pageSize: Int
        ): PageResponse<T> {
            val totalPages = if (totalElements > 0) {
                ((totalElements - 1) / pageSize + 1).toInt()
            } else {
                0
            }
            
            return PageResponse(
                content = content,
                totalElements = totalElements,
                totalPages = totalPages,
                currentPage = currentPage,
                pageSize = pageSize,
                hasNext = currentPage < totalPages,
                hasPrevious = currentPage > 1
            )
        }
    }
} 