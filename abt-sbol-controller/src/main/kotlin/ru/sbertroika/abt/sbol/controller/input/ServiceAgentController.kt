package ru.sbertroika.abt.sbol.controller.input

import kotlinx.coroutines.reactive.awaitSingle
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ru.sbertroika.abt.sbol.controller.input.model.ServiceAgentDto
import ru.sbertroika.abt.sbol.controller.output.model.ApiResponse
import ru.sbertroika.abt.sbol.controller.service.ServiceAgentService
import java.util.UUID

@RestController
@RequestMapping("/api/v1/abt/sbol/service-agents")
class ServiceAgentController(
    private val serviceAgentService: ServiceAgentService
) {

    @PostMapping
    suspend fun createServiceAgent(@RequestBody serviceAgentDto: ServiceAgentDto): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgent = serviceAgentService.createServiceAgent(serviceAgentDto).awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgent, "Связь услуги с агентом успешно создана"))
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(ApiResponse.error<Any>(e.message ?: "Ошибка создания связи услуги с агентом"))
        }
    }

    @PutMapping("/{id}")
    suspend fun updateServiceAgent(
        @PathVariable id: UUID,
        @RequestBody serviceAgentDto: ServiceAgentDto
    ): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgent = serviceAgentService.updateServiceAgent(id, serviceAgentDto).awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgent, "Связь услуги с агентом успешно обновлена"))
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(ApiResponse.error<Any>(e.message ?: "Ошибка обновления связи услуги с агентом"))
        }
    }

    @GetMapping("/{id}")
    suspend fun getServiceAgent(@PathVariable id: UUID): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgent = serviceAgentService.getServiceAgentById(id).awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgent, "Связь услуги с агентом найдена"))
        } catch (e: Exception) {
            ResponseEntity.notFound().build()
        }
    }

    @GetMapping
    suspend fun getAllServiceAgents(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgents = serviceAgentService.getAllServiceAgents(page, size).awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgents, "Список связей услуг с агентами получен"))
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(ApiResponse.error<Any>(e.message ?: "Ошибка получения списка связей услуг с агентами"))
        }
    }

    @GetMapping("/service/{serviceId}")
    suspend fun getServiceAgentsByServiceId(@PathVariable serviceId: UUID): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgents = serviceAgentService.getServiceAgentsByServiceId(serviceId).collectList().awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgents, "Связи услуги с агентами найдены"))
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(ApiResponse.error<Any>(e.message ?: "Ошибка получения связей услуги с агентами"))
        }
    }

    @GetMapping("/agent/{agentId}")
    suspend fun getServiceAgentsByAgentId(@PathVariable agentId: UUID): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgents = serviceAgentService.getServiceAgentsByAgentId(agentId).collectList().awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgents, "Связи агента с услугами найдены"))
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(ApiResponse.error<Any>(e.message ?: "Ошибка получения связей агента с услугами"))
        }
    }

    @GetMapping("/service/{serviceId}/agent/{agentId}")
    suspend fun getServiceAgentByServiceIdAndAgentId(
        @PathVariable serviceId: UUID,
        @PathVariable agentId: UUID
    ): ResponseEntity<ApiResponse<*>> {
        return try {
            val serviceAgent = serviceAgentService.getServiceAgentByServiceIdAndAgentId(serviceId, agentId).awaitSingle()
            ResponseEntity.ok(ApiResponse.success(serviceAgent, "Связь услуги с агентом найдена"))
        } catch (e: Exception) {
            ResponseEntity.notFound().build()
        }
    }

    @DeleteMapping("/{id}")
    suspend fun deleteServiceAgent(@PathVariable id: UUID): ResponseEntity<ApiResponse<*>> {
        return try {
            val deleted = serviceAgentService.deleteServiceAgent(id).awaitSingle()
            if (deleted) {
                ResponseEntity.ok(ApiResponse.success<Any>("", "Связь услуги с агентом успешно удалена"))
            } else {
                ResponseEntity.notFound().build()
            }
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(ApiResponse.error<Any>(e.message ?: "Ошибка удаления связи услуги с агентом"))
        }
    }
} 