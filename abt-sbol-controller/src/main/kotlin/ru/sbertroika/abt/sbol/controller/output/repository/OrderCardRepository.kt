package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.abt.sbol.model.entities.OrderCard
import java.util.UUID

@Repository
interface OrderCardRepository : ReactiveCrudRepository<OrderCard, UUID> {

    @Query("SELECT * FROM order_card WHERE order_id = :orderId")
    fun findByOrderId(orderId: UUID): Flux<OrderCard>

    @Query("SELECT * FROM order_card WHERE card_type = :cardType")
    fun findByCardType(cardType: String): Flux<OrderCard>

    @Query("SELECT * FROM order_card WHERE payment_system = :paymentSystem")
    fun findByPaymentSystem(paymentSystem: String): Flux<OrderCard>
} 