package ru.sbertroika.abt.sbol.controller.service

import org.slf4j.LoggerFactory
import org.springframework.dao.DataAccessException
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.controller.input.model.ServiceAgentDto
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.output.repository.ServiceAgentRepository
import ru.sbertroika.abt.sbol.model.entities.ServiceAgent as ServiceAgentEntity
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.UUID

@Service
class ServiceAgentService(
    private val serviceAgentRepository: ServiceAgentRepository
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    fun createServiceAgent(serviceAgentDto: ServiceAgentDto): Mono<ServiceAgentEntity> {
        return try {
            val serviceAgent = ServiceAgentEntity(
                serviceId = serviceAgentDto.serviceId,
                agentId = serviceAgentDto.agentId,
                agentVersion = serviceAgentDto.agentVersion
            )

            serviceAgentRepository.save(serviceAgent)
        } catch (e: DataAccessException) {
            log.error("Error create service agent", e)
            Mono.error(RuntimeException("Ошибка при создании связи услуги с агентом: ${e.message}", e))
        }
    }

    fun updateServiceAgent(id: UUID, serviceAgentDto: ServiceAgentDto): Mono<ServiceAgentEntity> {
        return try {
            serviceAgentRepository.findById(id)
                .flatMap { existingServiceAgent ->
                    val updatedServiceAgent = existingServiceAgent.copy(
                        serviceId = serviceAgentDto.serviceId,
                        agentId = serviceAgentDto.agentId,
                        agentVersion = serviceAgentDto.agentVersion
                    )
                    serviceAgentRepository.save(updatedServiceAgent)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при обновлении связи услуги с агентом: ${e.message}", e))
        }
    }

    fun getServiceAgentById(id: UUID): Mono<ServiceAgentEntity> {
        return try {
            serviceAgentRepository.findById(id)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении связи услуги с агентом по ID: ${e.message}", e))
        }
    }

    fun getServiceAgentsByServiceId(serviceId: UUID): Flux<ServiceAgentEntity> {
        return try {
            serviceAgentRepository.findByServiceId(serviceId)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении связей услуги с агентами: ${e.message}", e))
        }
    }

    fun getServiceAgentsByAgentId(agentId: UUID): Flux<ServiceAgentEntity> {
        return try {
            serviceAgentRepository.findByAgentId(agentId)
        } catch (e: DataAccessException) {
            Flux.error(RuntimeException("Ошибка при получении связей агента с услугами: ${e.message}", e))
        }
    }

    fun getServiceAgentByServiceIdAndAgentId(serviceId: UUID, agentId: UUID): Mono<ServiceAgentEntity> {
        return try {
            serviceAgentRepository.findByServiceIdAndAgentId(serviceId, agentId)
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении связи услуги с агентом: ${e.message}", e))
        }
    }

    fun getAllServiceAgents(page: Int = 0, size: Int = 20): Mono<PageResponse<ServiceAgentEntity>> {
        return try {
            val serviceAgentsFlux = serviceAgentRepository.findAllOrdered()
            val countMono = serviceAgentRepository.countAll()
            
            Mono.zip(serviceAgentsFlux.collectList(), countMono)
                .map { tuple ->
                    val serviceAgents = tuple.t1
                    val totalElements = tuple.t2
                    
                    // Применяем пагинацию в памяти
                    val startIndex = page * size
                    val endIndex = minOf(startIndex + size, serviceAgents.size)
                    val pagedServiceAgents = if (startIndex < serviceAgents.size) {
                        serviceAgents.subList(startIndex, endIndex)
                    } else {
                        emptyList()
                    }
                    
                    PageResponse.of(pagedServiceAgents, totalElements, page + 1, size)
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при получении списка связей услуг с агентами: ${e.message}", e))
        }
    }

    fun deleteServiceAgent(id: UUID): Mono<Boolean> {
        return try {
            serviceAgentRepository.existsById(id)
                .flatMap { exists ->
                    if (exists) {
                        serviceAgentRepository.deleteById(id)
                            .then(Mono.just(true))
                    } else {
                        Mono.just(false)
                    }
                }
        } catch (e: DataAccessException) {
            Mono.error(RuntimeException("Ошибка при удалении связи услуги с агентом: ${e.message}", e))
        }
    }
} 