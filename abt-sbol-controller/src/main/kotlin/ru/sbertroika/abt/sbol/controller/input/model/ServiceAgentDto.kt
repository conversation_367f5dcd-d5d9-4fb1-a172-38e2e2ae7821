package ru.sbertroika.abt.sbol.controller.input.model

import java.time.LocalDateTime
import java.util.UUID
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

data class ServiceAgentDto(
    val id: UUID? = null,
    @field:NotNull(message = "serviceId не может быть null")
    val serviceId: UUID,
    @field:NotNull(message = "agentId не может быть null")
    val agentId: UUID,
    @field:NotNull(message = "agentVersion не может быть null")
    @field:Positive(message = "agentVersion должен быть положительным числом")
    val agentVersion: Int,
    val createdAt: LocalDateTime? = null
) 