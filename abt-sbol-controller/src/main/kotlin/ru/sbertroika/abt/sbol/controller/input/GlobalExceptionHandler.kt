package ru.sbertroika.abt.sbol.controller.input

import org.springframework.dao.DataAccessException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.bind.support.WebExchangeBindException
import ru.sbertroika.abt.sbol.controller.output.model.ApiResponse
import reactor.core.publisher.Mono

@RestControllerAdvice
class GlobalExceptionHandler {

    @ExceptionHandler(WebExchangeBindException::class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleValidationExceptions(ex: WebExchangeBindException): Mono<ApiResponse<Unit>> {
        val errors = ex.bindingResult.fieldErrors.joinToString(", ") { "${it.field}: ${it.defaultMessage}" }
        
        return Mono.just(ApiResponse.error("Ошибка валидации", errors))
    }

    @ExceptionHandler(IllegalArgumentException::class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): Mono<ApiResponse<Unit>> {
        return Mono.just(ApiResponse.error("Некорректные данные", ex.message))
    }

    @ExceptionHandler(DataAccessException::class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    fun handleDataAccessException(ex: DataAccessException): Mono<ApiResponse<Unit>> {
        return Mono.just(ApiResponse.error("Ошибка доступа к базе данных", ex.message))
    }

    @ExceptionHandler(DataIntegrityViolationException::class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleDataIntegrityViolationException(ex: DataIntegrityViolationException): Mono<ApiResponse<Unit>> {
        return Mono.just(ApiResponse.error("Нарушение целостности данных", ex.message))
    }

    @ExceptionHandler(Exception::class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    fun handleGenericException(ex: Exception): Mono<ApiResponse<Unit>> {
        return Mono.just(ApiResponse.error("Внутренняя ошибка сервера", ex.message))
    }
} 