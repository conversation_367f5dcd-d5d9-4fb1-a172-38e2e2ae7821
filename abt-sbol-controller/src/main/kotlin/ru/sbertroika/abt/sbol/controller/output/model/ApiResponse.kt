package ru.sbertroika.abt.sbol.controller.output.model

data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val error: String? = null
) {
    companion object {
        fun <T> success(data: T, message: String? = null): ApiResponse<T> =
            ApiResponse(success = true, data = data, message = message)

        fun <T> error(error: String, message: String? = null): ApiResponse<T> =
            ApiResponse(success = false, error = error, message = message)
    }
} 