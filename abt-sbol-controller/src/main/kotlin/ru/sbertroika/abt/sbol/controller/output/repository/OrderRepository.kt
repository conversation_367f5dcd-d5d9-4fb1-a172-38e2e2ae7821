package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.Order
import java.util.UUID

@Repository
interface OrderRepository : ReactiveCrudRepository<Order, UUID> {

    @Query("SELECT * FROM \"order\" WHERE invoice_id = :invoiceId")
    fun findByInvoiceId(invoiceId: UUID): Mono<Order>

    @Query("SELECT * FROM \"order\" WHERE order_status = :status")
    fun findByStatus(status: String): Flux<Order>

    @Query("SELECT * FROM \"order\" ORDER BY created_at DESC")
    fun findAllOrdered(): Flux<Order>
} 