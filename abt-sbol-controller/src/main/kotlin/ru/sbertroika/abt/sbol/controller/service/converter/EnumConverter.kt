package ru.sbertroika.abt.sbol.controller.service.converter

import ru.sbertroika.abt.sbol.model.enums.SaleRuleLogic
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType

/**
 * Конвертеры для преобразования между enum и String
 */
object EnumConverter {
    
    fun SaleRuleType.toDatabaseValue(): String = this.name
    
    fun String.toSaleRuleType(): SaleRuleType = SaleRuleType.valueOf(this)
    
    fun SaleRuleLogic.toDatabaseValue(): String = this.name
    
    fun String.toSaleRuleLogic(): SaleRuleLogic = SaleRuleLogic.valueOf(this)
} 