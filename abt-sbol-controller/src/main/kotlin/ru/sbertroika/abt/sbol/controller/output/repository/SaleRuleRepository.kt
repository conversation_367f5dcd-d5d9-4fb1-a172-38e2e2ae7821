package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import java.util.UUID

@Repository
interface SaleRuleRepository : ReactiveCrudRepository<SaleRule, UUID> {

    @Query("SELECT * FROM sale_rule WHERE service_id = :serviceId")
    fun findByServiceId(serviceId: UUID): Flux<SaleRule>

    @Query("SELECT * FROM sale_rule WHERE service_id = :serviceId AND is_active = :isActive")
    fun findByServiceIdAndIsActive(serviceId: UUID, isActive: Boolean): Flux<SaleRule>

    @Query("SELECT * FROM sale_rule WHERE rule_type = :ruleType")
    fun findByRuleType(ruleType: String): Flux<SaleRule>

    @Query("SELECT * FROM sale_rule WHERE rule_logic = :ruleLogic")
    fun findByRuleLogic(ruleLogic: String): Flux<SaleRule>

    @Query("SELECT * FROM sale_rule WHERE is_active = :isActive")
    fun findByIsActive(isActive: Boolean): Flux<SaleRule>

    @Query("SELECT * FROM sale_rule ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    fun findAllWithPagination(limit: Int, offset: Int): Flux<SaleRule>

    @Query("SELECT COUNT(*) FROM sale_rule")
    fun countAll(): Mono<Long>
} 