package ru.sbertroika.abt.sbol.controller.service

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import mu.KotlinLogging
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.controller.input.model.*
import ru.sbertroika.abt.sbol.controller.output.repository.*
import ru.sbertroika.abt.sbol.model.entities.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID

@Service
class SalesService(
    private val invoiceRepository: InvoiceRepository,
    private val orderRepository: OrderRepository,
    private val orderCardRepository: OrderCardRepository,
    private val replenishmentItemRepository: ReplenishmentItemRepository,
    private val purchaseItemRepository: PurchaseItemRepository,
    private val serviceRepository: ServiceRepository
) {
    
    private val logger = KotlinLogging.logger {}
    
    /**
     * Получить список продаж с фильтрацией и пагинацией
     */
    suspend fun getSales(
        search: String?,
        invoiceStatus: String?,
        orderStatus: String?,
        regionId: String?,
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
        pageable: Pageable
    ): Page<SalesResponse> = withContext(Dispatchers.IO) {
        logger.info { "Получение списка продаж с фильтрами: search=$search, invoiceStatus=$invoiceStatus" }
        
        try {
            logger.info { "Начинаем запрос к базе данных" }
            
            // Получаем счета с фильтрацией
            val invoices = invoiceRepository.findWithFilters(
                search = search,
                invoiceStatus = invoiceStatus,
                regionId = regionId,
                dateFrom = dateFrom,
                dateTo = dateTo,
                pageable = pageable
            ).collectList().block() ?: emptyList()
            
            logger.info { "Получено ${invoices.size} счетов из базы данных" }
            
            // Получаем общее количество для пагинации
            val totalElements = invoiceRepository.countWithFilters(
                search = search,
                invoiceStatus = invoiceStatus,
                regionId = regionId,
                dateFrom = dateFrom,
                dateTo = dateTo
            ).block() ?: 0L
            
            logger.info { "Общее количество счетов: $totalElements" }
            
            // Преобразуем в ответы
            val salesResponses = invoices.map { invoice ->
                logger.info { "Обрабатываем счет: ${invoice.invoiceId}" }
                
                // Получаем связанный заказ
                val order = orderRepository.findByInvoiceId(invoice.invoiceId!!).block()
                logger.info { "Заказ для счета ${invoice.invoiceId}: ${order?.orderId}" }
                
                // Получаем карты заказа
                val orderCards = if (order != null) {
                    orderCardRepository.findByOrderId(order.orderId!!).collectList().block() ?: emptyList()
                } else {
                    emptyList()
                }
                
                logger.info { "Карт в заказе: ${orderCards.size}" }
                
                // Получаем элементы пополнения и покупок
                val items = mutableListOf<SalesItemResponse>()
                
                for (orderCard in orderCards) {
                    // Элементы пополнения
                    val replenishmentItems = replenishmentItemRepository.findByOrderCardId(orderCard.id!!).collectList().block() ?: emptyList()
                    for (replenishmentItem in replenishmentItems) {
                        items.add(
                            SalesItemResponse(
                                id = replenishmentItem.id!!,
                                type = "REPLENISHMENT",
                                serviceName = null,
                                description = replenishmentItem.description,
                                cost = replenishmentItem.replenishmentAmount
                            )
                        )
                    }
                    
                    // Элементы покупок
                    val purchaseItems = purchaseItemRepository.findByOrderCardId(orderCard.id!!).collectList().block() ?: emptyList()
                    for (purchaseItem in purchaseItems) {
                        val service = if (purchaseItem.serviceId != null) {
                            serviceRepository.findById(purchaseItem.serviceId).block()
                        } else null
                        
                        items.add(
                            SalesItemResponse(
                                id = purchaseItem.id!!,
                                type = "PURCHASE",
                                serviceName = service?.name,
                                description = purchaseItem.descriptionText,
                                cost = purchaseItem.cost
                            )
                        )
                    }
                }
                
                // Преобразуем карты
                val cards = orderCards.map { orderCard ->
                    SalesCardResponse(
                        cardId = orderCard.cardId,
                        cardType = orderCard.cardType,
                        pan = orderCard.pan,
                        paymentSystem = orderCard.paymentSystem
                    )
                }
                
                SalesResponse(
                    invoiceId = invoice.invoiceId!!,
                    orderId = order?.orderId?.toString() ?: "N/A",
                    agentTransactionId = invoice.agentTransactionId,
                    invoiceStatus = invoice.invoiceStatus,
                    invoiceAmount = invoice.invoiceAmount,
                    regionId = invoice.regionId,
                    orderStatus = order?.orderStatus ?: "N/A",
                    items = items,
                    cards = cards,
                    createdAt = LocalDateTime.now(), // Временно используем текущее время
                    updatedAt = LocalDateTime.now()  // Временно используем текущее время
                )
            }
            
            logger.info { "Успешно создано ${salesResponses.size} ответов" }
            return@withContext PageImpl(salesResponses, pageable, totalElements)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении списка продаж: ${e.message}" }
            throw e
        }
    }
    
    /**
     * Получить продажу по ID
     */
    suspend fun getSaleById(invoiceId: String): SalesDetailResponse = withContext(Dispatchers.IO) {
        logger.info { "Получение продажи по ID: $invoiceId" }
        
        try {
            val invoice = invoiceRepository.findById(UUID.fromString(invoiceId)).block()
                ?: throw IllegalArgumentException("Счет с ID $invoiceId не найден")
            
            // Получаем связанный заказ
            val order = orderRepository.findByInvoiceId(invoice.invoiceId!!).block()
            
            // Получаем карты заказа
            val orderCards = if (order != null) {
                orderCardRepository.findByOrderId(order.orderId!!).collectList().block() ?: emptyList()
            } else {
                emptyList()
            }
            
            // Получаем элементы пополнения и покупок
            val items = mutableListOf<SalesItemDetailResponse>()
            
            for (orderCard in orderCards) {
                // Элементы пополнения
                val replenishmentItems = replenishmentItemRepository.findByOrderCardId(orderCard.id!!).collectList().block() ?: emptyList()
                for (replenishmentItem in replenishmentItems) {
                    items.add(
                        SalesItemDetailResponse(
                            id = replenishmentItem.id!!,
                            type = "REPLENISHMENT",
                            serviceId = null,
                            serviceName = null,
                            cost = null,
                            descriptionText = replenishmentItem.description,
                            actionStartDate = null,
                            actionEndDate = null,
                            replenishmentAmount = replenishmentItem.replenishmentAmount
                        )
                    )
                }
                
                // Элементы покупок
                val purchaseItems = purchaseItemRepository.findByOrderCardId(orderCard.id!!).collectList().block() ?: emptyList()
                for (purchaseItem in purchaseItems) {
                    val service = if (purchaseItem.serviceId != null) {
                        serviceRepository.findById(purchaseItem.serviceId).block()
                    } else null
                    
                    items.add(
                        SalesItemDetailResponse(
                            id = purchaseItem.id!!,
                            type = "PURCHASE",
                            serviceId = purchaseItem.serviceId,
                            serviceName = service?.name,
                            cost = purchaseItem.cost,
                            descriptionText = purchaseItem.descriptionText,
                            actionStartDate = LocalDateTime.now(), // Временно используем текущее время
                            actionEndDate = LocalDateTime.now().plusMonths(1), // Временно используем +1 месяц
                            replenishmentAmount = null
                        )
                    )
                }
            }
            
            // Преобразуем карты
            val cards = orderCards.map { orderCard ->
                SalesCardDetailResponse(
                    cardId = orderCard.cardId,
                    cardType = orderCard.cardType,
                    pan = orderCard.pan,
                    panHash = orderCard.panHash,
                    paymentSystem = orderCard.paymentSystem
                )
            }

            return@withContext SalesDetailResponse(
                invoiceId = invoice.invoiceId!!,
                orderId = order?.orderId?.toString() ?: "N/A",
                agentTransactionId = invoice.agentTransactionId,
                invoiceStatus = invoice.invoiceStatus,
                invoiceAmount = invoice.invoiceAmount,
                regionId = invoice.regionId,
                orderStatus = order?.orderStatus ?: "N/A",
                items = items,
                cards = cards,
                createdAt = LocalDateTime.now(), // Временно используем текущее время
                updatedAt = LocalDateTime.now()  // Временно используем текущее время
            )
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении продажи $invoiceId" }
            throw e
        }
    }
    
    /**
     * Получить статусы счетов
     */
    suspend fun getInvoiceStatuses(): List<StatusOption> {
        return listOf(
            StatusOption("Все статусы", null),
            StatusOption("Создан", "CREATED"),
            StatusOption("Оплачен", "PAID"),
            StatusOption("Отменен", "CANCELED"),
            StatusOption("Устарел", "OUTDATED")
        )
    }
    
    /**
     * Получить статусы заказов
     */
    suspend fun getOrderStatuses(): List<StatusOption> {
        return listOf(
            StatusOption("Все статусы", null),
            StatusOption("Создан", "CREATED"),
            StatusOption("Завершен", "COMPLETED"),
            StatusOption("Отменен", "CANCELED")
        )
    }
    
    /**
     * Получить типы карт
     */
    suspend fun getCardTypes(): List<StatusOption> {
        return listOf(
            StatusOption("Все типы", null),
            StatusOption("Транспортная", "TRANSPORT"),
            StatusOption("IPS", "IPS")
        )
    }
    
    /**
     * Получить платежные системы
     */
    suspend fun getPaymentSystems(): List<StatusOption> {
        return listOf(
            StatusOption("Все системы", null),
            StatusOption("VISA", "VISA"),
            StatusOption("MASTERCARD", "MASTERCARD"),
            StatusOption("MIR", "MIR")
        )
    }
} 