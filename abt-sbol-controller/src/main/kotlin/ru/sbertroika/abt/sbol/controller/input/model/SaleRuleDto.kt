package ru.sbertroika.abt.sbol.controller.input.model

import ru.sbertroika.abt.sbol.model.enums.SaleRuleLogic
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType
import java.time.LocalDateTime
import java.util.UUID
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.PositiveOrZero

data class SaleRuleDto(
    val id: UUID? = null,
    @field:NotNull(message = "serviceId не может быть null")
    val serviceId: UUID,
    @field:NotNull(message = "ruleType не может быть null")
    val ruleType: SaleRuleType,
    @field:NotNull(message = "ruleLogic не может быть null")
    val ruleLogic: SaleRuleLogic,
    @field:PositiveOrZero(message = "startDay должен быть положительным числом или нулем")
    val startDay: Int? = null,
    @field:PositiveOrZero(message = "endDay должен быть положительным числом или нулем")
    val endDay: Int? = null,
    @field:PositiveOrZero(message = "startMonth должен быть положительным числом или нулем")
    val startMonth: Int? = null,
    @field:PositiveOrZero(message = "endMonth должен быть положительным числом или нулем")
    val endMonth: Int? = null,
    @field:PositiveOrZero(message = "startWeek должен быть положительным числом или нулем")
    val startWeek: Int? = null,
    @field:PositiveOrZero(message = "endWeek должен быть положительным числом или нулем")
    val endWeek: Int? = null,
    @field:PositiveOrZero(message = "minCardBalance должен быть положительным числом или нулем")
    val minCardBalance: Long? = null,
    @field:PositiveOrZero(message = "maxCardBalance должен быть положительным числом или нулем")
    val maxCardBalance: Long? = null,
    val isActive: Boolean = true,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null
) 