package ru.sbertroika.abt.sbol.controller.input

import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.controller.input.model.ServiceDto
import ru.sbertroika.abt.sbol.controller.output.model.ApiResponse
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.service.ServiceService
import java.util.UUID

@RestController
@RequestMapping("/api/v1/abt/sbol/services")
class ServiceController(
    private val serviceService: ServiceService
) {

    @PostMapping
    fun createService(@Valid @RequestBody serviceDto: ServiceDto): Mono<ApiResponse<ru.sbertroika.abt.sbol.model.entities.Service>> {
        return serviceService.createService(serviceDto)
            .map { service ->
                ApiResponse.success(service, "Услуга успешно создана")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ru.sbertroika.abt.sbol.model.entities.Service>("Ошибка создания услуги: ${error.message}"))
            }
    }

    @PutMapping("/{id}")
    fun updateService(
        @PathVariable id: UUID,
        @Valid @RequestBody serviceDto: ServiceDto
    ): Mono<ApiResponse<ru.sbertroika.abt.sbol.model.entities.Service>> {
        return serviceService.updateService(id, serviceDto)
            .map { service ->
                ApiResponse.success(service, "Услуга успешно обновлена")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ru.sbertroika.abt.sbol.model.entities.Service>("Ошибка обновления услуги: ${error.message}"))
            }
    }

    @GetMapping("/{id}")
    fun getServiceById(@PathVariable id: UUID): Mono<ApiResponse<ru.sbertroika.abt.sbol.model.entities.Service>> {
        return serviceService.getServiceById(id)
            .map { service ->
                ApiResponse.success(service)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ru.sbertroika.abt.sbol.model.entities.Service>("Услуга с id $id не найдена"))
            }
    }

    @GetMapping("/code/{serviceCode}")
    fun getServiceByCode(@PathVariable serviceCode: String): Mono<ApiResponse<ru.sbertroika.abt.sbol.model.entities.Service>> {
        return serviceService.getServiceByCode(serviceCode)
            .map { service ->
                ApiResponse.success(service)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ru.sbertroika.abt.sbol.model.entities.Service>("Услуга с кодом $serviceCode не найдена"))
            }
    }

    @GetMapping("/project/{projectId}")
    fun getServicesByProjectId(@PathVariable projectId: UUID): Mono<ApiResponse<List<ru.sbertroika.abt.sbol.model.entities.Service>>> {
        return serviceService.getServicesByProjectId(projectId)
            .collectList()
            .map { services ->
                ApiResponse.success(services)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<ru.sbertroika.abt.sbol.model.entities.Service>>("Ошибка получения услуг по проекту: ${error.message}"))
            }
    }

    @GetMapping("/subscription-type/{subscriptionType}")
    fun getServicesBySubscriptionType(@PathVariable subscriptionType: String): Mono<ApiResponse<List<ru.sbertroika.abt.sbol.model.entities.Service>>> {
        return serviceService.getServicesBySubscriptionType(subscriptionType)
            .collectList()
            .map { services ->
                ApiResponse.success(services)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<ru.sbertroika.abt.sbol.model.entities.Service>>("Ошибка получения услуг по типу подписки: ${error.message}"))
            }
    }

    @GetMapping("/social/{isSocial}")
    fun getServicesByIsSocial(@PathVariable isSocial: Boolean): Mono<ApiResponse<List<ru.sbertroika.abt.sbol.model.entities.Service>>> {
        return serviceService.getServicesByIsSocial(isSocial)
            .collectList()
            .map { services ->
                ApiResponse.success(services)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<ru.sbertroika.abt.sbol.model.entities.Service>>("Ошибка получения услуг по социальному статусу: ${error.message}"))
            }
    }

    @GetMapping
    fun getAllServices(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<PageResponse<ru.sbertroika.abt.sbol.model.entities.Service>>> {
        return serviceService.getAllServices(page, size)
            .map { services ->
                ApiResponse.success(services)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<PageResponse<ru.sbertroika.abt.sbol.model.entities.Service>>("Ошибка получения списка услуг: ${error.message}"))
            }
    }

    @DeleteMapping("/{id}")
    fun deleteService(@PathVariable id: UUID): Mono<ApiResponse<Unit>> {
        return serviceService.deleteService(id)
            .map { deleted ->
                if (deleted) {
                    ApiResponse.success(Unit, "Услуга успешно удалена")
                } else {
                    ApiResponse.error("Услуга с id $id не найдена")
                }
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Unit>("Ошибка удаления услуги: ${error.message}"))
            }
    }
} 