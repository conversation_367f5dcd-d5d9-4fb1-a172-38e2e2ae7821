package ru.sbertroika.abt.sbol.controller.input.model

import java.time.LocalDateTime
import java.util.UUID
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

data class RegionProjectDto(
    val id: UUID? = null,
    @field:NotBlank(message = "regionId не может быть пустым")
    val regionId: String,
    @field:NotNull(message = "projectId не может быть null")
    val projectId: UUID,
    @field:NotNull(message = "canDecodeHash не может быть null")
    val canDecodeHash: <PERSON>olean,
    val panDecodeKey: String? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null
) 