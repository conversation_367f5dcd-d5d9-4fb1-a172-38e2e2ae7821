package ru.sbertroika.abt.sbol.controller.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.Service
import java.util.UUID

@Repository
interface ServiceRepository : ReactiveCrudRepository<Service, UUID> {

    @Query("SELECT * FROM service WHERE project_id = :projectId")
    fun findByProjectId(projectId: UUID): Flux<Service>

    @Query("SELECT * FROM service WHERE service_code = :serviceCode")
    fun findByServiceCode(serviceCode: String): Mono<Service>

    @Query("SELECT * FROM service WHERE subscription_type = :subscriptionType")
    fun findBySubscriptionType(subscriptionType: String): Flux<Service>

    @Query("SELECT * FROM service WHERE is_social = :isSocial")
    fun findByIsSocial(isSocial: Boolean): Flux<Service>

    @Query("SELECT * FROM service ORDER BY created_at DESC")
    fun findAllOrdered(): Flux<Service>

    @Query("SELECT COUNT(*) FROM service")
    fun countAll(): Mono<Long>
} 