package ru.sbertroika.abt.sbol.controller.input.model

import java.time.LocalDateTime
import java.util.UUID
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.PositiveOrZero
import java.time.LocalDate

data class ServiceDto(
    val id: UUID? = null,
    @field:NotNull(message = "projectId не может быть null")
    val projectId: UUID,
    @field:NotNull(message = "templateId не может быть null")
    val templateId: UUID,
    @field:NotBlank(message = "serviceCode не может быть пустым")
    val serviceCode: String,
    @field:NotBlank(message = "name не может быть пустым")
    val name: String,
    val description: String? = null,
    @field:NotNull(message = "isSocial не может быть null")
    val isSocial: Boolean,
    @field:NotNull(message = "subscriptionType не может быть null")
    val subscriptionType: String,
    @field:PositiveOrZero(message = "cost должен быть положительным числом или нулем")
    val cost: Long? = null,
    val actionStartDate: LocalDate? = null,
    val actionEndDate: LocalDate? = null,
    @field:PositiveOrZero(message = "minReplenishmentAmount должен быть положительным числом или нулем")
    val minReplenishmentAmount: Long? = null,
    @field:PositiveOrZero(message = "maxReplenishmentAmount должен быть положительным числом или нулем")
    val maxReplenishmentAmount: Long? = null,
    @field:PositiveOrZero(message = "recommendedAmount должен быть положительным числом или нулем")
    val recommendedAmount: Long? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null
) 