package ru.sbertroika.abt.sbol.controller.config

import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Contact
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.info.License
import io.swagger.v3.oas.models.servers.Server
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
open class OpenApiConfig {

    @Bean
    open fun openAPI(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("ABT SBOL Controller API")
                    .description(
                        """
                        REST API для управления справочниками ABT SBOL.
                        
                        ## Описание
                        API предоставляет интерфейс для выполнения CRUD операций над справочными данными:
                        - **Service** - услуги/товары
                        - **RegionProject** - связки регионов и проектов  
                        - **SaleRule** - правила продаж
                        
                        ## Основные операции
                        - Создание, чтение, обновление, удаление справочных данных
                        - Поиск по различным критериям
                        - Пагинация для больших списков
                        - Валидация входящих данных
                        
                        ## Аутентификация
                        В текущей версии API не требует аутентификации.
                        
                        ## Формат ответов
                        Все ответы возвращаются в формате:
                        ```json
                        {
                          "success": true,
                          "data": {...},
                          "message": "Описание операции"
                        }
                        ```
                        """.trimIndent()
                    )
                    .version("1.0.0")
                    .contact(
                        Contact()
                            .name("ABT Team")
                            .email("<EMAIL>")
                    )
                    .license(
                        License()
                            .name("MIT")
                            .url("https://opensource.org/licenses/MIT")
                    )
            )
            .addServersItem(
                Server()
                    .url("http://localhost:8080")
                    .description("Локальная среда разработки")
            )
            .addServersItem(
                Server()
                    .url("http://localhost:8083")
                    .description("Локальная среда разработки (dev профиль)")
            )
            .addServersItem(
                Server()
                    .url("https://api.abt.sbertroika.tech")
                    .description("Продакшн среда")
            )
    }
} 