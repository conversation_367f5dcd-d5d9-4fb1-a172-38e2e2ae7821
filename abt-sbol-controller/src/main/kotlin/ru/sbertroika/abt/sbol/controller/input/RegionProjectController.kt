package ru.sbertroika.abt.sbol.controller.input

import jakarta.validation.Valid
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.controller.input.model.RegionProjectDto
import ru.sbertroika.abt.sbol.controller.output.model.ApiResponse
import ru.sbertroika.abt.sbol.controller.output.model.PageResponse
import ru.sbertroika.abt.sbol.controller.service.RegionProjectService
import ru.sbertroika.abt.sbol.model.entities.RegionProject
import java.util.UUID

@RestController
@RequestMapping("/api/v1/abt/sbol/region-projects")
class RegionProjectController(
    private val regionProjectService: RegionProjectService
) {

    @PostMapping
    fun createRegionProject(@Valid @RequestBody regionProjectDto: RegionProjectDto): Mono<ApiResponse<RegionProject>> {
        return regionProjectService.createRegionProject(regionProjectDto)
            .map { regionProject ->
                ApiResponse.success(regionProject, "Проект региона успешно создан")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<RegionProject>("Ошибка создания проекта региона: ${error.message}"))
            }
    }

    @PutMapping("/{id}")
    fun updateRegionProject(
        @PathVariable id: UUID,
        @Valid @RequestBody regionProjectDto: RegionProjectDto
    ): Mono<ApiResponse<RegionProject>> {
        return regionProjectService.updateRegionProject(id, regionProjectDto)
            .map { regionProject ->
                ApiResponse.success(regionProject, "Проект региона успешно обновлен")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<RegionProject>("Ошибка обновления проекта региона: ${error.message}"))
            }
    }

    @GetMapping("/{id}")
    fun getRegionProjectById(@PathVariable id: UUID): Mono<ApiResponse<RegionProject>> {
        return regionProjectService.getRegionProjectById(id)
            .map { regionProject ->
                ApiResponse.success(regionProject)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<RegionProject>("Проект региона с id $id не найден"))
            }
    }

    @GetMapping("/region/{regionId}")
    fun getRegionProjectsByRegionId(@PathVariable regionId: String): Mono<ApiResponse<List<RegionProject>>> {
        return regionProjectService.getRegionProjectsByRegionId(regionId)
            .collectList()
            .map { regionProjects ->
                ApiResponse.success(regionProjects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<RegionProject>>("Ошибка получения проектов региона: ${error.message}"))
            }
    }

    @GetMapping("/project/{projectId}")
    fun getRegionProjectsByProjectId(@PathVariable projectId: UUID): Mono<ApiResponse<List<RegionProject>>> {
        return regionProjectService.getRegionProjectsByProjectId(projectId)
            .collectList()
            .map { regionProjects ->
                ApiResponse.success(regionProjects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<RegionProject>>("Ошибка получения проектов по ID проекта: ${error.message}"))
            }
    }

    @GetMapping("/region/{regionId}/project/{projectId}")
    fun getRegionProjectByRegionIdAndProjectId(
        @PathVariable regionId: String,
        @PathVariable projectId: UUID
    ): Mono<ApiResponse<RegionProject>> {
        return regionProjectService.getRegionProjectByRegionIdAndProjectId(regionId, projectId)
            .map { regionProject ->
                ApiResponse.success(regionProject)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<RegionProject>("Проект региона не найден"))
            }
    }

    @GetMapping
    fun getAllRegionProjects(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<PageResponse<RegionProject>>> {
        return regionProjectService.getAllRegionProjects(page, size)
            .map { regionProjects ->
                ApiResponse.success(regionProjects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<PageResponse<RegionProject>>("Ошибка получения списка проектов региона: ${error.message}"))
            }
    }

    @DeleteMapping("/{id}")
    fun deleteRegionProject(@PathVariable id: UUID): Mono<ApiResponse<Unit>> {
        return regionProjectService.deleteRegionProject(id)
            .map { deleted ->
                if (deleted) {
                    ApiResponse.success(Unit, "Проект региона успешно удален")
                } else {
                    ApiResponse.error("Проект региона с id $id не найден")
                }
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Unit>("Ошибка удаления проекта региона: ${error.message}"))
            }
    }
} 