package ru.sbertroika.abt.sbol.controller.input

import mu.KotlinLogging
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ru.sbertroika.abt.sbol.controller.service.SalesService
import ru.sbertroika.abt.sbol.controller.input.model.*
import java.time.LocalDate
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/v1/abt/sbol/sales")
class SalesController(
    private val salesService: SalesService
) {
    
    private val logger = KotlinLogging.logger {}
    
    /**
     * Простой тестовый эндпоинт
     */
    @GetMapping("/test")
    suspend fun test(): ResponseEntity<Map<String, String>> {
        logger.info { "Тестовый запрос" }
        return ResponseEntity.ok(mapOf("status" to "OK", "message" to "API работает"))
    }
    
    /**
     * Получить список продаж с фильтрацией и пагинацией
     */
    @GetMapping
    suspend fun getSales(
        @RequestParam(required = false) search: String?,
        @RequestParam(required = false) invoiceStatus: String?,
        @RequestParam(required = false) orderStatus: String?,
        @RequestParam(required = false) regionId: String?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) dateFrom: LocalDate?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) dateTo: LocalDate?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "15") size: Int,
        @RequestParam(defaultValue = "createdAt") sortBy: String,
        @RequestParam(defaultValue = "desc") sortDirection: String
    ): ResponseEntity<Page<SalesResponse>> {
        logger.info { "Запрос списка продаж: search=$search, invoiceStatus=$invoiceStatus, page=$page, size=$size" }
        
        try {
            val sort = if (sortDirection == "desc") {
                Sort.by(Sort.Direction.DESC, sortBy)
            } else {
                Sort.by(Sort.Direction.ASC, sortBy)
            }
            
            val pageable = PageRequest.of(page, size, sort)
            
            val sales = salesService.getSales(
                search = search,
                invoiceStatus = invoiceStatus,
                orderStatus = orderStatus,
                regionId = regionId,
                dateFrom = dateFrom,
                dateTo = dateTo,
                pageable = pageable
            )
            
            return ResponseEntity.ok(sales)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении списка продаж" }
            return ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * Получить продажу по ID
     */
    @GetMapping("/{invoiceId}")
    suspend fun getSale(@PathVariable invoiceId: String): ResponseEntity<SalesDetailResponse> {
        logger.info { "Запрос продажи по ID: $invoiceId" }
        
        try {
            val sale = salesService.getSaleById(invoiceId)
            return ResponseEntity.ok(sale)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении продажи $invoiceId" }
            return ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * Получить статусы счетов
     */
    @GetMapping("/invoice-statuses")
    suspend fun getInvoiceStatuses(): ResponseEntity<List<StatusOption>> {
        logger.info { "Запрос статусов счетов" }
        
        try {
            val statuses = salesService.getInvoiceStatuses()
            return ResponseEntity.ok(statuses)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении статусов счетов" }
            return ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * Получить статусы заказов
     */
    @GetMapping("/order-statuses")
    suspend fun getOrderStatuses(): ResponseEntity<List<StatusOption>> {
        logger.info { "Запрос статусов заказов" }
        
        try {
            val statuses = salesService.getOrderStatuses()
            return ResponseEntity.ok(statuses)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении статусов заказов" }
            return ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * Получить типы карт
     */
    @GetMapping("/card-types")
    suspend fun getCardTypes(): ResponseEntity<List<StatusOption>> {
        logger.info { "Запрос типов карт" }
        
        try {
            val types = salesService.getCardTypes()
            return ResponseEntity.ok(types)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении типов карт" }
            return ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * Получить платежные системы
     */
    @GetMapping("/payment-systems")
    suspend fun getPaymentSystems(): ResponseEntity<List<StatusOption>> {
        logger.info { "Запрос платежных систем" }
        
        try {
            val systems = salesService.getPaymentSystems()
            return ResponseEntity.ok(systems)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении платежных систем" }
            return ResponseEntity.internalServerError().build()
        }
    }
} 