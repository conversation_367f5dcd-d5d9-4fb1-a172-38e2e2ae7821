server:
  port: 8080

spring:
  application:
    name: abt-sbol-controller
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # R2DBC конфигурация вместо JDBC
  r2dbc:
    url: r2dbc:${DB_URL:postgresql://postgres/abt_sbol_gate}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

# Конфигурация для контроллера
controller:
  pagination:
    default-page-size: 20
    max-page-size: 100 