server:
  port: 8083

spring:
  application:
    name: abt-sbol-controller
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  r2dbc:
    url: r2dbc:${DB_URL:postgresql://localhost:5434/abt_sbol_gate}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    ru.sbertroika.abt.sbol.controller: INFO
    org.springframework.web: INFO
    reactor.netty: WARN
    org.springframework.data.r2dbc: DEBUG
    org.flywaydb: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Конфигурация для контроллера
controller:
  pagination:
    default-page-size: 20
    max-page-size: 100 