# ABT SBOL Controller

Микросервис для управления справочниками ABT SBOL через REST API.

## Описание

Микросервис предоставляет REST API для выполнения CRUD операций над справочными данными:
- **Service** - услуги/товары
- **RegionProject** - связки регионов и проектов
- **SaleRule** - правила продаж

## Структура проекта

```
src/main/kotlin/ru/sbertroika/abt/sbol/controller/
├── config/                 # Конфигурации
├── input/                  # Входящие данные
│   ├── model/             # DTO модели
│   └── *.kt               # REST контроллеры
├── service/               # Сервисный слой
├── output/                # Исходящие данные
│   ├── model/             # Модели ответов
│   └── repository/        # Репозитории
└── AbtSbolControllerApplication.kt
```

## API Endpoints

### Services (`/api/v1/services`)

- `POST /` - Создание услуги
- `PUT /{id}` - Обновление услуги
- `GET /{id}` - Получение услуги по ID
- `GET /code/{serviceCode}` - Получение услуги по коду
- `GET /project/{projectId}` - Получение услуг по проекту
- `GET /subscription-type/{subscriptionType}` - Получение услуг по типу подписки
- `GET /social/{isSocial}` - Получение услуг по социальному статусу
- `GET /?page={page}&size={size}` - Получение всех услуг с пагинацией
- `DELETE /{id}` - Удаление услуги

### Region Projects (`/api/v1/region-projects`)

- `POST /` - Создание связки региона и проекта
- `PUT /{id}` - Обновление связки
- `GET /{id}` - Получение связки по ID
- `GET /region/{regionId}` - Получение связок по региону
- `GET /project/{projectId}` - Получение связок по проекту
- `GET /region/{regionId}/project/{projectId}` - Получение связки по региону и проекту
- `GET /?page={page}&size={size}` - Получение всех связок с пагинацией
- `DELETE /{id}` - Удаление связки

### Sale Rules (`/api/v1/sale-rules`)

- `POST /` - Создание правила продаж
- `PUT /{id}` - Обновление правила продаж
- `GET /{id}` - Получение правила по ID
- `GET /service/{serviceId}` - Получение правил по услуге
- `GET /service/{serviceId}/active` - Получение активных правил по услуге
- `GET /rule-type/{ruleType}` - Получение правил по типу
- `GET /active/{isActive}` - Получение правил по активности
- `GET /?page={page}&size={size}` - Получение всех правил с пагинацией
- `DELETE /{id}` - Удаление правила

## Запуск

### Локальная разработка

```bash
./gradlew :abt-sbol-controller:bootRun
```

### С профилем разработки

```bash
./gradlew :abt-sbol-controller:bootRun --args='--spring.profiles.active=dev'
```

### Сборка JAR

```bash
./gradlew :abt-sbol-controller:build
```

## Конфигурация

### Переменные окружения

- `DB_USER` - имя пользователя базы данных
- `DB_PASSWORD` - пароль базы данных
- `DB_URL` - URL базы данных
- `SERVER_PORT` - порт сервера (по умолчанию 8080)

### Профили

- `dev` - для разработки (порт 8083)
- `prod` - для продакшена

## База данных

Микросервис использует готовую базу данных PostgreSQL, которая создается миграциями из модуля `abt-sbol-gate`. 
Flyway отключен в данном микросервисе.

## Мониторинг

Доступны следующие endpoints для мониторинга:
- `/actuator/health` - состояние сервиса
- `/actuator/info` - информация о сервисе
- `/actuator/metrics` - метрики

## Swagger UI

Документация API доступна по адресу:
- `http://localhost:8080/swagger-ui.html` (основной профиль)
- `http://localhost:8083/swagger-ui.html` (dev профиль) 