### Создание услуги
POST http://localhost:8080/api/v1/abt/sbol/services
Content-Type: application/json

{
  "projectId": "550e8400-e29b-41d4-a716-446655440000",
  "templateId": "550e8400-e29b-41d4-a716-446655440001",
  "serviceCode": "TRANSPORT_001",
  "name": "Транспортный абонемент",
  "description": "Абонемент на проезд в общественном транспорте",
  "isSocial": false,
  "subscriptionType": "TRAVEL",
  "cost": 150000,
  "actionStartDate": "2024-01-01T00:00:00",
  "actionEndDate": "2024-12-31T23:59:59"
}

### Получение услуги по ID
GET http://localhost:8080/api/v1/abt/sbol/services/{{serviceId}}

### Получение услуги по коду
GET http://localhost:8080/api/v1/abt/sbol/services/code/TRANSPORT_001

### Получение всех услуг с пагинацией
GET http://localhost:8080/api/v1/abt/sbol/services?page=0&size=10

### Обновление услуги
PUT http://localhost:8080/api/v1/abt/sbol/services/{{serviceId}}
Content-Type: application/json

{
  "projectId": "550e8400-e29b-41d4-a716-446655440000",
  "templateId": "550e8400-e29b-41d4-a716-446655440001",
  "serviceCode": "TRANSPORT_001",
  "name": "Транспортный абонемент (обновленный)",
  "description": "Обновленный абонемент на проезд в общественном транспорте",
  "isSocial": false,
  "subscriptionType": "TRAVEL",
  "cost": 160000,
  "actionStartDate": "2024-01-01T00:00:00",
  "actionEndDate": "2024-12-31T23:59:59"
}

### Удаление услуги
DELETE http://localhost:8080/api/v1/abt/sbol/services/{{serviceId}}

### Создание связки региона и проекта
POST http://localhost:8080/api/v1/abt/sbol/region-projects
Content-Type: application/json

{
  "regionId": "47",
  "projectId": "550e8400-e29b-41d4-a716-446655440000",
  "canDecodeHash": true,
  "panDecodeKey": "secret-key-123"
}

### Получение связки по ID
GET http://localhost:8080/api/v1/abt/sbol/region-projects/{{regionProjectId}}

### Получение связок по региону
GET http://localhost:8080/api/v1/abt/sbol/region-projects/region/47

### Получение связки по региону и проекту
GET http://localhost:8080/api/v1/abt/sbol/region-projects/region/47/project/550e8400-e29b-41d4-a716-446655440000

### Обновление связки
PUT http://localhost:8080/api/v1/abt/sbol/region-projects/{{regionProjectId}}
Content-Type: application/json

{
  "regionId": "47",
  "projectId": "550e8400-e29b-41d4-a716-446655440000",
  "canDecodeHash": false,
  "panDecodeKey": null
}

### Удаление связки
DELETE http://localhost:8080/api/v1/abt/sbol/region-projects/{{regionProjectId}}

### Создание правила продаж
POST http://localhost:8080/api/v1/abt/sbol/sale-rules
Content-Type: application/json

{
  "serviceId": "{{serviceId}}",
  "ruleType": "MONTHLY_RANGE",
  "ruleLogic": "AND",
  "startDay": 1,
  "endDay": 10,
  "startMonth": 1,
  "endMonth": 12,
  "isActive": true
}

### Получение правила по ID
GET http://localhost:8080/api/v1/abt/sbol/sale-rules/{{saleRuleId}}

### Получение правил по услуге
GET http://localhost:8080/api/v1/abt/sbol/sale-rules/service/{{serviceId}}

### Получение активных правил по услуге
GET http://localhost:8080/api/v1/abt/sbol/sale-rules/service/{{serviceId}}/active

### Получение правил по типу
GET http://localhost:8080/api/v1/abt/sbol/sale-rules/rule-type/MONTHLY_RANGE

### Обновление правила продаж
PUT http://localhost:8080/api/v1/abt/sbol/sale-rules/{{saleRuleId}}
Content-Type: application/json

{
  "serviceId": "{{serviceId}}",
  "ruleType": "MONTHLY_RANGE",
  "ruleLogic": "OR",
  "startDay": 1,
  "endDay": 15,
  "startMonth": 1,
  "endMonth": 12,
  "isActive": true
}

### Удаление правила продаж
DELETE http://localhost:8080/api/v1/abt/sbol/sale-rules/{{saleRuleId}}

### Проверка здоровья сервиса
GET http://localhost:8080/actuator/health

### Получение информации о сервисе
GET http://localhost:8080/actuator/info 