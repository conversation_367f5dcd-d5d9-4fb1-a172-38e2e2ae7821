version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: abt_sbol_gate
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  abt-sbol-controller:
    build: .
    ports:
      - "8083:8080"
    environment:
      SPRING_PROFILES_ACTIVE: dev
      DB_USER: postgres
      DB_PASSWORD: postgres
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data: 