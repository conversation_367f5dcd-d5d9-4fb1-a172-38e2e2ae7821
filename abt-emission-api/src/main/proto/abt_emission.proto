syntax = "proto3";

package ru.sbertroika.abt.emission.v1;

option java_multiple_files = true;
option java_package = "ru.sbertroika.abt.emission.v1";

import "common.proto";
import "google/protobuf/empty.proto";

service ABTEmissionService {
  rpc emission(EmissionRequest) returns (EmissionResponse);
  rpc cardInfo(CardInfoRequest) returns (CardInfoRequestResponse);
  rpc cardTypes(google.protobuf.Empty) returns (CardTypesResponse);
  rpc makeCard(MakeCardRequest) returns (MakeCardResponse);
}

message CardType {
  string id = 1;
  string region = 2;                  // Наименование средства предъявления
  string name = 3;                    // Наименование средства предъявления
  string description = 4;             // Детальная информация о средстве предъявления
  string mask = 5;                    // Маска ввода печатного номера
  string prefix = 6;                  // Префикс для полного номера карты
  string img = 10;                    // Ссылка на изображение средства предъявления
  string preview = 11;                // Ссылка на иконку средства предъявления
  map<string, string> patterns = 12;  // Условие проверки валидности печатного номера
  optional string projectId = 13;     // Идентификатор проекта
}

message EmissionRequest {
  string number = 1;
  string uid = 2;
  string region = 3;
  string type = 4;
  string projectId = 5;
}

message EmissionResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    string cardId = 2;
  }
}

message CardInfoRequest {
  string cardId = 1;
}

message CardInfo {
  string id = 1;
  string number = 2;
  string uid = 3;
  string region = 4;
  CardType type = 5;
  string projectId = 6;
}

message CardInfoRequestResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    CardInfo info = 2;
  }
}

message CardTypes {
  repeated CardType list = 1;
}

message CardTypesResponse {
  oneof response {
    common.v1.OperationError error = 1;
    CardTypes types = 2;
  }
}

message MakeCardRequest {
  string number = 1;
  string cardTypeId = 2;
}

message MakeCardResult {
  string cardNum = 1;
  string region = 2;
}

message MakeCardResponse {
  oneof response {
    common.v1.OperationError error = 1;
    MakeCardResult result = 2;
  }
}
