# ABT SBOL Gate

Микросервис для работы с SBOL API и управления базой данных Agent Gate SBOL.

## Описание

Этот микросервис предоставляет:
- REST API для работы с SBOL
- Управление базой данных PostgreSQL через R2DBC
- Миграции Flyway
- Интеграцию с ABT Emission Service

## Технологии

- Spring Boot 3.5.4
- Spring WebFlux (реактивный веб-фреймворк)
- Spring Data R2DBC (реактивный доступ к БД)
- PostgreSQL (база данных)
- Flyway (миграции БД)
- gRPC (для интеграции с ABT Emission)

## Конфигурация

### База данных

Настройки подключения к PostgreSQL:

```yaml
spring:
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/abt_sbol_gate
    username: postgres
    password: postgres
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
```

Переменные окружения:
- `DB_HOST` - хост базы данных (по умолчанию: localhost)
- `DB_PORT` - порт базы данных (по умолчанию: 5432)
- `DB_NAME` - имя базы данных (по умолчанию: abt_sbol_gate)
- `DB_USERNAME` - имя пользователя (по умолчанию: postgres)
- `DB_PASSWORD` - пароль (по умолчанию: postgres)

### ABT Emission Service

```yaml
service:
  abt_emission_url: abt-emission.abt.svc.cluster.local:5000
```

## Структура базы данных

База данных содержит следующие основные таблицы:

1. **region_project** - связка регионов и проектов
2. **service** - услуги/товары
3. **service_agent** - связка услуг и агентов
4. **sale_rule** - правила продаж (включая ограничения по балансу карты)
5. **invoice** - счета
6. **order** - заказы
7. **order_card** - карты в заказе
8. **replenishment_item** - элементы пополнения
9. **purchase_item** - элементы покупки
10. **card** - кэш информации о картах

## Запуск

1. Убедитесь, что PostgreSQL запущен и доступен
2. Создайте базу данных `abt_sbol_gate`
3. Запустите приложение:

```bash
./gradlew :abt-sbol-gate:bootRun
```

Миграции Flyway автоматически создадут все необходимые таблицы при первом запуске.

## API

Микросервис предоставляет REST API на порту 8081. Документация API доступна по адресу:
- Swagger UI: http://localhost:8081/swagger-ui.html
- OpenAPI Spec: http://localhost:8081/v3/api-docs

## Мониторинг

Health check доступен по адресу: http://localhost:8081/actuator/health 