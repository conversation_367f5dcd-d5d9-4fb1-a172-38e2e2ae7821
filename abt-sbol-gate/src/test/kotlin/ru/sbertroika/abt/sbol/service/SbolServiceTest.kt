package ru.sbertroika.abt.sbol.service

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.mockito.ArgumentCaptor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import ru.sbertroika.abt.sbol.model.*
import ru.sbertroika.abt.sbol.model.enums.PaymentSystem
import ru.sbertroika.abt.sbol.model.enums.InvoiceStatus
import ru.sbertroika.abt.sbol.model.entities.Invoice
import ru.sbertroika.abt.sbol.output.repository.InvoiceRepository
import ru.sbertroika.abt.sbol.output.repository.OrderRepository
import ru.sbertroika.abt.sbol.output.repository.OrderCardRepository
import ru.sbertroika.abt.sbol.output.repository.ReplenishmentItemRepository
import ru.sbertroika.abt.sbol.output.repository.PurchaseItemRepository
import ru.sbertroika.abt.sbol.service.impl.SbolServiceImpl
import java.math.BigDecimal
import java.sql.Timestamp
import java.util.UUID

// Простой unit тест без Spring контекста
class SbolServiceUnitTest {

    @Test
    fun `updateInvoiceStatus should return false when invoice not found`() = runTest {
        // Given
        val mockRepository = mock(InvoiceRepository::class.java)
        val mockR2dbcEntityTemplate = mock(R2dbcEntityTemplate::class.java)
        val sbolService = SbolServiceImpl(
            abtEmissionService = mock(),
            emvEmissionService = mock(),
            abtGateService = mock(),
            saleRuleService = mock(),
            invoiceRepository = mockRepository,
            orderRepository = mock(),
            orderCardRepository = mock(),
            replenishmentItemRepository = mock(),
            purchaseItemRepository = mock()
        )
        
        val invoiceId = "2245dd54-f0d2-49da-91a3-4e2640321bd5"
        val uuid = UUID.fromString(invoiceId)
        
        `when`(mockRepository.findById(uuid)).thenReturn(reactor.core.publisher.Mono.empty())
        
        // When
        val result = sbolService.updateInvoiceStatus(
            regionId = "47",
            invoiceId = invoiceId,
            agentTransactionId = "trx-123",
            invoiceStatus = InvoiceStatus.PAID
        )
        
        // Then
        assertFalse(result)
        verify(mockRepository).findById(uuid)
        verify(mockRepository, never()).save(any())
    }

    @Test
    fun `updateInvoiceStatus should return true when invoice found and updated`() = runTest {
        // Given
        val mockRepository = mock(InvoiceRepository::class.java)
        val mockR2dbcEntityTemplate = mock(R2dbcEntityTemplate::class.java)
        val sbolService = SbolServiceImpl(
            abtEmissionService = mock(),
            emvEmissionService = mock(),
            abtGateService = mock(),
            saleRuleService = mock(),
            invoiceRepository = mockRepository,
            orderRepository = mock(),
            orderCardRepository = mock(),
            replenishmentItemRepository = mock(),
            purchaseItemRepository = mock()
        )
        
        val invoiceId = "2245dd54-f0d2-49da-91a3-4e2640321bd5"
        val uuid = UUID.fromString(invoiceId)
        val existingInvoice = Invoice(
            invoiceId = uuid,
            agentTransactionId = "trx-123",
            invoiceStatus = "CREATED",
            invoiceAmount = 1000L,
            regionId = "47",
            createdAt = Timestamp(System.currentTimeMillis()),
            updatedAt = Timestamp(System.currentTimeMillis())
        )
        
        `when`(mockRepository.findById(uuid)).thenReturn(reactor.core.publisher.Mono.just(existingInvoice))
        `when`(mockRepository.save(any())).thenReturn(reactor.core.publisher.Mono.just(existingInvoice))
        
        // When
        val result = sbolService.updateInvoiceStatus(
            regionId = "47",
            invoiceId = invoiceId,
            agentTransactionId = "trx-123",
            invoiceStatus = InvoiceStatus.PAID
        )
        
        // Then
        assertTrue(result)
        verify(mockRepository).findById(uuid)
        verify(mockRepository).save(any())
    }
}

@SpringBootTest
class SbolServiceTest {

    @Autowired
    private lateinit var sbolService: SbolService

    @Test
    fun `getAvailableOperationsForTransportCard should return valid response`() = runTest {
        // Given
        val regionId = "47"
        val pan = "****************"
        val agentId = "test-agent"
        val clientCert = "test-cert"

        // When
        val response = sbolService.getAvailableOperationsForTransportCard(
            regionId = regionId,
            pan = pan,
            agentId = agentId,
        )

        // Then
        assertNotNull(response)
        assertNotNull(response.card)
        assertEquals(pan, response.card?.pan)
        assertNotNull(response.cardAccount)
        assertTrue(response.cardAccount?.balanceAmount!! > BigDecimal.ZERO)
        assertNotNull(response.counterReplenishment)
        assertTrue(response.counterReplenishment?.allowed == true)
        assertNotNull(response.servicePurchase)
        assertTrue(response.servicePurchase?.allowed == true)
        assertFalse(response.servicePurchase?.services.isNullOrEmpty())
    }

    @Test
    fun `getAvailableOperationsForBankCard should return valid response`() = runTest {
        // Given
        val regionId = "47"
        val ipsName = "MIR"
        val panHash = "0009B988BDE0A5B322C0685C5D806D2632C09324"
        val agentId = "test-agent"
        val clientCert = "test-cert"

        // When
        val response = sbolService.getAvailableOperationsForBankCard(
            regionId = regionId,
            ipsName = ipsName,
            panHash = panHash,
            agentId = agentId,
        )

        // Then
        assertNotNull(response)
        assertNotNull(response.card)
        assertEquals(panHash, response.card?.panHash)
        assertEquals(PaymentSystem.MIR, response.card?.paymentSystem)
        assertNotNull(response.cardAccount)
        assertTrue(response.cardAccount?.balanceAmount!! > BigDecimal.ZERO)
        assertNotNull(response.counterReplenishment)
        assertTrue(response.counterReplenishment?.allowed == true)
        assertNotNull(response.servicePurchase)
        assertTrue(response.servicePurchase?.allowed == true)
        assertFalse(response.servicePurchase?.services.isNullOrEmpty())
    }

    @Test
    fun `createOrder should return valid invoice`() = runTest {
        // Given
        val regionId = "47"
        val agentTransactionId = "trx-*********"
        val order = Order(
            orderCards = listOf(
                OrderCard(
                    transportCard = TransportCard(pan = "****************"),
                    replenishment = Replenishment(
                        amount = BigDecimal("10000"),
                        type = "VALUE"
                    )
                )
            )
        )
        val agentId = "test-agent"
        val clientCert = "test-cert"

        // When
        val invoice = sbolService.createOrder(
            regionId = regionId,
            agentTransactionId = agentTransactionId,
            order = order,
            agentId = agentId,
        )

        // Then
        assertNotNull(invoice)
        assertNotNull(invoice.invoiceId)
        assertEquals(InvoiceStatus.CREATED, invoice.invoiceStatus)
        assertEquals(BigDecimal("10000"), invoice.invoiceAmount)
        assertEquals(agentTransactionId, invoice.agentTransactionId)
        assertNotNull(invoice.order)
        assertEquals("order-$agentTransactionId", invoice.order.orderId)
        assertEquals(OrderStatus.CREATED, invoice.order.orderStatus)
    }

    @Test
    fun `createOrder should create invoice with generated ID`() = runTest {
        // Given
        val mockInvoiceRepository = mock(InvoiceRepository::class.java)
        val mockOrderRepository = mock(OrderRepository::class.java)
        val mockOrderCardRepository = mock(OrderCardRepository::class.java)
        val mockReplenishmentItemRepository = mock(ReplenishmentItemRepository::class.java)
        val mockPurchaseItemRepository = mock(PurchaseItemRepository::class.java)
        val mockR2dbcEntityTemplate = mock(R2dbcEntityTemplate::class.java)
        
        val sbolService = SbolServiceImpl(
            abtEmissionService = mock(),
            emvEmissionService = mock(),
            abtGateService = mock(),
            saleRuleService = mock(),
            invoiceRepository = mockInvoiceRepository,
            orderRepository = mockOrderRepository,
            orderCardRepository = mockOrderCardRepository,
            replenishmentItemRepository = mockReplenishmentItemRepository,
            purchaseItemRepository = mockPurchaseItemRepository
        )
        
        val regionId = "47"
        val agentTransactionId = "trx-*********"
        val order = Order(
            orderCards = listOf(
                OrderCard(
                    transportCard = TransportCard(pan = "****************"),
                    replenishment = Replenishment(
                        amount = BigDecimal("10000"),
                        type = "VALUE"
                    ),
                    purchaseItems = emptyList()
                )
            )
        )
        val agentId = "test-agent"
        
        // Мокаем сохранение invoice с сгенерированным ID
        val generatedInvoiceId = UUID.randomUUID()
        val savedInvoice = Invoice(
            invoiceId = generatedInvoiceId,
            agentTransactionId = agentTransactionId,
            invoiceStatus = "CREATED",
            invoiceAmount = 10000L,
            regionId = regionId,
            createdAt = Timestamp(System.currentTimeMillis()),
            updatedAt = Timestamp(System.currentTimeMillis())
        )
        
        // Мокаем сохранение order с сгенерированным ID
        val generatedOrderId = UUID.randomUUID()
        val savedOrder = ru.sbertroika.abt.sbol.model.entities.Order(
            orderId = generatedOrderId,
            invoiceId = generatedInvoiceId,
            orderStatus = "CREATED",
            createdAt = Timestamp(System.currentTimeMillis()),
            updatedAt = Timestamp(System.currentTimeMillis())
        )
        
        `when`(mockInvoiceRepository.findByAgentTransactionId(agentTransactionId))
            .thenReturn(reactor.core.publisher.Mono.empty())
        `when`(mockInvoiceRepository.save(any())).thenReturn(reactor.core.publisher.Mono.just(savedInvoice))
        `when`(mockR2dbcEntityTemplate.insert(any<ru.sbertroika.abt.sbol.model.entities.Order>())).thenReturn(reactor.core.publisher.Mono.just(savedOrder))
        `when`(mockOrderCardRepository.save(any())).thenReturn(reactor.core.publisher.Mono.just(mock<ru.sbertroika.abt.sbol.model.entities.OrderCard>()))
        
        // When
        val result = sbolService.createOrder(
            regionId = regionId,
            agentTransactionId = agentTransactionId,
            order = order,
            agentId = agentId
        )
        
        // Then
        assertNotNull(result)
        assertEquals(generatedInvoiceId, result.invoiceId)
        assertEquals(InvoiceStatus.CREATED, result.invoiceStatus)
        assertEquals(BigDecimal("10000"), result.invoiceAmount)
        assertEquals(agentTransactionId, result.agentTransactionId)
        assertEquals(generatedOrderId, result.order.orderId) // Проверяем, что orderId был сгенерирован базой данных
        
        // Проверяем, что save был вызван с null ID для invoice
        val invoiceCaptor = ArgumentCaptor.forClass(Invoice::class.java)
        verify(mockInvoiceRepository).save(invoiceCaptor.capture())
        assertNull(invoiceCaptor.value.invoiceId) // ID должен быть null для INSERT
        
        // Проверяем, что insert был вызван для order с null orderId
        val orderCaptor = ArgumentCaptor.forClass(ru.sbertroika.abt.sbol.model.entities.Order::class.java)
        verify(mockR2dbcEntityTemplate).insert(orderCaptor.capture())
        assertNull(orderCaptor.value.orderId) // orderId должен быть null для автогенерации
    }

    @Test
    fun `updateInvoiceStatus should return success`() = runTest {
        // Given
        val regionId = "47"
        val invoiceId = "test-invoice-id"
        val agentTransactionId = "trx-*********"
        val invoiceStatus = InvoiceStatus.PAID

        // When
        val success = sbolService.updateInvoiceStatus(
            regionId = regionId,
            invoiceId = invoiceId,
            agentTransactionId = agentTransactionId,
            invoiceStatus = invoiceStatus,
        )

        // Then
        assertTrue(success)
    }
} 