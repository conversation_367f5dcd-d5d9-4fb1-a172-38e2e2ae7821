package ru.sbertroika.abt.sbol.config

import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Contact
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.info.License
import io.swagger.v3.oas.models.servers.Server
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
open class OpenApiConfig {

    @Bean
    open fun openAPI(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("SBOL REST API")
                    .description(
                        """
                        REST API для работы с Сбербанк Онлайн (SBOL) интеграцией.
                        
                        ## Описание
                        API предоставляет интерфейс для работы с транспортными и банковскими картами через Сбербанк Онлайн.
                        Поддерживает операции пополнения счетов и приобретения услуг.
                        
                        ## Аутентификация
                        API использует заголовки для аутентификации:
                        - `X-Agent-ID` - идентификатор агента
                        - `X-Client-Cert` - клиентский сертификат
                        
                        ## Основные операции
                        - Получение доступных операций для транспортных карт
                        - Получение доступных операций для банковских карт
                        - Выставление счетов
                        - Подтверждение статуса операций
                        """.trimIndent()
                    )
                    .version("1.0.0")
                    .contact(
                        Contact()
                            .name("Agent Gateway Team")
                            .email("<EMAIL>")
                    )
                    .license(
                        License()
                            .name("MIT")
                            .url("https://opensource.org/licenses/MIT")
                    )
            )
            .addServersItem(
                Server()
                    .url("http://localhost:8081")
                    .description("Локальная среда разработки")
            )
            .addServersItem(
                Server()
                    .url("https://api.agent-gateway.com")
                    .description("Продакшн среда")
            )
    }
} 