package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.abt.sbol.model.entities.Order
import java.util.UUID

@Repository
interface OrderRepository : ReactiveCrudRepository<Order, UUID> {
    
    @Query("SELECT * FROM \"order\" WHERE invoice_id = :invoiceId")
    fun findByInvoiceId(invoiceId: UUID): reactor.core.publisher.Mono<Order>
    
    @Query("SELECT * FROM \"order\" WHERE order_status = :status")
    fun findByStatus(status: String): reactor.core.publisher.Flux<Order>
} 