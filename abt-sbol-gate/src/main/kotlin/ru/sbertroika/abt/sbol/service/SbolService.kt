package ru.sbertroika.abt.sbol.service

import ru.sbertroika.abt.sbol.model.AvailableOperationsResponse
import ru.sbertroika.abt.sbol.model.Invoice
import ru.sbertroika.abt.sbol.model.enums.InvoiceStatus
import ru.sbertroika.abt.sbol.model.Order

/**
 * Сервис для работы с SBOL API
 */
interface SbolService {

    /**
     * Получить доступные операции для транспортной карты
     */
    suspend fun getAvailableOperationsForTransportCard(
        regionId: String,
        pan: String,
        agentId: String,
    ): AvailableOperationsResponse

    /**
     * Получить доступные операции для банковской карты
     */
    suspend fun getAvailableOperationsForBankCard(
        regionId: String,
        ipsName: String,
        panHash: String,
        agentId: String,
    ): AvailableOperationsResponse

    /**
     * Создать заказ
     */
    suspend fun createOrder(
        regionId: String,
        agentTransactionId: String,
        order: Order,
        agentId: String,
    ): Invoice

    /**
     * Обновить статус счета
     */
    suspend fun updateInvoiceStatus(
        regionId: String,
        invoiceId: String,
        agentTransactionId: String,
        invoiceStatus: InvoiceStatus,
    ): Boolean
} 