package ru.sbertroika.abt.sbol.input

import mu.KotlinLogging
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ServerWebExchange
import ru.sbertroika.abt.sbol.config.AuthorizationRESTFilter
import ru.sbertroika.abt.sbol.model.AvailableOperationsResponse
import ru.sbertroika.abt.sbol.model.enums.InvoiceStatus
import ru.sbertroika.abt.sbol.model.Order
import ru.sbertroika.abt.sbol.service.SbolService
import java.math.BigDecimal

@RestController
@RequestMapping("/sbol/regions/{regionId}")
class SbolController(
    private val sbolService: SbolService,
) {

    private val logger = KotlinLogging.logger {}

    @GetMapping("/cards/transport/{pan}/available-operations")
    suspend fun getAvailableOperationsForTransportCard(
        @PathVariable regionId: String,
        @PathVariable pan: String,
        exchange: ServerWebExchange,
    ): ResponseEntity<AvailableOperationsResponse> {
        logger.info { "Запрос доступных операций для транспортной карты: pan=$pan, regionId=$regionId" }

        val agentId = getAgentIdFromHeaders(
            exchange.request.headers.getFirst("X-Agent-ID"),
            exchange.request.headers.getFirst("X-Client-Cert")
        )
        
        if (agentId.isNullOrBlank()) {
            return ResponseEntity.badRequest()
                .body(AvailableOperationsResponse(errorMsg = "Отсутствуют обязательные заголовки аутентификации"))
        }
        
        return try {
            val response = sbolService.getAvailableOperationsForTransportCard(
                regionId = regionId,
                pan = pan,
                agentId = agentId,
            )
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении доступных операций для транспортной карты" }
            ResponseEntity.badRequest()
                .body(AvailableOperationsResponse(errorMsg = "Карта не найдена в кэше"))
        }
    }

    @PostMapping("/cards/ips/{ipsName}/{panHash}/available-operations")
    suspend fun getAvailableOperationsForBankCard(
        @PathVariable regionId: String,
        @PathVariable ipsName: String,
        @PathVariable panHash: String,
        exchange: ServerWebExchange,
    ): ResponseEntity<AvailableOperationsResponse> {
        logger.info { "Запрос доступных операций для банковской карты: panHash=$panHash, ipsName=$ipsName, regionId=$regionId" }

        val agentId = getAgentIdFromHeaders(
            exchange.request.headers.getFirst("X-Agent-ID"),
            exchange.request.headers.getFirst("X-Client-Cert")
        )

        if (agentId.isNullOrBlank()) {
            return ResponseEntity.badRequest()
                .body(AvailableOperationsResponse(errorMsg = "Отсутствуют обязательные заголовки аутентификации"))
        }
        
        return try {
            val response = sbolService.getAvailableOperationsForBankCard(
                regionId = regionId,
                ipsName = ipsName,
                panHash = panHash,
                agentId = agentId,
            )
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении доступных операций для банковской карты" }
            ResponseEntity.badRequest()
                .body(AvailableOperationsResponse(errorMsg = "Карта не найдена в кэше"))
        }
    }

    @PostMapping("/invoices/one-click")
    suspend fun createOrder(
        @PathVariable regionId: String,
        @RequestBody request: CreateOrderRequest,
        exchange: ServerWebExchange,
    ): ResponseEntity<CreateOrderResponse> {
        logger.info { "Запрос на создание заказа: agentTransactionId=${request.agentTransactionId}, regionId=$regionId" }

        val agentId = getAgentIdFromHeaders(
            exchange.request.headers.getFirst("X-Agent-ID"),
            exchange.request.headers.getFirst("X-Client-Cert")
        )

        if (agentId.isNullOrBlank()) {
            return ResponseEntity.badRequest()
                .body(CreateOrderResponse(errorMsg = "Отсутствуют обязательные заголовки аутентификации"))
        }
        
        return try {
            val invoice = sbolService.createOrder(
                regionId = regionId,
                agentTransactionId = request.agentTransactionId,
                order = request.order,
                agentId = agentId,
            )
            
            val response = CreateOrderResponse(
                invoiceId = invoice.invoiceId.toString(),
                invoiceStatus = invoice.invoiceStatus.name,
                invoiceAmount = invoice.invoiceAmount,
                agentTransactionId = invoice.agentTransactionId,
                order = invoice.order,
            )
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при создании заказа" }
            ResponseEntity.badRequest()
                .body(CreateOrderResponse(errorMsg = "Ошибка при создании заказа"))
        }
    }

    @PostMapping("/invoices/{invoiceId}/status")
    suspend fun updateInvoiceStatus(
        @PathVariable regionId: String,
        @PathVariable invoiceId: String,
        @RequestBody request: UpdateStatusRequest,
        exchange: ServerWebExchange,
    ): ResponseEntity<UpdateStatusResponse> {
        logger.info { "Запрос на обновление статуса счета: invoiceId=$invoiceId, status=${request.invoiceStatus}" }

        val agentId = getAgentIdFromHeaders(
            exchange.request.headers.getFirst("X-Agent-ID"),
            exchange.request.headers.getFirst("X-Client-Cert")
        )

        if (agentId.isNullOrBlank()) {
            return ResponseEntity.badRequest()
                .body(UpdateStatusResponse(error = "Отсутствуют обязательные заголовки аутентификации"))
        }
        
        return try {
            val success = sbolService.updateInvoiceStatus(
                regionId = regionId,
                invoiceId = invoiceId,
                agentTransactionId = request.agentTransactionId,
                invoiceStatus = InvoiceStatus.valueOf(request.invoiceStatus),
            )
            
            if (success) {
                ResponseEntity.ok(UpdateStatusResponse(status = "SUCCESS"))
            } else {
                ResponseEntity.badRequest()
                    .body(UpdateStatusResponse(error = "Счет не найден"))
            }
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при обновлении статуса счета" }
            ResponseEntity.badRequest()
                .body(UpdateStatusResponse(error = "Ошибка при обновлении статуса"))
        }
    }

    private fun getAgentIdFromHeaders(agentId: String?, clientCert: String?): String = agentId
        ?: clientCert?.let {
            AuthorizationRESTFilter.getCNFromHeader(clientCert)
        } ?: ""
}

// Модели запросов и ответов для REST API
data class CreateOrderRequest(
    val agentTransactionId: String,
    val order: Order,
)

data class CreateOrderResponse(
    val invoiceId: String? = null,
    val invoiceStatus: String? = null,
    val invoiceAmount: BigDecimal? = null,
    val agentTransactionId: String? = null,
    val order: Order? = null,
    val errorMsg: String? = null,
)

data class UpdateStatusRequest(
    val agentTransactionId: String,
    val invoiceStatus: String,
)

data class UpdateStatusResponse(
    val status: String? = null,
    val error: String? = null,
) 