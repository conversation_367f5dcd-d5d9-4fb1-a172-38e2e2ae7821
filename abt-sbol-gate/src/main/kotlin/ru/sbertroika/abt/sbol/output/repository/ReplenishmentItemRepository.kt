package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.abt.sbol.model.entities.ReplenishmentItem
import java.util.UUID

@Repository
interface ReplenishmentItemRepository : ReactiveCrudRepository<ReplenishmentItem, UUID> {
    
    @Query("SELECT * FROM replenishment_item WHERE order_card_id = :orderCardId")
    fun findByOrderCardId(orderCardId: UUID): Flux<ReplenishmentItem>
    
    @Query("SELECT * FROM replenishment_item WHERE item_type = :itemType")
    fun findByItemType(itemType: String): Flux<ReplenishmentItem>
} 