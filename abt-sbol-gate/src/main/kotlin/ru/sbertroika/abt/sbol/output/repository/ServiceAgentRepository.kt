package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.abt.sbol.model.entities.ServiceAgent
import java.util.UUID

@Repository
interface ServiceAgentRepository : ReactiveCrudRepository<ServiceAgent, UUID> {
    
    @Query("SELECT * FROM service_agent WHERE agent_id = :agentId")
    fun findByAgentId(agentId: UUID): Flux<ServiceAgent>
    
    @Query("SELECT * FROM service_agent WHERE service_id = :serviceId")
    fun findByServiceId(serviceId: UUID): Flux<ServiceAgent>
    
    @Query("SELECT * FROM service_agent WHERE service_id = :serviceId AND agent_id = :agentId")
    fun findByServiceIdAndAgentId(serviceId: UUID, agentId: UUID): Flux<ServiceAgent>
    
    @Query("SELECT DISTINCT sa.* FROM service_agent sa " +
           "INNER JOIN service s ON sa.service_id = s.id " +
           "WHERE s.project_id = :projectId AND sa.agent_id = :agentId")
    fun findAgentServicesByProjectId(projectId: UUID, agentId: UUID): Flux<ServiceAgent>
} 