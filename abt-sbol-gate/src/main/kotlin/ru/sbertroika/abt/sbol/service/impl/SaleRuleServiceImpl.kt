package ru.sbertroika.abt.sbol.service.impl

import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.reactive.awaitSingle
import mu.KotlinLogging
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.model.ActionRange
import ru.sbertroika.abt.sbol.model.Service as SbolService
import ru.sbertroika.abt.sbol.model.ServiceDescription
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType
import ru.sbertroika.abt.sbol.model.enums.IntervalLength
import ru.sbertroika.abt.sbol.service.converter.EnumConverter.toSaleRuleType
import ru.sbertroika.abt.sbol.service.converter.EnumConverter.toSubscriptionType
import ru.sbertroika.abt.sbol.output.repository.SaleRuleRepository
import ru.sbertroika.abt.sbol.output.repository.ServiceAgentRepository
import ru.sbertroika.abt.sbol.output.repository.ServiceRepository
import ru.sbertroika.abt.sbol.service.SaleRuleService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.temporal.WeekFields
import java.util.UUID

@Service
class SaleRuleServiceImpl(
    private val serviceRepository: ServiceRepository,
    private val saleRuleRepository: SaleRuleRepository,
    private val serviceAgentRepository: ServiceAgentRepository
) : SaleRuleService {

    private val logger = KotlinLogging.logger {}

    override suspend fun getAvailableServices(cardId: UUID, projectId: UUID, agentId: String): List<SbolService> {
        logger.info { "Получение доступных услуг для карты: cardId=$cardId, projectId=$projectId, agentId=$agentId" }
        
        try {
            // 1. Получаем все услуги с активными правилами продажи для проекта
            val services = serviceRepository.findServicesWithActiveRulesByProjectId(projectId).collectList().awaitSingle()
            
            if (services.isEmpty()) {
                logger.warn { "Не найдено услуг с активными правилами продажи для проекта $projectId" }
                return emptyList()
            }
            
            // 2. Получаем услуги, доступные агенту
            val agentServices = serviceAgentRepository.findAgentServicesByProjectId(projectId, UUID.fromString(agentId))
                .collectList()
                .awaitSingle()
            val agentServiceIds = agentServices.map { it.serviceId }.toSet()
            
            logger.debug { "Агенту $agentId доступно ${agentServiceIds.size} услуг из ${services.size} общих" }
            
            // 3. Фильтруем услуги по правилам продажи и правам агента
            val availableServices = mutableListOf<SbolService>()
            
            for (service in services) {
                // Проверяем, что услуга доступна агенту
                if (!agentServiceIds.contains(service.id)) {
                    logger.debug { "Услуга ${service.id} недоступна агенту $agentId" }
                    continue
                }
                
                // Проверяем правила продажи
                if (isServiceAvailableForCard(service, cardId, projectId)) {
                    val sbolService = convertToSbolService(service)
                    availableServices.add(sbolService)
                }
            }
            
            logger.info { "Найдено ${availableServices.size} доступных услуг для карты $cardId" }
            return availableServices
            
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении доступных услуг для карты $cardId" }
            return emptyList()
        }
    }
    
    /**
     * Проверяет, доступна ли услуга для карты на основе правил продажи
     */
    private suspend fun isServiceAvailableForCard(service: ru.sbertroika.abt.sbol.model.entities.Service, cardId: UUID, projectId: UUID): Boolean {
        val saleRules = saleRuleRepository.findByServiceIdAndActive(service.id!!).collectList().awaitSingle()
        
        if (saleRules.isEmpty()) {
            logger.debug { "Для услуги ${service.id} не найдено активных правил продажи" }
            return false
        }
        
        // Проверяем каждое правило
        for (rule in saleRules) {
            if (!isRuleSatisfied(rule, cardId)) {
                logger.debug { "Правило ${rule.id} для услуги ${service.id} не выполнено" }
                return false
            }
        }
        
        return true
    }
    
    /**
     * Проверяет выполнение правила продажи
     */
    private fun isRuleSatisfied(rule: ru.sbertroika.abt.sbol.model.entities.SaleRule, cardId: UUID): Boolean {
        val now = LocalDate.now()
        val ruleType = rule.ruleType.toSaleRuleType()
        
        return when (ruleType) {
            SaleRuleType.MONTHLY_RANGE -> {
                val currentMonth = now.monthValue
                val startMonth = rule.startMonth ?: 1
                val endMonth = rule.endMonth ?: 12
                
                if (startMonth <= endMonth) {
                    currentMonth in startMonth..endMonth
                } else {
                    // Для диапазонов, переходящих через год (например, ноябрь-февраль)
                    currentMonth >= startMonth || currentMonth <= endMonth
                }
            }
            
            SaleRuleType.WEEKLY_RANGE -> {
                val currentWeek = now.get(WeekFields.ISO.weekOfYear())
                val startWeek = rule.startWeek ?: 1
                val endWeek = rule.endWeek ?: 52
                
                if (startWeek <= endWeek) {
                    currentWeek in startWeek..endWeek
                } else {
                    // Для диапазонов, переходящих через год
                    currentWeek >= startWeek || currentWeek <= endWeek
                }
            }
            
            SaleRuleType.DAILY_RANGE -> {
                val currentDay = now.dayOfYear
                val startDay = rule.startDay ?: 1
                val endDay = rule.endDay ?: 365
                
                if (startDay <= endDay) {
                    currentDay in startDay..endDay
                } else {
                    // Для диапазонов, переходящих через год
                    currentDay >= startDay || currentDay <= endDay
                }
            }
            
            SaleRuleType.BALANCE_RANGE -> {
                // TODO: Здесь должна быть логика проверки баланса карты
                // Пока возвращаем true, так как баланс проверяется в другом месте
                true
            }
        }
    }
    
    /**
     * Конвертирует сущность Service в модель SbolService
     */
    private fun convertToSbolService(service: ru.sbertroika.abt.sbol.model.entities.Service): SbolService {
        return SbolService(
            serviceId = service.id.toString(),
            cost = BigDecimal(service.cost ?: 0),
            actionRange = ActionRange(
                startDate = service.actionStartDate?.atStartOfDay()?.atOffset(ZoneOffset.UTC) 
                    ?: OffsetDateTime.now(),
                endDate = service.actionEndDate?.atStartOfDay()?.atOffset(ZoneOffset.UTC)
                    ?: OffsetDateTime.now().plusMonths(1)
            ),
            description = ServiceDescription(
                textNote = service.description ?: service.name,
                intervalAmount = when (service.subscriptionType.toSubscriptionType()) {
                    SubscriptionType.WALLET -> 1
                    SubscriptionType.TRAVEL -> 1
                    SubscriptionType.UNLIMITED -> 1
                },
                intervalLength = when (service.subscriptionType.toSubscriptionType()) {
                    SubscriptionType.WALLET -> IntervalLength.D
                    SubscriptionType.TRAVEL -> IntervalLength.M
                    SubscriptionType.UNLIMITED -> IntervalLength.M
                }
            )
        )
    }
} 