package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.abt.sbol.model.entities.PurchaseItem
import java.util.UUID

@Repository
interface PurchaseItemRepository : ReactiveCrudRepository<PurchaseItem, UUID> {
    
    @Query("SELECT * FROM purchase_item WHERE order_card_id = :orderCardId")
    fun findByOrderCardId(orderCardId: UUID): Flux<PurchaseItem>
    
    @Query("SELECT * FROM purchase_item WHERE service_id = :serviceId")
    fun findByServiceId(serviceId: UUID): Flux<PurchaseItem>
} 