package ru.sbertroika.abt.sbol.config

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.server.ResponseStatusException
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono
import java.util.*


@Component
class AuthorizationRESTFilter(
    @Value("\${auth_log.enabled}")
    val authLog: Boolean
): WebFilter {

    private val log = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val CLIENT_ID = "1a98e85a-8077-40ce-95c8-b89d9957cfc0"
        const val CLIENT_HEADER = "x-client-ssl-cn"
        const val CLIENT_CERT = "x-forwarded-client-cert"
        const val HEADER_AGENT_ID = "x-agent-id"

        fun getCNFromHeader(header: String): String {
            val i = header.indexOf("CN=")
            var res = ""
            if (i > -1) {
                res = header.substring(i + 3, i + 39)
                try {
                    UUID.fromString(res)
                } catch (e: Exception) {
                    res = ""
                }
            }
            return res
        }
    }

    override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {
        log.info("filter called: host ${exchange.request.headers["Host"]?.get(0)}, path ${exchange.request.uri.path}")
        // исключение для swagger
        if (exchange.request.headers["Host"]?.get(0) == "agent-api-doc.sbertroika.tech")
            return chain.filter(exchange)
        // исключение для колбеков после оплаты заказа
        if (exchange.request.uri.path.startsWith("/payment/"))
            return chain.filter(exchange)
        // заголовок с agentId
        val agentHeader = exchange.request.headers[HEADER_AGENT_ID]
        if (!agentHeader.isNullOrEmpty())
            return chain.filter(exchange)
        // заголовок с инфо сертификата
        val clientCert = exchange.request.headers[CLIENT_CERT]
        if (clientCert.isNullOrEmpty()) {
            if (authLog) log.warn("Client cert is missing")
            throw ResponseStatusException(HttpStatus.FORBIDDEN)
        }
        // parse this: Hash=8bdc5757bce4f7b3a1b50601b4e08008c7fd4580e10f5c9c981a7cbc77ee3f0b;Subject="emailAddress=<EMAIL>,CN=1a98e85a-8077-40ce-95c8-b89d9957cfc0,OU=AgentApi,O=SberTroika,L=Moscow,ST=Moscow,C=RU";URI=
        val clientUUID = getCNFromHeader(clientCert[0])
        // TODO search in DB
        if (clientUUID.isEmpty()) {
            if (authLog) log.warn("Client CN is missing or can't be parsed : clientId = $clientUUID, client info: ${clientCert[0]}")
            throw ResponseStatusException(HttpStatus.FORBIDDEN, "ClientId is not recognized")
        }
        return chain.filter(exchange)
    }
}