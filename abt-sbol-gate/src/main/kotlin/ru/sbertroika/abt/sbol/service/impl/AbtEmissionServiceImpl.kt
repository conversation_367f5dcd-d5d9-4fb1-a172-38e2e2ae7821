package ru.sbertroika.abt.sbol.service.impl

import arrow.core.Either
import arrow.core.right
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.emission.v1.ABTEmissionServiceGrpcKt
import ru.sbertroika.abt.emission.v1.EmissionRequest
import ru.sbertroika.abt.sbol.service.AbtEmissionService
import java.util.*

@Service
class AbtEmissionServiceImpl(
    @Value("\${service.abt_emission_url}")
    private val abtEmissionServiceUrl: String
) : AbtEmissionService {

    private val channel = ManagedChannelBuilder.forTarget(abtEmissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)

    override suspend fun emission(cardNum: String, projectId: UUID): Either<Throwable, UUID> = Either.catch {
        val request = EmissionRequest.newBuilder()
            .setNumber(cardNum)
            .setProjectId(projectId.toString())
            .build()
        val response = client.emission(request)
        if (response.hasError()) throw RuntimeException("Не удалось подключиться к модулю эмиссии ABT")

        return UUID.fromString(response.cardId).right()
    }
}