package ru.sbertroika.abt.sbol.service.impl

import arrow.core.Either
import arrow.core.right
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.gateway.v1.AbtGatewayServiceGrpcKt
import ru.sbertroika.abt.gateway.v1.CreateSubscriptionRequest
import ru.sbertroika.abt.gateway.v1.SubscriptionListRequest
import ru.sbertroika.abt.gateway.v1.SubscriptionListResponse
import ru.sbertroika.abt.gateway.v1.WalletRefillRequest
import ru.sbertroika.abt.gateway.v1.WalletRefillResponse
import ru.sbertroika.abt.sbol.service.AbtGateService

@Service
class AbtGateServiceImpl(
    @Value("\${service.abt_gate_url}")
    private val abtGateServiceUrl: String
) : AbtGateService {

    private val channel = ManagedChannelBuilder.forTarget(abtGateServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)

    override suspend fun getSubscriptionList(request: SubscriptionListRequest): Either<Throwable, SubscriptionListResponse> = Either.catch {
        val response = client.subscriptionList(request)
        if (response.hasError()) throw RuntimeException("Ошибка получения списка абонементов из abt-gate")
        response
    }
    
    override suspend fun createSubscription(request: CreateSubscriptionRequest): Either<Throwable, String> = Either.catch {
        val response = client.createSubscription(request)
        response.info.subscriptionId
    }
    
    override suspend fun walletRefill(request: WalletRefillRequest): Either<Throwable, WalletRefillResponse> = Either.catch {
        client.walletRefill(request)
    }
} 