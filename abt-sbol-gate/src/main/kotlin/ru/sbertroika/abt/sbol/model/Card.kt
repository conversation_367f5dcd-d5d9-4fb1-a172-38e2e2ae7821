package ru.sbertroika.abt.sbol.model

import java.math.BigDecimal
import java.time.OffsetDateTime
import ru.sbertroika.abt.sbol.model.enums.PaymentSystem
import ru.sbertroika.abt.sbol.model.enums.IntervalLength

/**
 * Модель карты
 */
data class Card(
    val pan: String,
    val panHash: String? = null,
    val paymentSystem: PaymentSystem? = null,
    val accountReference: String? = null,
)

/**
 * Модель счета карты
 */
data class CardAccount(
    val balanceAmount: BigDecimal,
)

/**
 * Модель пополнения счетчика
 */
data class CounterReplenishment(
    val allowed: Boolean,
    val restriction: Restriction? = null,
)

/**
 * Модель ограничений
 */
data class Restriction(
    val value: Value? = null,
    val upTo: UpTo? = null,
)

/**
 * Модель значения
 */
data class Value(
    val minAmount: BigDecimal,
    val maxAmount: BigDecimal,
    val recommendedAmount: BigDecimal? = null,
)

/**
 * Модель "до"
 */
data class UpTo(
    val minAmount: BigDecimal,
    val maxAmount: BigDecimal,
)

/**
 * Модель покупки услуг
 */
data class ServicePurchase(
    val allowed: Boolean,
    val availableCounterAmount: BigDecimal? = null,
    val services: List<Service> = emptyList(),
)

/**
 * Модель услуги
 */
data class Service(
    val serviceId: String,
    val cost: BigDecimal,
    val actionRange: ActionRange? = null,
    val description: ServiceDescription? = null,
)

/**
 * Модель описания услуги
 */
data class ServiceDescription(
    val textNote: String? = null,
    val intervalAmount: Int? = null,
    val intervalLength: IntervalLength? = null,
)

/**
 * Модель диапазона действия
 */
data class ActionRange(
    val startDate: OffsetDateTime,
    val endDate: OffsetDateTime,
) 