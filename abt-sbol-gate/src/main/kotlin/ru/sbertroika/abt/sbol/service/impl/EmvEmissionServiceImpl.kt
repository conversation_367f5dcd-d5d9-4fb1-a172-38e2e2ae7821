package ru.sbertroika.abt.sbol.service.impl

import arrow.core.Either
import arrow.core.right
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.service.EmvEmissionService
import ru.sbertroika.emv.emission.v1.EMVEmissionServiceGrpcKt
import ru.sbertroika.emv.emission.v1.EmissionRequest
import java.util.*

@Service
class EmvEmissionServiceImpl(
    @Value("\${service.emv_emission_url}")
    private val emissionServiceUrl: String
) : EmvEmissionService {

    private val channel = ManagedChannelBuilder.forTarget(emissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = EMVEmissionServiceGrpcKt.EMVEmissionServiceCoroutineStub(channel)

    override suspend fun emission(pan: String): Either<Throwable, UUID> = Either.catch {
        val request = EmissionRequest.newBuilder()
            .setPan(pan)
            .build()
        val response = client.emission(request)
        if (response.hasError()) throw RuntimeException("Не удалось подключиться к модулю эмиссии EMV")

        return UUID.fromString(response.cardId).right()
    }
}