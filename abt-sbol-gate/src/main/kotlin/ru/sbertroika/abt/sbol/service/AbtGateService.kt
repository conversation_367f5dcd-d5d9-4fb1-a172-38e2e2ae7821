package ru.sbertroika.abt.sbol.service

import arrow.core.Either
import ru.sbertroika.abt.gateway.v1.CreateSubscriptionRequest
import ru.sbertroika.abt.gateway.v1.SubscriptionListRequest
import ru.sbertroika.abt.gateway.v1.SubscriptionListResponse
import ru.sbertroika.abt.gateway.v1.WalletRefillRequest
import ru.sbertroika.abt.gateway.v1.WalletRefillResponse

interface AbtGateService {
    suspend fun getSubscriptionList(request: SubscriptionListRequest): Either<Throwable, SubscriptionListResponse>
    
    suspend fun createSubscription(request: CreateSubscriptionRequest): Either<Throwable, String>
    
    suspend fun walletRefill(request: WalletRefillRequest): Either<Throwable, WalletRefillResponse>
} 