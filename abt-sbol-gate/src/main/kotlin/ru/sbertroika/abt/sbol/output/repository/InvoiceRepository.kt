package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.sbol.model.entities.Invoice
import java.util.UUID

@Repository
interface InvoiceRepository : ReactiveCrudRepository<Invoice, UUID> {
    
    @Query("SELECT * FROM invoice WHERE agent_transaction_id = :agentTransactionId")
    fun findByAgentTransactionId(agentTransactionId: String): Mono<Invoice>
    
    @Query("SELECT * FROM invoice WHERE invoice_status = :status")
    fun findByStatus(status: String): Flux<Invoice>
    
    @Query("SELECT * FROM invoice WHERE region_id = :regionId")
    fun findByRegionId(regionId: String): Flux<Invoice>
} 