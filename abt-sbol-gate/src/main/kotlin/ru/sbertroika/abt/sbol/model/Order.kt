package ru.sbertroika.abt.sbol.model

import java.math.BigDecimal
import java.util.UUID
import ru.sbertroika.abt.sbol.model.enums.InvoiceStatus
import ru.sbertroika.abt.sbol.model.enums.PaymentSystem

/**
 * Модель заказа
 */
data class Order(
    val orderId: UUID? = null,
    val orderStatus: OrderStatus? = null,
    val orderCards: List<OrderCard> = emptyList(),
)

/**
 * Статус заказа
 */
enum class OrderStatus {
    CREATED,
    PAID,
    CANCELED,
    OUTDATED,
}

/**
 * Модель карты в заказе
 */
data class OrderCard(
    val ipsCard: IpsCard? = null,
    val transportCard: TransportCard? = null,
    val purchaseItems: List<PurchaseItem> = emptyList(),
    val replenishment: Replenishment? = null,
)

/**
 * Модель банковской карты
 */
data class IpsCard(
    val panHash: String,
    val paymentSystem: PaymentSystem,
)

/**
 * Модель транспортной карты
 */
data class TransportCard(
    val pan: String,
)

/**
 * Модель пополнения
 */
data class Replenishment(
    val amount: BigDecimal,
    val type: String,
)

/**
 * Модель элемента покупки
 */
data class PurchaseItem(
    val serviceId: UUID,
    val usedCounterAmount: BigDecimal,
)

/**
 * Модель счета
 */
data class Invoice(
    val invoiceId: UUID,
    val invoiceStatus: InvoiceStatus,
    val invoiceAmount: BigDecimal,
    val agentTransactionId: String,
    val order: Order,
)



/**
 * Модель ответа с доступными операциями
 */
data class AvailableOperationsResponse(
    val card: Card? = null,
    val cardAccount: CardAccount? = null,
    val counterReplenishment: CounterReplenishment? = null,
    val servicePurchase: ServicePurchase? = null,
    val errorMsg: String? = null,
) 