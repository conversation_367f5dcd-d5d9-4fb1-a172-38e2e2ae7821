package ru.sbertroika.abt.sbol.service.converter

import ru.sbertroika.abt.sbol.model.enums.CardType
import ru.sbertroika.abt.sbol.model.enums.InvoiceStatus
import ru.sbertroika.abt.sbol.model.enums.PaymentSystem
import ru.sbertroika.abt.sbol.model.enums.SaleRuleLogic
import ru.sbertroika.abt.sbol.model.enums.SaleRuleType
import ru.sbertroika.abt.sbol.model.enums.SubscriptionType
import ru.sbertroika.abt.sbol.model.enums.IntervalLength

/**
 * Конвертеры для преобразования между enum и String
 */
object EnumConverter {
    
    fun CardType.toDatabaseValue(): String = this.name
    
    fun String.toCardType(): CardType = CardType.valueOf(this)
    
    fun PaymentSystem.toDatabaseValue(): String = this.name
    
    fun String.toPaymentSystem(): PaymentSystem = PaymentSystem.valueOf(this)
    
    fun InvoiceStatus.toDatabaseValue(): String = this.name
    
    fun String.toInvoiceStatus(): InvoiceStatus = InvoiceStatus.valueOf(this)
    
    fun SaleRuleType.toDatabaseValue(): String = this.name
    
    fun String.toSaleRuleType(): SaleRuleType = SaleRuleType.valueOf(this)
    
    fun SaleRuleLogic.toDatabaseValue(): String = this.name
    
    fun String.toSaleRuleLogic(): SaleRuleLogic = SaleRuleLogic.valueOf(this)
    
    fun SubscriptionType.toDatabaseValue(): String = this.name
    
    fun String.toSubscriptionType(): SubscriptionType = SubscriptionType.valueOf(this)
    
    fun IntervalLength.toDatabaseValue(): String = this.name
    
    fun String.toIntervalLength(): IntervalLength = IntervalLength.valueOf(this)
} 