package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.abt.sbol.model.entities.Service
import java.util.UUID

@Repository
interface ServiceRepository : ReactiveCrudRepository<Service, UUID> {
    
    @Query("SELECT * FROM service WHERE project_id = :projectId")
    fun findByProjectId(projectId: UUID): Flux<Service>
    
    @Query("SELECT * FROM service WHERE service_code = :serviceCode")
    fun findByServiceCode(serviceCode: String): Flux<Service>
    
    @Query("SELECT * FROM service WHERE subscription_type = :subscriptionType")
    fun findBySubscriptionType(subscriptionType: String): Flux<Service>
    
    @Query("SELECT DISTINCT s.* FROM service s " +
           "INNER JOIN sale_rule sr ON s.id = sr.service_id " +
           "WHERE s.project_id = :projectId AND sr.is_active = true")
    fun findServicesWithActiveRulesByProjectId(projectId: UUID): Flux<Service>
} 