package ru.sbertroika.abt.sbol.service.impl

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import mu.KotlinLogging
import org.springframework.stereotype.Service
import ru.sbertroika.abt.sbol.model.AvailableOperationsResponse
import ru.sbertroika.abt.sbol.model.Card
import ru.sbertroika.abt.sbol.model.CardAccount
import ru.sbertroika.abt.sbol.model.CounterReplenishment
import ru.sbertroika.abt.sbol.model.Invoice
import ru.sbertroika.abt.sbol.model.Order
import ru.sbertroika.abt.sbol.model.OrderStatus
import ru.sbertroika.abt.sbol.model.Restriction
import ru.sbertroika.abt.sbol.model.ServicePurchase
import ru.sbertroika.abt.sbol.model.UpTo
import ru.sbertroika.abt.sbol.model.Value
import ru.sbertroika.abt.sbol.service.SbolService
import ru.sbertroika.abt.sbol.model.entities.Invoice as InvoiceEntity
import ru.sbertroika.abt.sbol.model.entities.Order as OrderEntity
import ru.sbertroika.abt.sbol.model.entities.OrderCard as OrderCardEntity
import ru.sbertroika.abt.sbol.model.entities.ReplenishmentItem as ReplenishmentItemEntity
import ru.sbertroika.abt.sbol.model.entities.PurchaseItem as PurchaseItemEntity
import ru.sbertroika.abt.sbol.service.converter.EnumConverter.toDatabaseValue
import ru.sbertroika.abt.sbol.model.enums.CardType
import ru.sbertroika.abt.sbol.output.repository.InvoiceRepository
import ru.sbertroika.abt.sbol.output.repository.OrderCardRepository
import ru.sbertroika.abt.sbol.output.repository.OrderRepository
import ru.sbertroika.abt.sbol.output.repository.PurchaseItemRepository
import ru.sbertroika.abt.sbol.output.repository.ReplenishmentItemRepository
import ru.sbertroika.abt.sbol.output.repository.ServiceRepository
import ru.sbertroika.abt.sbol.service.AbtEmissionService
import ru.sbertroika.abt.sbol.service.AbtGateService
import ru.sbertroika.abt.sbol.service.EmvEmissionService
import ru.sbertroika.abt.sbol.service.SaleRuleService
import ru.sbertroika.abt.sbol.model.enums.InvoiceStatus as InvoiceStatusEnum
import ru.sbertroika.abt.sbol.model.enums.PaymentSystem as PaymentSystemEnum

import java.math.BigDecimal
import java.sql.Timestamp
import java.util.UUID

@Service
class SbolServiceImpl(
    private val abtEmissionService: AbtEmissionService,
    private val emvEmissionService: EmvEmissionService,
    private val abtGateService: AbtGateService,
    private val saleRuleService: SaleRuleService,
    private val invoiceRepository: InvoiceRepository,
    private val orderRepository: OrderRepository,
    private val orderCardRepository: OrderCardRepository,
    private val replenishmentItemRepository: ReplenishmentItemRepository,
    private val purchaseItemRepository: PurchaseItemRepository,
    private val serviceRepository: ServiceRepository
) : SbolService {

    private val logger = KotlinLogging.logger {}

    override suspend fun getAvailableOperationsForTransportCard(
        regionId: String,
        pan: String,
        agentId: String,
    ): AvailableOperationsResponse = withContext(Dispatchers.IO) {
        logger.info { "Получение доступных операций для транспортной карты: pan=$pan, regionId=$regionId" }
        
        try {
            // 1. Получаем cardId по номеру карты через AbtEmissionService
            val projectId = UUID.fromString(regionId) // Предполагаем, что regionId это projectId
            val cardId = abtEmissionService.emission(pan, projectId).fold(
                { throwable ->
                    logger.error { "Ошибка получения cardId для карты $pan: ${throwable.message}" }
                    return@withContext AvailableOperationsResponse(errorMsg = "Карта не найдена в системе")
                },
                { it }
            )
            
            // 2. Получаем список активных абонементов на карте через AbtGateService
            val subscriptionListRequest = ru.sbertroika.abt.gateway.v1.subscriptionListRequest {
                filter = ru.sbertroika.abt.gateway.v1.subscriptionListFilter {
                    this.cardId = cardId.toString()
                    this.projectId = regionId
                    isActive = true
                    isActual = true
                }
                limit = 100
            }
            
            val subscriptionListResponse = abtGateService.getSubscriptionList(subscriptionListRequest).fold(
                { throwable ->
                    logger.error { "Ошибка получения списка абонементов для карты $cardId: ${throwable.message}" }
                    return@withContext AvailableOperationsResponse(errorMsg = "Ошибка получения информации об абонементах")
                },
                { it }
            )
            
            // 3. Получаем доступные услуги на основе правил продажи и прав агента
            val availableServices = saleRuleService.getAvailableServices(cardId, projectId, agentId)
            
            // 4. Рассчитываем баланс на основе активных абонементов
            val balanceAmount = calculateBalanceFromSubscriptions(subscriptionListResponse)

            return@withContext AvailableOperationsResponse(
                card = Card(
                    pan = pan,
                    accountReference = "ACC${pan.takeLast(9)}"
                ),
                cardAccount = CardAccount(
                    balanceAmount = balanceAmount
                ),
                counterReplenishment = CounterReplenishment(
                    allowed = true,
                    restriction = Restriction(
                        value = Value(
                            minAmount = BigDecimal("1000"),
                            maxAmount = BigDecimal("50000"),
                            recommendedAmount = BigDecimal("10000")
                        ),
                        upTo = UpTo(
                            minAmount = BigDecimal.ZERO,
                            maxAmount = BigDecimal("100000")
                        )
                    )
                ),
                servicePurchase = ServicePurchase(
                    allowed = true,
                    availableCounterAmount = balanceAmount,
                    services = availableServices
                )
            )
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении доступных операций для транспортной карты: pan=$pan" }
            return@withContext AvailableOperationsResponse(errorMsg = "Внутренняя ошибка сервера")
        }
    }

    override suspend fun getAvailableOperationsForBankCard(
        regionId: String,
        ipsName: String,
        panHash: String,
        agentId: String,
    ): AvailableOperationsResponse = withContext(Dispatchers.IO) {
        logger.info { "Получение доступных операций для банковской карты: panHash=$panHash, ipsName=$ipsName, regionId=$regionId" }
        
        try {
            // 1. Получаем cardId по хэшу PAN через EmvEmissionService
            val projectId = UUID.fromString(regionId) // Предполагаем, что regionId это projectId
            val cardId = emvEmissionService.emission(panHash).fold(
                { throwable ->
                    logger.error { "Ошибка получения cardId для банковской карты $panHash: ${throwable.message}" }
                    return@withContext AvailableOperationsResponse(errorMsg = "Банковская карта не найдена в системе")
                },
                { it }
            )
            
            // 2. Получаем список активных абонементов на карте через AbtGateService
            val subscriptionListRequest = ru.sbertroika.abt.gateway.v1.subscriptionListRequest {
                filter = ru.sbertroika.abt.gateway.v1.subscriptionListFilter {
                    this.cardId = cardId.toString()
                    this.projectId = regionId
                    isActive = true
                    isActual = true
                }
                limit = 100
            }
            
            val subscriptionListResponse = abtGateService.getSubscriptionList(subscriptionListRequest).fold(
                { throwable ->
                    logger.error { "Ошибка получения списка абонементов для банковской карты $cardId: ${throwable.message}" }
                    return@withContext AvailableOperationsResponse(errorMsg = "Ошибка получения информации об абонементах")
                },
                { it }
            )
            
            // 3. Получаем доступные услуги на основе правил продажи и прав агента
            val availableServices = saleRuleService.getAvailableServices(cardId, projectId, agentId)
            
            // 4. Рассчитываем баланс на основе активных абонементов
            val balanceAmount = calculateBalanceFromSubscriptions(subscriptionListResponse)

            return@withContext AvailableOperationsResponse(
                card = Card(
                    pan = "", // Для банковских карт PAN не используется
                    panHash = panHash,
                    paymentSystem = PaymentSystemEnum.valueOf(ipsName),
                    accountReference = "ACC${panHash.takeLast(9)}"
                ),
                cardAccount = CardAccount(
                    balanceAmount = balanceAmount
                ),
                counterReplenishment = CounterReplenishment(
                    allowed = true,
                    restriction = Restriction(
                        value = Value(
                            minAmount = BigDecimal("1000"),
                            maxAmount = BigDecimal("100000"),
                            recommendedAmount = BigDecimal("20000")
                        )
                    )
                ),
                servicePurchase = ServicePurchase(
                    allowed = true,
                    availableCounterAmount = balanceAmount,
                    services = availableServices
                )
            )
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при получении доступных операций для банковской карты: panHash=$panHash" }
            return@withContext AvailableOperationsResponse(errorMsg = "Внутренняя ошибка сервера")
        }
    }

    override suspend fun createOrder(
        regionId: String,
        agentTransactionId: String,
        order: Order,
        agentId: String,
    ): Invoice = withContext(Dispatchers.IO) {
        logger.info { "Создание заказа: agentTransactionId=$agentTransactionId, regionId=$regionId" }
        
        try {
            // Проверяем, не существует ли уже счет с таким agentTransactionId
            val existingInvoice = invoiceRepository.findByAgentTransactionId(agentTransactionId).block()
            if (existingInvoice != null) {
                logger.warn { "Счет с agentTransactionId=$agentTransactionId уже существует" }
                throw IllegalArgumentException("Счет с таким идентификатором транзакции уже существует")
            }
            
            // Создаем счет
            val invoiceAmount = calculateInvoiceAmount(order)
            
            // Сохраняем счет в базу данных
            val invoiceEntity = InvoiceEntity(
                invoiceId = null, // Устанавливаем null, чтобы R2DBC выполнил INSERT
                agentTransactionId = agentTransactionId,
                invoiceStatus = InvoiceStatusEnum.CREATED.toDatabaseValue(),
                invoiceAmount = invoiceAmount.toLong(),
                regionId = regionId,
                createdAt = Timestamp(System.currentTimeMillis()),
                updatedAt = Timestamp(System.currentTimeMillis())
            )
            
            val savedInvoice = invoiceRepository.save(invoiceEntity).block()
                ?: throw RuntimeException("Ошибка сохранения счета")
            
            // Получаем ID из сохраненной записи
            val savedInvoiceId = savedInvoice.invoiceId
                ?: throw RuntimeException("ID счета не был сгенерирован")
            
            // Сохраняем заказ
            val orderEntity = OrderEntity(
                invoiceId = savedInvoiceId,
                orderStatus = "CREATED"
            )
            
            // Используем R2dbcEntityTemplate.insert() для явного выполнения INSERT операции
            val savedOrder = orderRepository.save(orderEntity).block()
                ?: throw RuntimeException("Ошибка сохранения заказа")

            // Получаем сгенерированный orderId
            val generatedOrderId = savedOrder.orderId
                ?: throw RuntimeException("ID заказа не был сгенерирован")

            // Сохраняем карты заказа
            for (orderCard in order.orderCards) {
                val orderCardEntity = createOrderCardEntity(generatedOrderId, orderCard)
                val savedOrderCardEntity = orderCardRepository.save(orderCardEntity).block()
                    ?: throw RuntimeException("Ошибка сохранения карты заказа")
                
                // Сохраняем элементы пополнения
                if (orderCard.replenishment != null) {
                    val replenishmentItem = ReplenishmentItemEntity(
                        orderCardId = savedOrderCardEntity.id!!,
                        itemType = orderCard.replenishment.type,
                        description = "Пополнение счета",
                        replenishmentAmount = orderCard.replenishment.amount.toLong(),
                        createdAt = Timestamp(System.currentTimeMillis())
                    )
                    replenishmentItemRepository.save(replenishmentItem).block()
                        ?: throw RuntimeException("Ошибка сохранения элемента пополнения")
                }
                
                // Сохраняем элементы покупки
                for (purchaseItem in orderCard.purchaseItems) {
                    val purchaseItemEntity = PurchaseItemEntity(
                        orderCardId = savedOrderCardEntity.id!!,
                        serviceId = purchaseItem.serviceId,
                        serviceIdString = purchaseItem.serviceId.toString(),
                        replenishmentAmount = null,
                        usedCounterAmount = purchaseItem.usedCounterAmount.toLong(),
                        cost = 0L, // Будет заполнено из информации об услуге
                        descriptionText = null,
                        intervalAmount = null,
                        intervalLength = null,
                        actionStartDate = null,
                        actionEndDate = null,
                        createdAt = Timestamp(System.currentTimeMillis())
                    )
                    purchaseItemRepository.save(purchaseItemEntity).block()
                        ?: throw RuntimeException("Ошибка сохранения элемента покупки")
                }
            }
            
            // Возвращаем результат
            return@withContext Invoice(
                invoiceId = savedInvoiceId,
                invoiceStatus = InvoiceStatusEnum.CREATED,
                invoiceAmount = invoiceAmount,
                agentTransactionId = agentTransactionId,
                order = order.copy(
                    orderId = generatedOrderId,
                    orderStatus = OrderStatus.CREATED
                )
            )
            
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при создании заказа: agentTransactionId=$agentTransactionId" }
            throw e
        }
    }

    override suspend fun updateInvoiceStatus(
        regionId: String,
        invoiceId: String,
        agentTransactionId: String,
        invoiceStatus: ru.sbertroika.abt.sbol.model.enums.InvoiceStatus,
    ): Boolean = withContext(Dispatchers.IO) {
        logger.info { "Обновление статуса счета: invoiceId=$invoiceId, status=$invoiceStatus, regionId=$regionId" }

        try {
            // Находим существующий счет по ID
            val existingInvoice = invoiceRepository.findById(UUID.fromString(invoiceId)).block()

            if (existingInvoice == null) {
                logger.warn { "Счет с ID $invoiceId не найден" }
                return@withContext false
            }

            if (existingInvoice.invoiceStatus == ru.sbertroika.abt.sbol.model.enums.InvoiceStatus.PAID.name) return@withContext true

            // Если статус не PAID, просто обновляем статус
            if (invoiceStatus != ru.sbertroika.abt.sbol.model.enums.InvoiceStatus.PAID) {
                updateInvoiceAndOrderStatus(existingInvoice, invoiceStatus, ru.sbertroika.abt.sbol.model.OrderStatus.CANCELED)
                return@withContext true
            }

            // Для статуса PAID нужно обработать услуги
            val order = orderRepository.findByInvoiceId(existingInvoice.invoiceId!!).block()
            if (order == null) {
                logger.warn { "Заказ для счета $invoiceId не найден" }
                updateInvoiceAndOrderStatus(existingInvoice, ru.sbertroika.abt.sbol.model.enums.InvoiceStatus.CANCELED, ru.sbertroika.abt.sbol.model.OrderStatus.CANCELED)
                return@withContext false
            }

            // Получаем карты заказа
            val orderCards = orderCardRepository.findByOrderId(order.orderId!!).collectList().block() ?: emptyList()
            
            var hasErrors = false
            
            // Обрабатываем каждую карту заказа
            for (orderCard in orderCards) {
                // Обрабатываем пополнения кошельков
                val replenishmentItems = replenishmentItemRepository.findByOrderCardId(orderCard.id!!).collectList().block() ?: emptyList()
                for (replenishmentItem in replenishmentItems) {
                    val success = processWalletReplenishment(orderCard, replenishmentItem, regionId)
                    if (!success) {
                        hasErrors = true
                        logger.error { "Ошибка пополнения кошелька для карты ${orderCard.id}" }
                    }
                }
                
                // Обрабатываем покупки услуг
                val purchaseItems = purchaseItemRepository.findByOrderCardId(orderCard.id!!).collectList().block() ?: emptyList()
                for (purchaseItem in purchaseItems) {
                    val success = processServicePurchase(orderCard, purchaseItem, regionId)
                    if (!success) {
                        hasErrors = true
                        logger.error { "Ошибка создания абонемента для услуги ${purchaseItem.serviceId}" }
                    }
                }
            }
            
            // Обновляем статусы в зависимости от результата
            if (hasErrors) {
                updateInvoiceAndOrderStatus(existingInvoice, ru.sbertroika.abt.sbol.model.enums.InvoiceStatus.CANCELED, ru.sbertroika.abt.sbol.model.OrderStatus.CANCELED)
                logger.warn { "Счет $invoiceId отменен из-за ошибок обработки услуг" }
                return@withContext false
            } else {
                updateInvoiceAndOrderStatus(existingInvoice, invoiceStatus, ru.sbertroika.abt.sbol.model.OrderStatus.PAID)
                logger.info { "Статус счета $invoiceId успешно обновлен на $invoiceStatus" }
                return@withContext true
            }
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при обновлении статуса счета: invoiceId=$invoiceId" }
            return@withContext false
        }
    }
    
    /**
     * Рассчитывает баланс на основе активных абонементов
     */
    private fun calculateBalanceFromSubscriptions(subscriptionListResponse: ru.sbertroika.abt.gateway.v1.SubscriptionListResponse): BigDecimal {
        return if (subscriptionListResponse.hasList()) {
            val subscriptions = subscriptionListResponse.list.infoList
            if (subscriptions.isNotEmpty()) {
                // В реальной реализации здесь была бы логика расчета баланса
                // на основе активных абонементов и их остатков
                BigDecimal("15000") // Временная заглушка
            } else {
                BigDecimal.ZERO
            }
        } else {
            BigDecimal.ZERO
        }
    }
    
    /**
     * Рассчитывает общую сумму счета
     */
    private fun calculateInvoiceAmount(order: Order): BigDecimal {
        var totalAmount = BigDecimal.ZERO
        
        for (orderCard in order.orderCards) {
            // Добавляем сумму пополнения
            if (orderCard.replenishment != null) {
                totalAmount = totalAmount.add(orderCard.replenishment.amount)
            }
            
            // Добавляем стоимость покупок услуг
            for (purchaseItem in orderCard.purchaseItems) {
                // В реальной реализации здесь нужно получить стоимость услуги из базы данных
                // Пока используем usedCounterAmount как стоимость
                totalAmount = totalAmount.add(purchaseItem.usedCounterAmount)
            }
        }
        
        return totalAmount
    }
    
    /**
     * Создает сущность карты заказа
     */
    private fun createOrderCardEntity(orderId: UUID, orderCard: ru.sbertroika.abt.sbol.model.OrderCard): OrderCardEntity {
        return when {
            orderCard.transportCard != null -> {
                OrderCardEntity(
                    orderId = orderId,
                    cardType = CardType.TRANSPORT.toDatabaseValue(),
                    pan = orderCard.transportCard.pan,
                    panHash = null,
                    paymentSystem = null,
                    cardId = null,
                    createdAt = Timestamp(System.currentTimeMillis())
                )
            }
            orderCard.ipsCard != null -> {
                OrderCardEntity(
                    orderId = orderId,
                    cardType = CardType.IPS.toDatabaseValue(),
                    pan = null,
                    panHash = orderCard.ipsCard.panHash,
                    paymentSystem = orderCard.ipsCard.paymentSystem.toDatabaseValue(),
                    cardId = null,
                    createdAt = Timestamp(System.currentTimeMillis())
                )
            }
            else -> {
                throw IllegalArgumentException("Карта должна быть либо транспортной, либо банковской")
            }
        }
    }
    
    /**
     * Обрабатывает пополнение кошелька
     */
    private suspend fun processWalletReplenishment(
        orderCard: ru.sbertroika.abt.sbol.model.entities.OrderCard,
        replenishmentItem: ru.sbertroika.abt.sbol.model.entities.ReplenishmentItem,
        regionId: String
    ): Boolean {
        return try {
            // Получаем cardId по PAN карты
            val pan = orderCard.pan
            if (pan == null) {
                logger.error { "PAN карты не найден для пополнения кошелька" }
                return false
            }
            
            val cardId = abtEmissionService.emission(pan, UUID.fromString(regionId)).fold(
                { throwable ->
                    logger.error { "Ошибка получения cardId для карты $pan: ${throwable.message}" }
                    return false
                },
                { it }
            )
            
            // Создаем запрос на пополнение кошелька
            val walletRefillRequest = ru.sbertroika.abt.gateway.v1.walletRefillRequest {
                this.cardId = cardId.toString()
                this.projectId = regionId
                this.value = replenishmentItem.replenishmentAmount.toInt()
            }
            
            // Отправляем запрос в abt-gate
            val result = abtGateService.walletRefill(walletRefillRequest)
            result.fold(
                { throwable ->
                    logger.error { "Ошибка пополнения кошелька: ${throwable.message}" }
                    false
                },
                { response ->
                    logger.info { "Кошелек успешно пополнен на ${replenishmentItem.replenishmentAmount}. Новый баланс: ${response.balance}" }
                    true
                }
            )
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при обработке пополнения кошелька" }
            false
        }
    }
    
    /**
     * Обрабатывает покупку услуги (создание абонемента)
     */
    private suspend fun processServicePurchase(
        orderCard: ru.sbertroika.abt.sbol.model.entities.OrderCard,
        purchaseItem: ru.sbertroika.abt.sbol.model.entities.PurchaseItem,
        regionId: String
    ): Boolean {
        return try {
            // Получаем информацию об услуге
            val service = serviceRepository.findById(purchaseItem.serviceId).block()
            if (service == null) {
                logger.error { "Услуга с ID ${purchaseItem.serviceId} не найдена" }
                return false
            }
            
            // Получаем cardId по PAN карты
            val pan = orderCard.pan
            if (pan == null) {
                logger.error { "PAN карты не найден для создания абонемента" }
                return false
            }
            
            val cardId = abtEmissionService.emission(pan, UUID.fromString(regionId)).fold(
                { throwable ->
                    logger.error { "Ошибка получения cardId для карты $pan: ${throwable.message}" }
                    return false
                },
                { it }
            )
            
            // Создаем запрос на создание абонемента
            val createSubscriptionRequest = ru.sbertroika.abt.gateway.v1.createSubscriptionRequest {
                this.templateId = service.templateId.toString()
                this.cardId = cardId.toString()
                this.projectId = regionId
                // По умолчанию используем ABT как источник эмиссии
                this.emissionSource = ru.sbertroika.abt.gateway.v1.SubscriptionEmissionSource.ABT
            }
            
            // Отправляем запрос в abt-gate
            val result = abtGateService.createSubscription(createSubscriptionRequest)
            result.fold(
                { throwable ->
                    logger.error { "Ошибка создания абонемента: ${throwable.message}" }
                    false
                },
                { subscriptionId ->
                    logger.info { "Абонемент успешно создан с ID: $subscriptionId" }
                    true
                }
            )
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при обработке покупки услуги" }
            false
        }
    }
    
    /**
     * Обновляет статусы счета и заказа
     */
    private suspend fun updateInvoiceAndOrderStatus(
        invoice: ru.sbertroika.abt.sbol.model.entities.Invoice,
        invoiceStatus: ru.sbertroika.abt.sbol.model.enums.InvoiceStatus,
        orderStatus: ru.sbertroika.abt.sbol.model.OrderStatus
    ) {
        try {
            // Обновляем статус счета
            val updatedInvoice = invoice.copy(
                invoiceStatus = invoiceStatus.toDatabaseValue(),
                updatedAt = Timestamp(System.currentTimeMillis())
            )
            invoiceRepository.save(updatedInvoice).block()
            
            // Обновляем статус заказа
            val order = orderRepository.findByInvoiceId(invoice.invoiceId!!).block()
            if (order != null) {
                val updatedOrder = order.copy(
                    orderStatus = orderStatus.name,
                    updatedAt = Timestamp(System.currentTimeMillis())
                )
                orderRepository.save(updatedOrder).block()
            }
            
            logger.info { "Статусы обновлены: счет=${invoiceStatus}, заказ=${orderStatus}" }
        } catch (e: Exception) {
            logger.error(e) { "Ошибка при обновлении статусов счета и заказа" }
        }
    }
} 