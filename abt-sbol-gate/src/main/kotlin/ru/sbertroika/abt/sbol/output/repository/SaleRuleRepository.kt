package ru.sbertroika.abt.sbol.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.abt.sbol.model.entities.SaleRule
import java.util.UUID

@Repository
interface SaleRuleRepository : ReactiveCrudRepository<SaleRule, UUID> {
    
    @Query("SELECT * FROM sale_rule WHERE service_id = :serviceId AND is_active = true")
    fun findByServiceIdAndActive(serviceId: UUID): Flux<SaleRule>
    
    @Query("SELECT * FROM sale_rule WHERE rule_type = :ruleType AND is_active = true")
    fun findByRuleTypeAndActive(ruleType: String): Flux<SaleRule>
    
    @Query("SELECT DISTINCT sr.* FROM sale_rule sr " +
           "INNER JOIN service s ON sr.service_id = s.id " +
           "WHERE s.project_id = :projectId AND sr.is_active = true")
    fun findActiveRulesByProjectId(projectId: UUID): Flux<SaleRule>
} 