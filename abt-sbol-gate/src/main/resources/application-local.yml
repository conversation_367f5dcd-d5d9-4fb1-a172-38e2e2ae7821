server:
  port: 8085

spring:
  application:
    name: abt-sbol-gate
  
  webflux:
    base-path: /
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5434/abt_sbol_gate}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
  
  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5434/abt_sbol_gate}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    ru.sbertroika.abt.sbol.gate: INFO
    org.springframework.web: INFO
    reactor.netty: WARN
    org.springframework.data.r2dbc: DEBUG
    org.flywaydb: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Конфигурация для SBOL
sbol:
  timeout:
    connect: 5000
    read: 30000
  retry:
    max-attempts: 3
    backoff:
      initial: 1000
      multiplier: 2.0
      max-delay: 10000

service:
  abt_emission_url: ${ABT_EMISSION_URL:localhost:5008}
  emv_emission_url: ${EMV_EMISSION_URL:localhost:5007}
  abt_gate_url: ${ABT_GATE_URL:localhost:5011}