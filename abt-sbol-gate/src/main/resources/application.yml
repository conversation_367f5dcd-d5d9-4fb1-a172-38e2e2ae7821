server:
  port: 8080

spring:
  application:
    name: abt-sbol-gate
  
  webflux:
    base-path: /
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres/abt_emission}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
  
  flyway:
    enabled: ${DB_MIGRATION_ENABLE:false}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://postgres/abt_emission}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

auth_log:
  enabled: ${AUTH_LOG_ENABLED:true}

# Конфигурация для SBOL
sbol:
  timeout:
    connect: 5000
    read: 30000
  retry:
    max-attempts: 3
    backoff:
      initial: 1000
      multiplier: 2.0
      max-delay: 10000

service:
  abt_emission_url: ${abt_EMISSION_URL:abt-emission.abt.svc.cluster.local:5000}
  emv_emission_url: ${abt_EMISSION_URL:emv-emission.emv.svc.cluster.local:5000}
  abt_gate_url: ${ABT_GATE_URL:abt-gate.abt.svc.cluster.local:5000}