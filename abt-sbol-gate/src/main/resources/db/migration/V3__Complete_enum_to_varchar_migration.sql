-- Завершение миграции enum типов на varchar(200)

-- Изменяем card_type в таблице order_card
ALTER TABLE order_card ALTER COLUMN card_type TYPE varchar(200);

-- Изменяем payment_system в таблице order_card
ALTER TABLE order_card ALTER COLUMN payment_system TYPE varchar(200);

-- Изменяем interval_length в таблице purchase_item
ALTER TABLE purchase_item ALTER COLUMN interval_length TYPE varchar(200);

-- Удаляем enum типы, так как они больше не используются
DROP TYPE IF EXISTS subscription_type;
DROP TYPE IF EXISTS sale_rule_type;
DROP TYPE IF EXISTS sale_rule_logic;
DROP TYPE IF EXISTS invoice_status;
DROP TYPE IF EXISTS card_type;
DROP TYPE IF EXISTS payment_system;
DROP TYPE IF EXISTS interval_length; 