-- Создание enum типов
CREATE TYPE subscription_type AS ENUM ('WALLET', 'TRAVEL', 'UNLIMITED');
CREATE TYPE sale_rule_type AS ENUM ('MONTHLY_RANGE', 'WEEKLY_RANGE', 'DAILY_RANGE', 'BALANCE_RANGE');
CREATE TYPE sale_rule_logic AS ENUM ('AND', 'OR');
CREATE TYPE invoice_status AS ENUM ('CREATED', 'PAID', 'CANCELED', 'OUTDATED');
CREATE TYPE card_type AS ENUM ('TRANSPORT', 'IPS');
CREATE TYPE payment_system AS ENUM ('VISA', 'MASTERCARD', 'MIR');
CREATE TYPE interval_length AS ENUM ('M', 'D');

-- Таблица связки регионов и проектов
CREATE TABLE region_project (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    region_id VARCHAR NOT NULL,
    project_id UUID NOT NULL,
    can_decode_hash BOOLEAN NOT NULL DEFAULT false,
    pan_decode_key VARCHAR,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица услуг
CREATE TABLE service (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    template_id UUID NOT NULL,
    service_code VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    is_social BOOLEAN NOT NULL DEFAULT false,
    subscription_type subscription_type NOT NULL,
    cost BIGINT,
    action_start_date date,
    action_end_date date,
    min_replenishment_amount BIGINT,
    max_replenishment_amount BIGINT,
    recommended_amount BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица связки услуг и агентов
CREATE TABLE service_agent (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES service(id),
    agent_id UUID NOT NULL,
    agent_version INT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица правил продаж
CREATE TABLE sale_rule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES service(id),
    rule_type sale_rule_type NOT NULL,
    rule_logic sale_rule_logic NOT NULL,
    start_day INT,
    end_day INT,
    start_month INT,
    end_month INT,
    start_week INT,
    end_week INT,
    min_card_balance BIGINT,
    max_card_balance BIGINT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица счетов
CREATE TABLE invoice (
    invoice_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_transaction_id VARCHAR NOT NULL,
    invoice_status invoice_status NOT NULL,
    invoice_amount BIGINT NOT NULL,
    region_id VARCHAR NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица заказов
CREATE TABLE "order" (
    order_id VARCHAR PRIMARY KEY,
    invoice_id UUID NOT NULL REFERENCES invoice(invoice_id),
    order_status VARCHAR NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица карт в заказе
CREATE TABLE order_card (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR NOT NULL REFERENCES "order"(order_id),
    card_type card_type NOT NULL,
    pan VARCHAR,
    pan_hash VARCHAR,
    payment_system payment_system,
    card_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица элементов пополнения
CREATE TABLE replenishment_item (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_card_id UUID NOT NULL REFERENCES order_card(id),
    item_type VARCHAR NOT NULL,
    description VARCHAR,
    replenishment_amount BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица элементов покупки
CREATE TABLE purchase_item (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_card_id UUID NOT NULL REFERENCES order_card(id),
    service_id UUID NOT NULL REFERENCES service(id),
    service_id_string VARCHAR NOT NULL,
    replenishment_amount BIGINT,
    used_counter_amount BIGINT,
    cost BIGINT NOT NULL,
    description_text VARCHAR,
    interval_amount INT,
    interval_length interval_length,
    action_start_date TIMESTAMP,
    action_end_date TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблица кэша карт
CREATE TABLE card (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    card_id UUID NOT NULL,
    pan VARCHAR,
    pan_hash VARCHAR,
    payment_system payment_system,
    account_reference VARCHAR,
    balance_amount BIGINT NOT NULL,
    region_id VARCHAR NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Создание индексов
CREATE INDEX idx_region_project_region_id ON region_project(region_id);
CREATE INDEX idx_region_project_project_id ON region_project(project_id);
CREATE INDEX idx_service_project_id ON service(project_id);
CREATE INDEX idx_service_service_code ON service(service_code);
CREATE INDEX idx_service_agent_service_id ON service_agent(service_id);
CREATE INDEX idx_service_agent_agent_id ON service_agent(agent_id);
CREATE INDEX idx_sale_rule_service_id ON sale_rule(service_id);
CREATE INDEX idx_sale_rule_active ON sale_rule(is_active);
CREATE INDEX idx_invoice_agent_transaction_id ON invoice(agent_transaction_id);
CREATE INDEX idx_invoice_status ON invoice(invoice_status);
CREATE INDEX idx_order_invoice_id ON "order"(invoice_id);
CREATE INDEX idx_order_card_order_id ON order_card(order_id);
CREATE INDEX idx_order_card_card_id ON order_card(card_id);
CREATE INDEX idx_replenishment_item_order_card_id ON replenishment_item(order_card_id);
CREATE INDEX idx_purchase_item_order_card_id ON purchase_item(order_card_id);
CREATE INDEX idx_purchase_item_service_id ON purchase_item(service_id);
CREATE INDEX idx_card_card_id ON card(card_id);
CREATE INDEX idx_card_pan ON card(pan);
CREATE INDEX idx_card_pan_hash ON card(pan_hash);
CREATE INDEX idx_card_region_id ON card(region_id); 