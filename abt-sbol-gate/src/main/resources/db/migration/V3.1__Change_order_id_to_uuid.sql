-- Миграция для изменения типа поля order_id с VARCHAR на UUID
-- Сначала удаляем внешние ключи, которые ссылаются на order_id
ALTER TABLE order_card DROP CONSTRAINT IF EXISTS order_card_order_id_fkey;

-- Изменяем тип поля order_id в таблице order
ALTER TABLE "order" ALTER COLUMN order_id TYPE UUID USING order_id::UUID;
ALTER TABLE "order" ALTER COLUMN order_id SET DEFAULT gen_random_uuid();

-- Изменяем тип поля order_id в таблице order_card
ALTER TABLE order_card ALTER COLUMN order_id TYPE UUID USING order_id::UUID;

-- Восстанавливаем внешний ключ
ALTER TABLE order_card ADD CONSTRAINT order_card_order_id_fkey 
    FOREIGN KEY (order_id) REFERENCES "order"(order_id);

-- Добавляем комментарии
COMMENT ON COLUMN "order".order_id IS 'UUID идентификатор заказа (автогенерируемый)';
COMMENT ON COLUMN order_card.order_id IS 'UUID идентификатор заказа'; 