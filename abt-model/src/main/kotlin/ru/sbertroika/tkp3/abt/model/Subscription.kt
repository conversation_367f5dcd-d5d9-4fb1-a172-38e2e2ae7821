package ru.sbertroika.tkp3.abt.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.relational.core.mapping.Table
import org.springframework.data.relational.core.mapping.Column
import java.time.ZonedDateTime
import java.util.*

/**
 * Абонемент
 */
@Table(value = "subscription")
data class Subscription (
    /**
     * Идентификатор абонемента
     */
    @Column("sub_id")
    val id: UUID,

    /**
     * Время формирования абонемента
     */
    @Column("sub_created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var createdAt: ZonedDateTime? = null,

    /**
     * Идентификатор карты
     */
    @Column("sub_crd_id")
    val crdId: UUID,

    /**
     * Числовой Идентификатор абонемента
     * При создании задается автоматически через сквозной счетчик
     * Идентификатор служит для формирования белых список в паре с
     */
    @Column("sub_abonement_id")
    val abonementId: Long? = null,

    /**
     * Идентификатор проекта
     */
    @Column("sub_project_id")
    val projectId: UUID,

    /**
     * Время начала действия абонемента
     */
    @Column("sub_active_from")
    var activeFrom: ZonedDateTime? = null,

    /**
     * Время окончания действия абонемента
     */
    @Column("sub_active_till")
    var activeTill: ZonedDateTime? = null,

    /**
     * Название
     */
    @Column("sub_name")
    val name: String,

    /**
     * Расширенное описание
     */
    @Column("sub_description")
    val description: String?,

    /**
     * Является социальным
     */
    @Column("sub_is_social")
    val isSocial: Boolean,

    /**
     * Идентификатор шаблона
     */
    @Column("sub_tt_id")
    val ttId: UUID,

    /**
     * Версия шаблона
     */
    @Column("sub_tt_version")
    val ttVersion: Int,

    /**
     * Теги
     */
    @Column("sub_tags")
    val tags: String? = null,

    /**
     * Источник эмиссии абонемента
     */
    @Column("sub_emission_source")
    val emissionSource: String? = null
)