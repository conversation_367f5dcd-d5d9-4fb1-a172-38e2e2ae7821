package ru.sbertroika.tkp3.abt.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.relational.core.mapping.Table
import org.springframework.data.relational.core.mapping.Column
import java.time.ZonedDateTime
import java.util.*

/**
 * Счетчик абонемента
 */
@Table(value = "subscription_base_counter")
data class SubscriptionBaseCounter(
    /**
     * Идентификатор счетчика
     */
    @Column("sbc_id")
    val id: UUID? = null,

    /**
     * Время формирования билета
     */
    @Column("sbc_created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var createdAt: ZonedDateTime? = null,

    /**
     * Идентификатор абонемента
     */
    @Column("sbc_subscription_id")
    val subscriptionId: UUID,

    /**
     * Тип счетчика
     */
    @Column("sbc_counter_type")
    val counterType: String,

    /**
     * Значение счетчика
     */
    @Column("sbc_counter_value")
    val counterValue: Int,

    /**
     * Признак автобуса
     */
    @Column("sbc_is_bus")
    val isBus: Boolean,

    /**
     * Признак троллейбуса
     */
    @Column("sbc_is_trolleybus")
    val isTrolleybus: Boolean,

    /**
     * Признак трамвая
     */
    @Column("sbc_is_tram")
    val isTram: Boolean,

    /**
     * Признак метро
     */
    @Column("sbc_is_metro")
    val isMetro: Boolean,

    /**
     * Идентификатор проекта
     */
    @Column("sbc_project_id")
    val projectId: UUID
)
