package ru.sbertroika.tkp3.abt.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class SubscriptionTemplatePK(
    val oId: UUID? = null,
    val oVersion: Int? = null
)

@Table(value = "subscription_template")
data class SubscriptionTemplate(

    @Id
    @Column("st_id")
    var id: UUID? = null,

    @Column("st_version")
    var version: Int? = null,

    @Column("st_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("st_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("st_project_id")
    var projectId: UUID? = null,

    @Column("st_active_from")
    var activeFrom: Timestamp? = null,

    @Column("st_active_till")
    var activeTill: Timestamp? = null,

    /**
     * Название
     */
    @Column("st_name")
    var stName: String? = null,

    /**
     * Расширенное описание
     */
    @Column("st_description")
    var description: String? = null,

    /**
     * Тип билета
     * @see SubscriptionTemplateType
     */
    @Column("st_type")
    var type: String? = null,

    /**
     * Код приложения
     */
    @Column("st_app_code")
    var appCode: Int? = null,

    /**
     * Код билета
     */
    @Column("st_crd_code")
    var crdCode: Int? = null,

    /**
     * Является социальным
     */
    @Column("st_is_social")
    var isSocial: Boolean? = null,

    /**
     * Тип срока действия
     * @see SubscriptionTemplateValidTimeType
     */
    @Column("st_valid_time_type")
    var validTimeType: String? = null,

    @Column("st_valid_time_start")
    var validTimeStart: Timestamp? = null,

    @Column("st_valid_time_days")
    var validTimeDays: Int? = null,

    @Column("st_valid_time_end")
    var validTimeEnd: Timestamp? = null
)

enum class SubscriptionTemplateType {
    /**
     * Кошелек
     */
    WALLET,

    /**
     * Поездочный
     */
    TRAVEL,

    /**
     * Безлимитный
     */
    UNLIMITED
}

enum class SubscriptionTemplateValidTimeType {
    /**
     * С периодом на календарный месяц
     */
    INTERVAL,

    /**
     * Списание в период(дней) от первой поездки
     */
    DAYS,

    /**
     * Списание в период(дней) от первой поездки, но не позднее даты
     */
    INTERVAL_AND_DAYS
}
