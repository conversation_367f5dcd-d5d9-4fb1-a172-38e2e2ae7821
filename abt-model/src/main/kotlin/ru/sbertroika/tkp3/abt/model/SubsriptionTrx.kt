package ru.sbertroika.tkp3.abt.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.UUID

@Table("abt_trx")
data class SubsriptionTrx(
    /**
     * Идентификатор транзакции (заказа)
     */
    @Id
    @Column("trx_id")
    var trxId: UUID? = null,

    /**
     * Время формирования транзакции на терминале
     */
    @Column("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var createdAt: ZonedDateTime? = null,

    /**
     * Время формирования транзакции на сервере
     */
    @Column("record_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var recordAt: ZonedDateTime? = null,

    /**
     * Идентификатор карты
     */
    @Column("crd_id")
    var crdId: UUID? = null,

    /**
     * Статус
     */
    @Column("status")
    var status: AbtPassStatusType? = null,

    /**
     * Идентификатор абонемента
     */
    @Column("subscription_id")
    var subscriptionId: UUID? = null,

    /**
     * Идентификатор шаблона
     */
    @Column("tpl_id")
    var templateId: UUID? = null,

    /**
     * Идентификатор шаблона
     */
    @Column("tpl_ver")
    var templateVer: UInt? = null,

    /**
     * Заводской номер терминала
     */
    @Column("terminal_serial")
    var terminalSerial: String? = null,

    /**
     * Номер смены на терминале
     */
    @Column("shift_num")
    var shiftNum: UInt? = null,

    /**
     * Единый номер операции на терминале (уникальный в рамках смены)
     */
    @Column("ern")
    var ern: UInt? = null,

    /**
     * Сумма авторизации
     */
    @Column("amount")
    var amount: UInt? = null,

    /**
     * Тэги
     */
    @Column("tags")
    var tags: String? = null
)

enum class AbtPassStatusType {
    SUCCESS, OVERLIMIT
}
