package ru.sbertroika.tkp3.abt.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class SubscriptionTemplateRulePK(
    val oId: UUID? = null,
    val oVersion: Int? = null
)

@Table(value = "subscription_template_rule")
data class SubscriptionTemplateRule(
    /**
     * Идентификатор
     */
    @Id
    @Column("str_id")
    var id: UUID? = null,

    /**
     * Версия
     */
    @Column("str_version")
    var version: Int? = null,

    /**
     * Время создания версии
     */
    @Column("str_version_created_at")
    var versionCreatedAt: java.sql.Timestamp? = null,

    /**
     * Кто создал версию
     */
    @Column("str_version_created_by")
    var versionCreatedBy: UUID? = null,

    /**
     * Идентификатор шаблона
     */
    @Column("str_subscription_template_id")
    var subscriptionTemplateId: UUID? = null,

    /**
     * Индекс прохода
     */
    @Column("str_pass_index")
    var passIndex: Int? = null,

    /**
     * Действие прохода
     */
    @Column("str_pass_action")
    var passAction: String? = null,

    /**
     * Идентификатор проекта
     */
    @Column("str_project_id")
    var projectId: UUID? = null
)
