package ru.sbertroika.tkp3.abt.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class SubscriptionTemplateBindingPK(
    val oId: UUID? = null,
    val oVersion: Int? = null
)

@Table(value = "subscription_template_binding")
data class SubscriptionTemplateBinding(
    /**
     * Идентификатор
     */
    @Id
    @Column("stb_id")
    var id: UUID? = null,

    /**
     * Версия
     */
    @Column("stb_version")
    var version: Int? = null,

    /**
     * Время создания версии
     */
    @Column("stb_version_created_at")
    var versionCreatedAt: java.sql.Timestamp? = null,

    /**
     * Кто создал версию
     */
    @Column("stb_version_created_by")
    var versionCreatedBy: UUID? = null,

    /**
     * Идентификатор шаблона
     */
    @Column("stb_subscription_template_id")
    var subscriptionTemplateId: UUID? = null,

    /**
     * Идентификатор типа карты
     */
    @Column("stb_card_type_id")
    var cardTypeId: UUID? = null,

    /**
     * Дата начала действия
     */
    @Column("stb_active_from")
    var activeFrom: java.sql.Timestamp? = null,

    /**
     * Дата окончания действия
     */
    @Column("stb_active_till")
    var activeTill: java.sql.Timestamp? = null,

    /**
     * Идентификатор проекта
     */
    @Column("stb_project_id")
    var projectId: UUID? = null
)
