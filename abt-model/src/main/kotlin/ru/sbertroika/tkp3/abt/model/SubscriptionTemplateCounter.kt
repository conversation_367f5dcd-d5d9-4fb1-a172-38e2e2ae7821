package ru.sbertroika.tkp3.abt.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class SubscriptionTemplateCounterPK(
    val oId: UUID? = null,
    val oVersion: Int? = null
)

@Table(value = "subscription_template_counter")
data class SubscriptionTemplateCounter(
    /**
     * Идентификатор
     */
    @Id
    @Column("stc_id")
    var id: UUID? = null,

    /**
     * Версия
     */
    @Column("stc_version")
    var version: Int? = null,

    /**
     * Время создания версии
     */
    @Column("stc_version_created_at")
    var versionCreatedAt: java.sql.Timestamp? = null,

    /**
     * Кто создал версию
     */
    @Column("stc_version_created_by")
    var versionCreatedBy: UUID? = null,

    /**
     * Идентификатор шаблона
     */
    @Column("stc_subscription_template_id")
    var subscriptionTemplateId: UUID? = null,

    /**
     * Тип счетчика
     */
    @Column("stc_counter_type")
    var type: String? = null,

    /**
     * Начальное число поездок на счетчике (при покупке)
     */
    @Column("stc_counter_value")
    var value: Int? = null,

    /**
     * Признак автобуса
     */
    @Column("stc_is_bus")
    var isBus: Boolean? = null,

    /**
     * Признак троллейбуса
     */
    @Column("stc_is_trolleybus")
    var isTrolleybus: Boolean? = null,

    /**
     * Признак трамвая
     */
    @Column("stc_is_tram")
    var isTram: Boolean? = null,

    /**
     * Признак метро
     */
    @Column("stc_is_metro")
    var isMetro: Boolean? = null,

    /**
     * Идентификатор проекта
     */
    @Column("stc_project_id")
    var projectId: UUID? = null
)

enum class SubscriptionTemplateCounterType {
    /**
     * Один счетчик на все виды транспорта
     */
    ALL,

    /**
     * Один счетчик на все разрешенные виды транспорта
     */
    ALLOW_LIST,

    /**
     * Для каждого перечисленного вида транспорта свой счетчик
     */
    SINGLE,

    /**
     * Неограниченное число поездок
     */
    ALL_UNLIMITED,

    /**
     * Неограниченное число поездок на все разрешенные виды транспорта
     */
    ALLOW_LIST_UNLIMITED
}
