openapi: 3.0.3
info:
  title: SBOL REST API
  description: |
    REST API для работы с Сбербанк Онлайн (SBOL) интеграцией.
    
    ## Описание
    API предоставляет интерфейс для работы с транспортными и банковскими картами через Сбербанк Онлайн.
    Поддерживает операции пополнения счетов и приобретения услуг.
    
    ## Аутентификация
    API использует заголовки для аутентификации:
    - `X-Agent-ID` - идентификатор агента
    - `X-Client-Cert` - клиентский сертификат
    
    ## Основные операции
    - Получение доступных операций для транспортных карт
    - Получение доступных операций для банковских карт
    - Выставление счетов
    - Подтверждение статуса операций
  version: 1.0.0
  contact:
    name: Agent Gateway Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.agent-gateway.com
    description: Продакшн среда
  - url: https://dev-api.agent-gateway.com
    description: Среда разработки

tags:
  - name: SBOL
    description: Операции с Сбербанк Онлайн

paths:
  /sbol/regions/{region_id}/cards/transport/{pan}/available-operations:
    get:
      tags:
        - SBOL
      summary: Перечень доступных операций ТК
      description: Запрос на получение доступных действий для транспортной карты
      operationId: getAvailableOperationsTransport
      parameters:
        - name: pan
          in: path
          required: true
          description: Номер транспортной карты, передается в открытом виде
          schema:
            type: string
            example: "****************"
          example: "****************"
        - name: region_id
          in: path
          required: true
          description: Регион
          schema:
            type: string
            example: "47"
          example: "47"
        - name: X-Agent-ID
          in: header
          description: Идентификатор агента
          schema:
            type: string
        - name: X-Client-Cert
          in: header
          description: Клиентский сертификат
          schema:
            type: string
      responses:
        '200':
          description: Запрос успешно выполнен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SBOLAvailableOperationsResponse'
              example:
                card:
                  pan: "****************"
                  accountReference: "ACC123456789"
                cardAccount:
                  balanceAmount: 15000
                counterReplenishment:
                  allowed: true
                  restriction:
                    value:
                      minAmount: 1000
                      maxAmount: 50000
                      recommendedAmount: 10000
                    upTo:
                      minAmount: 0
                      maxAmount: 100000
                servicePurchase:
                  allowed: true
                  availableCounterAmount: 15000
                  services:
                    - serviceId: "service-001"
                      cost: 5000
                      actionRange:
                        startDate: "2024-01-01T00:00:00Z"
                        endDate: "2024-02-01T00:00:00Z"
                      description:
                        textNote: "Проездной на месяц"
                        intervalAmount: 1
                        intervalLength: "M"
        '400':
          description: Ошибка запроса, пожалуйста, проверьте входные параметры
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SBOLAvailableOperationsResponse'
              example:
                errorMsg: "Карта не найдена в кэше"

  /sbol/regions/{region_id}/cards/ips/{ips_name}/{pan_Hash}/available-operations:
    post:
      tags:
        - SBOL
      summary: Перечень доступных операций БК
      description: Запрос на получение доступных действий для банковской карты
      operationId: getAvailableOperationsBank
      parameters:
        - name: region_id
          in: path
          required: true
          description: Регион
          schema:
            type: string
            example: "47"
          example: "47"
        - name: ips_name
          in: path
          required: true
          description: "Наименование платежной системы, Возможные варианты: VISA, MASTERCARD, MIR"
          schema:
            type: string
            enum: [VISA, MASTERCARD, MIR]
            example: "MIR"
          example: "MIR"
        - name: pan_Hash
          in: path
          required: true
          description: Хэш PAN-карты
          schema:
            type: string
            example: "0009B988BDE0A5B322C0685C5D806D2632C09324"
          example: "0009B988BDE0A5B322C0685C5D806D2632C09324"
        - name: X-Agent-ID
          in: header
          description: Идентификатор агента
          schema:
            type: string
        - name: X-Client-Cert
          in: header
          description: Клиентский сертификат
          schema:
            type: string
      responses:
        '200':
          description: Запрос успешно выполнен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SBOLAvailableOperationsResponse'
        '400':
          description: Ошибка запроса, пожалуйста, проверьте входные параметры
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SBOLAvailableOperationsResponse'

  /sbol/regions/{region_id}/invoices/one-click:
    post:
      tags:
        - SBOL
      summary: Запрос на выставление счета по выбранному действию
      description: |
        Набор полей зависит от используемой клиентом карты (транспортная либо банковская) 
        и действия, выбранного клиентом (пополнение счета либо приобретение услуг)
      operationId: createOrder
      parameters:
        - name: region_id
          in: path
          required: true
          description: Регион
          schema:
            type: string
            example: "47"
          example: "47"
        - name: X-Agent-ID
          in: header
          description: Идентификатор агента
          schema:
            type: string
        - name: X-Client-Cert
          in: header
          description: Клиентский сертификат
          schema:
            type: string
      requestBody:
        required: true
        description: Тело запроса
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SBOLOrderOperationRequest'
            example:
              agentTransactionId: "trx-123456789"
              order:
                orderCards:
                  - transportCard:
                      pan: "****************"
                    replenishment:
                      amount: 10000
                      type: "VALUE"
      responses:
        '200':
          description: Запрос успешно выполнен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SBOLOrderOperationResponse'
              example:
                invoiceId: "4cdf8fbe-a89f-4862-be83-5e44aab2ef7a"
                invoiceStatus: "CREATED"
                invoiceAmount: 10000
                agentTransactionId: "trx-123456789"
                order:
                  orderId: "order-123456789"
                  orderStatus: "CREATED"
                  orderCards:
                    - transportCard:
                        pan: "****************"
                      replenishmentItems:
                        - itemType: "VALUE"
                          description: "Пополнение счета"
                          replenishmentAmount: 10000
        '400':
          description: Ошибка запроса, пожалуйста, проверьте входные параметры
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SBOLOrderOperationResponse'

  /sbol/regions/{region_id}/invoices/{invoice_id}/status:
    post:
      tags:
        - SBOL
      summary: Запрос на подтверждение пополнения транспортной карты
      description: Изменение статуса счета
      operationId: updatePaymentStatus
      parameters:
        - name: region_id
          in: path
          required: true
          description: Регион
          schema:
            type: string
            example: "47"
          example: "47"
        - name: invoice_id
          in: path
          required: true
          description: Идентификатор счета
          schema:
            type: string
            format: uuid
            example: "4cdf8fbe-a89f-4862-be83-5e44aab2ef7a"
          example: "4cdf8fbe-a89f-4862-be83-5e44aab2ef7a"
      requestBody:
        required: true
        description: Тело запроса
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SBOLChangeStatusRequest'
            example:
              agentTransactionId: "trx-123456789"
              invoiceStatus: "PAID"
      responses:
        '200':
          description: Запрос успешно выполнен
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "SUCCESS"
              example:
                status: "SUCCESS"
        '400':
          description: Ошибка запроса, пожалуйста, проверьте входные параметры
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
              example:
                error: "Счет не найден"

components:
  schemas:


    SBOLAvailableOperationsResponse:
      type: object
      properties:
        card:
          $ref: '#/components/schemas/Card'
        cardAccount:
          $ref: '#/components/schemas/CardAccount'
        counterReplenishment:
          $ref: '#/components/schemas/SBOLCounterReplenishment'
        servicePurchase:
          $ref: '#/components/schemas/SBOLServicePurchase'
        errorMsg:
          type: string
          description: Текст ошибки (при ошибке исполнения)
          example: "Карта не найдена в кэше"

    Card:
      type: object
      properties:
        pan:
          type: string
          description: PAN карты
          example: "****************"
        panHash:
          type: string
          description: Хэш PAN-карты
          example: "0009B988BDE0A5B322C0685C5D806D2632C09324"
        paymentSystem:
          type: string
          description: "Наименование платежной системы, к которой принадлежит карта: VISA, MASTERCARD, MIR"
          enum: [VISA, MASTERCARD, MIR]
          example: "MIR"
        accountReference:
          type: string
          description: Идентификатор счета клиента
          example: "ACC123456789"

    CardAccount:
      type: object
      properties:
        balanceAmount:
          type: integer
          format: int64
          description: Остаток средств на счете карты
          example: 15000

    SBOLCounterReplenishment:
      type: object
      properties:
        allowed:
          type: boolean
          description: Признак доступности операции пополнения
          example: true
        restriction:
          $ref: '#/components/schemas/Restriction'

    Restriction:
      type: object
      properties:
        value:
          $ref: '#/components/schemas/Value'
        upTo:
          $ref: '#/components/schemas/UpTo'

    Value:
      type: object
      properties:
        minAmount:
          type: integer
          format: int64
          description: Минимально разрешенная сумма пополнения
          example: 1000
        maxAmount:
          type: integer
          format: int64
          description: Максимально разрешенная сумма пополнения
          example: 50000
        recommendedAmount:
          type: integer
          format: int64
          description: Рекомендованная сумма пополнения
          example: 10000

    UpTo:
      type: object
      properties:
        minAmount:
          type: integer
          format: int64
          description: Минимально разрешенный остаток средств по карте, до которого разрешено пополнение
          example: 0
        maxAmount:
          type: integer
          format: int64
          description: Максимально разрешенный остаток средств по карте, до которого разрешено пополнение
          example: 100000

    SBOLServicePurchase:
      type: object
      properties:
        allowed:
          type: boolean
          description: Признак разрешения услуг к приобретению
          example: true
        availableCounterAmount:
          type: integer
          format: int64
          description: Доступные на карте средства для оплаты услуг
          example: 15000
        services:
          type: array
          items:
            $ref: '#/components/schemas/Service'

    Service:
      type: object
      properties:
        serviceId:
          type: string
          description: Уникальный идентификатор услуги
          example: "service-001"
        cost:
          type: integer
          format: int64
          description: Стоимость приобретаемой услуги
          example: 5000
        actionRange:
          $ref: '#/components/schemas/SBOLActionRange'
        description:
          $ref: '#/components/schemas/SBOLDescription'

    SBOLDescription:
      type: object
      properties:
        textNote:
          type: string
          description: Текстовое поле описания услуги
          example: "Проездной на месяц"
        intervalAmount:
          type: integer
          description: Количество периодов
          example: 1
        intervalLength:
          type: string
          description: "Единица измерения длины периода. Возможные варианты: М – месяц, D – день"
          enum: [M, D]
          example: "M"

    SBOLActionRange:
      type: object
      properties:
        startDate:
          type: string
          format: date-time
          description: Дата начала действия
          example: "2024-01-01T00:00:00Z"
        endDate:
          type: string
          format: date-time
          description: Дата окончания действия
          example: "2024-02-01T00:00:00Z"

    SBOLOrderOperationRequest:
      type: object
      required:
        - agentTransactionId
        - order
      properties:
        agentTransactionId:
          type: string
          description: Идентификатор транзакции агента
          example: "trx-123456789"
        order:
          $ref: '#/components/schemas/Order'

    Order:
      type: object
      properties:
        orderCards:
          type: array
          items:
            $ref: '#/components/schemas/OrderCard'
          maxItems: 1

    OrderCard:
      type: object
      properties:
        ipsCard:
          $ref: '#/components/schemas/IpsCard'
        transportCard:
          $ref: '#/components/schemas/TransportCard'
        purchaseItems:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseItem'
          maxItems: 1
        replenishment:
          $ref: '#/components/schemas/Replenishment'

    IpsCard:
      type: object
      required:
        - panHash
        - paymentSystem
      properties:
        panHash:
          type: string
          description: Хэш PAN-карты
          example: "0009B988BDE0A5B322C0685C5D806D2632C09324"
        paymentSystem:
          type: string
          description: Платежная система
          enum: [VISA, MASTERCARD, MIR]
          example: "MIR"

    TransportCard:
      type: object
      required:
        - pan
      properties:
        pan:
          type: string
          description: PAN транспортной карты
          example: "****************"

    Replenishment:
      type: object
      required:
        - amount
        - type
      properties:
        amount:
          type: integer
          format: int64
          description: Сумма пополнения
          example: 10000
        type:
          type: string
          description: Тип пополнения
          example: "VALUE"

    PurchaseItem:
      type: object
      required:
        - serviceId
        - usedCounterAmount
      properties:
        serviceId:
          type: string
          format: uuid
          description: Идентификатор услуги
          example: "550e8400-e29b-41d4-a716-************"
        usedCounterAmount:
          type: integer
          format: int64
          description: Используемая сумма со счета
          example: 5000

    SBOLOrderOperationResponse:
      type: object
      properties:
        invoiceId:
          type: string
          format: uuid
          description: Идентификатор счета
          example: "4cdf8fbe-a89f-4862-be83-5e44aab2ef7a"
        invoiceStatus:
          type: string
          description: Статус счета
          enum: [CREATED, PAID, CANCELED, OUTDATED]
          example: "CREATED"
        invoiceAmount:
          type: integer
          format: int64
          description: Сумма счета
          example: 10000
        agentTransactionId:
          type: string
          description: Идентификатор транзакции агента
          example: "trx-123456789"
        order:
          $ref: '#/components/schemas/ResponseOrder'
        errorMsg:
          type: string
          description: Текст ошибки
          example: "Счет не найден"

    ResponseOrder:
      type: object
      properties:
        orderId:
          type: string
          description: Идентификатор заказа
          example: "order-123456789"
        orderStatus:
          type: string
          description: Статус заказа
          example: "CREATED"
        orderCards:
          type: array
          items:
            $ref: '#/components/schemas/ResponseOrderCard'
          maxItems: 1

    ResponseOrderCard:
      type: object
      properties:
        ipsCard:
          $ref: '#/components/schemas/IpsCard'
        transportCard:
          $ref: '#/components/schemas/TransportCard'
        purchaseItems:
          type: array
          items:
            $ref: '#/components/schemas/ResponsePurchaseItem'
          maxItems: 1
        replenishmentItems:
          type: array
          items:
            $ref: '#/components/schemas/ReplenishmentItem'
          maxItems: 1

    ResponsePurchaseItem:
      type: object
      properties:
        serviceId:
          type: string
          description: Идентификатор услуги
          example: "service-001"
        replenishmentAmount:
          type: integer
          format: int64
          description: Сумма пополнения
          example: 5000
        usedCounterAmount:
          type: integer
          format: int64
          description: Используемая сумма со счета
          example: 5000
        cost:
          type: integer
          format: int64
          description: Стоимость услуги
          example: 5000
        description:
          $ref: '#/components/schemas/SBOLDescription'
        actionRange:
          $ref: '#/components/schemas/SBOLActionRange'

    ReplenishmentItem:
      type: object
      properties:
        itemType:
          type: string
          description: Тип пополнения
          example: "VALUE"
        description:
          type: string
          description: Описание пополнения
          example: "Пополнение счета"
        replenishmentAmount:
          type: integer
          format: int64
          description: Сумма пополнения
          example: 10000

    SBOLChangeStatusRequest:
      type: object
      required:
        - agentTransactionId
        - invoiceStatus
      properties:
        agentTransactionId:
          type: string
          description: Идентификатор транзакции агента
          example: "trx-123456789"
        invoiceStatus:
          type: string
          description: Статус счета
          enum: [CREATED, PAID, CANCELED, OUTDATED]
          example: "PAID"

  securitySchemes:
    AgentIdHeader:
      type: apiKey
      in: header
      name: X-Agent-ID
      description: Идентификатор агента
    ClientCertHeader:
      type: apiKey
      in: header
      name: X-Client-Cert
      description: Клиентский сертификат

security:
  - AgentIdHeader: []
  - ClientCertHeader: [] 