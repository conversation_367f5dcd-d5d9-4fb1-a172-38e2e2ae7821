@startuml Agent Gate SBOL Database Structure

!define table(x) class x << (T,#FFAAAA) >>
!define primary_key(x) <b>x</b>
!define foreign_key(x) <i>x</i>

title Структура базы данных Agent Gate SBOL

' Основные таблицы
table(Agent) {
    primary_key(a_id): UUID
    primary_key(a_version): INT
    a_version_created_at: TIMESTAMP
    a_version_created_by: UUID
    a_organization_id: UUID
    a_active_from: TIMESTAMP
    a_active_till: TIMESTAMP
    a_status: INT
}

table(RegionProject) {
    primary_key(id): UUID
    foreign_key(region_id): STRING
    foreign_key(project_id): UUID
    can_decode_hash: BOOLEAN
    pan_decode_key: STRING
    created_at: TIMESTAMP
    updated_at: TIMESTAMP
}

table(Service) {
    primary_key(id): UUID
    foreign_key(project_id): UUID
    foreign_key(template_id): UUID
    service_code: STRING
    name: STRING
    description: TEXT
    is_social: BOOLEAN
    subscription_type: ENUM(WALLET, TRAVEL, UNLIMITED)
    cost: BIGINT
    action_start_date: TIMESTAMP
    action_end_date: TIMESTAMP
    min_replenishment_amount: BIGINT
    max_replenishment_amount: BIGINT
    recommended_amount: BIGINT
    created_at: TIMESTAMP
    updated_at: TIMESTAMP
}

table(ServiceAgent) {
    primary_key(id): UUID
    foreign_key(service_id): UUID
    foreign_key(agent_id): UUID
    foreign_key(agent_version): INT
    created_at: TIMESTAMP
}

table(SaleRule) {
    primary_key(id): UUID
    foreign_key(service_id): UUID
    rule_type: ENUM(MONTHLY_RANGE, WEEKLY_RANGE, DAILY_RANGE, BALANCE_RANGE)
    rule_logic: ENUM(AND, OR)
    start_day: INT
    end_day: INT
    start_month: INT
    end_month: INT
    start_week: INT
    end_week: INT
    min_card_balance: BIGINT
    max_card_balance: BIGINT
    is_active: BOOLEAN
    created_at: TIMESTAMP
    updated_at: TIMESTAMP
}

' Таблицы для работы с SBOL API
table(Invoice) {
    primary_key(invoice_id): UUID
    foreign_key(agent_transaction_id): STRING
    invoice_status: ENUM(CREATED, PAID, CANCELED, OUTDATED)
    invoice_amount: BIGINT
    region_id: STRING
    created_at: TIMESTAMP
    updated_at: TIMESTAMP
}

table(Order) {
    primary_key(order_id): STRING
    foreign_key(invoice_id): UUID
    order_status: STRING
    created_at: TIMESTAMP
    updated_at: TIMESTAMP
}

table(OrderCard) {
    primary_key(id): UUID
    foreign_key(order_id): STRING
    card_type: ENUM(TRANSPORT, IPS)
    pan: STRING
    pan_hash: STRING
    payment_system: ENUM(VISA, MASTERCARD, MIR)
    card_id: UUID
    created_at: TIMESTAMP
}

table(ReplenishmentItem) {
    primary_key(id): UUID
    foreign_key(order_card_id): UUID
    item_type: STRING
    description: STRING
    replenishment_amount: BIGINT
    created_at: TIMESTAMP
}

table(PurchaseItem) {
    primary_key(id): UUID
    foreign_key(order_card_id): UUID
    foreign_key(service_id): UUID
    service_id: STRING
    replenishment_amount: BIGINT
    used_counter_amount: BIGINT
    cost: BIGINT
    description_text: STRING
    interval_amount: INT
    interval_length: ENUM(M, D)
    action_start_date: TIMESTAMP
    action_end_date: TIMESTAMP
    created_at: TIMESTAMP
}

table(Card) {
    primary_key(id): UUID
    card_id: UUID
    pan: STRING
    pan_hash: STRING
    payment_system: ENUM(VISA, MASTERCARD, MIR)
    account_reference: STRING
    balance_amount: BIGINT
    region_id: STRING
    created_at: TIMESTAMP
    updated_at: TIMESTAMP
}

' Связи между таблицами
Agent ||--o{ ServiceAgent : "has"
Service ||--o{ ServiceAgent : "available_for"
Service ||--o{ SaleRule : "has"
Service ||--o{ PurchaseItem : "purchased_as"
RegionProject ||--o{ Service : "belongs_to"

Invoice ||--|| Order : "creates"
Order ||--o{ OrderCard : "contains"
OrderCard ||--o{ ReplenishmentItem : "has"
OrderCard ||--o{ PurchaseItem : "has"
OrderCard ||--o{ Card : "references"

@enduml 