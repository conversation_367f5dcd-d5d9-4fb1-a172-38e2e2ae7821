Требования к шлюзу по продажи абонементов и пополнению кошельков в СБОЛ (agent-gate-sbol)

Модель данных БД для шлюза по продажи абонементов и пополнению кошельков (agent-gate-sbol).
Есть товары, тарифы, поставщики услуг (средства оплаты).
Должны быть правила продажи: например продажа доступна с 1 по 10 число месяца.
Оператор заводит услугу (товар) следующим способом:
1) В интерфейсе управления шлюзом по проекту запрашивается список доступных для продажи абонементов из abt-подсистемы. ABT подсистема имеет свой rest api а интерфейсы связаны через федерацию.
2) Оператор выбирает из списка абонемент или кошелек.
   а) Если выбран кошелек то запрашиваются следующие параметры:
- Минимально разрешенная сумма пополнения
- Максимально разрешенная сумма пополнения
- Рекомендованная сумма пополнения
- Минимально разрешенный остаток средств по карте, до которого разрешено пополнение (200)
- Максимально разрешенный остаток средств по карте, до которого разрешено пополнение (по умолчанию 15000)
- Проект (берется из данных которые вернула abt-подсистема)
- Уникальный код услуги в регионе (берется из данных которые вернула abt-подсистема)
- Идентификатор шаблона (берется из данных которые вернула abt-подсистема)
- Наименование услуги (берется из данных которые вернула abt-подсистема)
- Описание услуги (берется из данных которые вернула abt-подсистема)
  б) Если выбран Поездочный или Безлимитный то запрашиваются следующие параметры:
- Стоимость приобретаемой услуги
- Период, на который приобретается услуга (начальная и конечная дата)
- Проект (берется из данных которые вернула abt-подсистема)
- Уникальный код услуги в регионе (берется из данных которые вернула abt-подсистема)
- Идентификатор шаблона (берется из данных которые вернула abt-подсистема)
- Наименование услуги (берется из данных которые вернула abt-подсистема)
- Описание услуги (берется из данных которые вернула abt-подсистема)
3) Выбираются из списка поставщики услуг для которых она доступна (таблица Agent).

````
data class AgentPK(
val aId: UUID? = null,
val aVersion: Int? = null,
): Serializable


@Table("agent")
data class Agent(

    @Column("a_id")
    var aId: UUID? = null,

    @Column("a_version")
    var aVersion: Int? = null,

    @Column("a_version_created_at")
    var aVersionCreatedAt: Timestamp? = null,

    @Column("a_version_created_by")
    var aVersionCreatedBy: UUID? = null,

    @Column("a_organization_id")
    var aOrganizationId: UUID? = null,

    @Column("a_active_from")
    var aActiveFrom: Timestamp? = null,

    @Column("a_active_till")
    var aActiveTill: Timestamp? = null,

    @Column("a_status")
    var aStatus: Int? = null,
)
````

Должна быть таблица для связки regionId и projectId.

Подсистема abt предоставляет список абонементов которые можно купить или список кошельков которые можно пополнить.
Модель данных которую возвращает abt-подсистема при запросе доступных абонементов или кошельков:
data SubscriptionList(
list: List<Subscription>
)

data Subscription(
// Идентификатор проекта
projectId: UUID,
// Идентификатор шаблона
templateId: UUID,
// Наименование абонемента
name: String,
// Описание абонемента
description: String,
// Уникальный код услуги в регионе
serviceCode: String,
// Социальный или не
isSocial: Boolean,
// Кошелек
type: SubscriptionType
)

enum SubscriptionType {
// Кошелек
WALLET,
// Поездочный,
TRAVEL
// Безлимитный
UNLIMITED
}



