# Структура базы данных Agent Gate SBOL

## Обзор

База данных для шлюза по продаже абонементов и пополнению кошельков в СБОЛ (agent-gate-sbol) состоит из нескольких логических групп таблиц:

1. **Основные таблицы** - управление агентами, услугами и правилами продаж
2. **Таблицы для работы с SBOL API** - обработка транзакций и заказов
3. **Справочные таблицы** - связки регионов и проектов

## Описание таблиц

### Основные таблицы

#### Agent
Таблица агентов (поставщиков услуг) с версионированием.
- `a_id` + `a_version` - составной первичный ключ для поддержки версионирования
- `a_organization_id` - связь с организацией
- `a_active_from/till` - период активности агента
- `a_status` - статус агента

#### RegionProject
Связка регионов и проектов для корректной работы с ABT-подсистемой.
- `region_id` - идентификатор региона (например, "47")
- `project_id` - идентификатор проекта из ABT-подсистемы
- `can_decode_hash` - флаг, указывающий нужно ли декодировать PAN-хэш для данного региона
- `pan_decode_key` - ключ для декодирования PAN-хэша (если требуется)

#### Service
Основная таблица услуг/товаров, получаемых из ABT-подсистемы.
- `project_id`, `template_id` - данные из ABT-подсистемы
- `service_code` - уникальный код услуги в регионе
- `subscription_type` - тип подписки (WALLET, TRAVEL, UNLIMITED)
- Поля для кошельков: `min_replenishment_amount`, `max_replenishment_amount`, `recommended_amount`
- Поля для абонементов: `cost`, `action_start_date`, `action_end_date`

#### ServiceAgent
Связка услуг и агентов, для которых эти услуги доступны.
- Многие-ко-многим связь между Service и Agent
- Поддерживает версионирование агентов

#### SaleRule
Правила продаж для услуг (например, продажа доступна с 1 по 10 число месяца).
- `rule_type` - тип правила (MONTHLY_RANGE, WEEKLY_RANGE, DAILY_RANGE, BALANCE_RANGE)
- `rule_logic` - логика применения правил (AND - все правила должны выполняться, OR - хотя бы одно правило должно выполняться)
- Гибкая настройка периодов продаж
- Для правил типа BALANCE_RANGE: `min_card_balance`, `max_card_balance` - ограничения по балансу карты
- Для одной услуги может быть несколько правил продаж, которые применяются согласно `rule_logic`

### Таблицы для работы с SBOL API

#### Invoice
Счета, создаваемые через SBOL API.
- `invoice_id` - UUID счета
- `agent_transaction_id` - идентификатор транзакции агента
- `invoice_status` - статус счета (CREATED, PAID, CANCELED, OUTDATED)
- `invoice_amount` - сумма счета в копейках

#### Order
Заказы, связанные со счетами.
- `order_id` - идентификатор заказа
- Связь 1:1 с Invoice

#### OrderCard
Карты в заказе (транспортные или банковские).
- `card_type` - тип карты (TRANSPORT, IPS)
- `pan` - номер карты (для транспортных)
- `pan_hash` - хэш PAN (для банковских)
- `payment_system` - платежная система (VISA, MASTERCARD, MIR)
- `card_id` - идентификатор карты в эмиссии

#### ReplenishmentItem
Элементы пополнения счета карты.
- `item_type` - тип пополнения (VALUE)
- `replenishment_amount` - сумма пополнения

#### PurchaseItem
Элементы покупки услуг.
- `service_id` - идентификатор услуги
- `used_counter_amount` - используемая сумма со счета
- `cost` - стоимость услуги
- Поля для описания услуги: `description_text`, `interval_amount`, `interval_length`
- Период действия: `action_start_date`, `action_end_date`

#### Card
Кэш информации о картах.
- `card_id` - идентификатор карты в эмиссии
- `pan` / `pan_hash` - номер карты или его хэш
- `balance_amount` - баланс карты
- `account_reference` - идентификатор счета клиента

## Связи между таблицами

1. **Agent ↔ ServiceAgent ↔ Service** - агенты имеют доступ к определенным услугам
2. **Service ↔ SaleRule** - услуги могут иметь несколько правил продаж
3. **RegionProject ↔ Service** - услуги принадлежат к проектам в регионах
4. **Invoice ↔ Order ↔ OrderCard** - иерархия заказов
5. **OrderCard ↔ ReplenishmentItem/PurchaseItem** - операции с картами
6. **OrderCard ↔ Card** - ссылка на кэш карт

## Особенности проектирования

1. **Версионирование агентов** - поддержка истории изменений агентов
2. **Гибкие правила продаж** - различные типы временных ограничений
3. **Поддержка разных типов карт** - транспортные и банковские карты
4. **Кэширование данных карт** - для оптимизации запросов к SBOL API
5. **Связь с ABT-подсистемой** - через таблицу RegionProject

## Типы данных

- **UUID** - для идентификаторов
- **BIGINT** - для денежных сумм в копейках
- **ENUM** - для ограниченных наборов значений
- **TIMESTAMP** - для временных меток
- **TEXT** - для длинных описаний 