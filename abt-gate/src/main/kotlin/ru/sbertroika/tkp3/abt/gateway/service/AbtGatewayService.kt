package ru.sbertroika.tkp3.abt.gateway.service

import arrow.core.Either
import ru.sbertroika.abt.gateway.v1.CreateSubscriptionRequest
import ru.sbertroika.abt.gateway.v1.SubscriptionListRequest
import ru.sbertroika.abt.gateway.v1.SubscriptionListResponse
import ru.sbertroika.abt.gateway.v1.WalletRefillRequest
import ru.sbertroika.abt.gateway.v1.WalletRefillResponse
import ru.sbertroika.tkp3.abt.gateway.model.CardInfo

interface AbtGatewayService {

    suspend fun getCardInfo(pan: String, projectId: String): Either<Throwable, CardInfo>

    suspend fun createSubscription(request: CreateSubscriptionRequest): Either<Throwable, String>

    suspend fun getSubscriptionList(request: SubscriptionListRequest): Either<Throwable, SubscriptionListResponse>
    
    suspend fun walletRefill(request: WalletRefillRequest): Either<Throwable, WalletRefillResponse>
}