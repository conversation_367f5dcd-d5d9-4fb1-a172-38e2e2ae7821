package ru.sbertroika.tkp3.abt.gateway.output.service

import arrow.core.Either
import arrow.core.left
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.emission.v1.ABTEmissionServiceGrpcKt
import ru.sbertroika.abt.emission.v1.CardInfoRequest
import ru.sbertroika.tkp3.abt.gateway.model.CardInfo
import ru.sbertroika.tkp3.abt.gateway.model.CardType

@Service
class AbtEmissionServiceImpl(
    @Value("\${abt_emission_url}")
    private val abtEmissionServiceUrl: String
): AbtEmissionService {

    private val channel = ManagedChannelBuilder.forTarget(abtEmissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)

    override suspend fun getCardInfo(pan: String, projectId: String): Either<Throwable, CardInfo> = Either.catch {
        val request = CardInfoRequest.newBuilder()
            .build()
        val response = client.cardInfo(request)
        if (response.hasError())
            return Error("getCardInfo() from abt-emission err: " + response.error.message).left()
        CardInfo(
            id = response.info.id,
            number = response.info.number,
            region = response.info.region,
            uid = response.info.uid,
            type = with(response.info.type) {
                CardType(
                    id = id,
                    name = name
                )
            }
        )
    }
}