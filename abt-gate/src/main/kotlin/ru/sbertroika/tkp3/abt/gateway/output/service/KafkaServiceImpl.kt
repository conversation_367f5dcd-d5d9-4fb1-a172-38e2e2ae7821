package ru.sbertroika.tkp3.abt.gateway.output.service

import arrow.core.Either
import arrow.core.left
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.abt.gateway.mapper
import ru.sbertroika.tkp3.abt.model.Subscription
import ru.sbertroika.tkp3.abt.model.SubscriptionBaseCounter
import java.time.format.DateTimeFormatter

@Service
class KafkaServiceImpl(
    kafkaProducerFactory: ProducerFactory<String, Any>,

    @Value("\${spring.kafka.subscription_topic}")
    val subscriptionTopic: String,

    @Value("\${spring.kafka.subscription_counters_topic}")
    val subscriptionCountersTopic: String,
) : KafkaService {

    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    companion object {
        private val DT_FMT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
    }

    override fun produceSubscription(sub: Subscription): Either<Throwable, Unit> = Either.catch {
        val out = ProducerRecord<String, Any>(subscriptionTopic, sub.id.toString(), mapper.writeValueAsString(sub))
        producer.send(out)
        Unit.left()
    }

    override fun produceCounter(counter: SubscriptionBaseCounter): Either<Throwable, Unit> = Either.catch {
        val out = ProducerRecord<String, Any>(subscriptionCountersTopic, counter.id.toString(), mapper.writeValueAsString(counter))
        producer.send(out)
        Unit.left()
    }
}