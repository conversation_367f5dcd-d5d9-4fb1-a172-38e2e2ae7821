package ru.sbertroika.tkp3.abt.gateway.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateRule
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateRulePK
import java.util.*

interface SubscriptionTemplateRuleRepository : CoroutineCrudRepository<SubscriptionTemplateRule, SubscriptionTemplateRulePK> {

    suspend fun findAllBySubscriptionTemplateId(subscriptionTemplateId: UUID): Flow<SubscriptionTemplateRule>
}