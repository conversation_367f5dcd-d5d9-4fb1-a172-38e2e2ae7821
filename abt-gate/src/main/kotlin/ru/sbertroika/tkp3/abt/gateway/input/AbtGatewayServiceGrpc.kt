package ru.sbertroika.tkp3.abt.gateway.input

import org.lognet.springboot.grpc.GRpcService
import org.slf4j.LoggerFactory
import ru.sbertroika.abt.gateway.v1.*
import ru.sbertroika.common.manifest.v1.ManifestRequest
import ru.sbertroika.common.toOperationError
import ru.sbertroika.tkp3.abt.gateway.output.service.ManifestService
import ru.sbertroika.tkp3.abt.gateway.service.AbtGatewayService

@GRpcService
class AbtGatewayServiceGrpc(
    private val manifestService: ManifestService,
    private val abtGatewayService: AbtGatewayService
) : AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineImplBase() {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    override suspend fun getManifest(request: ManifestRequest): ManifestResponse {
        return manifestService.getManifest(request).fold(
            {
                logger.error("Error generate manifest", it)
                manifestResponse {
                    error = toOperationError(Error(it))
                }
            },
            { res ->
                manifestResponse {
                    manifest = res
                }
            }
        )
    }

    override suspend fun cardInfo(request: CardInfoRequest): CardInfoRequestResponse =
        abtGatewayService.getCardInfo(request.pan, request.projectId).fold(
            {
                cardInfoRequestResponse {
                    error = toOperationError(Error(it))
                }
            },
            {
                cardInfoRequestResponse {
                    info = cardInfo {
                        id = it.id
                        number = it.number
                        type = cardType {
                            id = it.type?.id ?: ""
                            name = it.type?.name ?: ""
                        }
                    }
                }
            }
        )

    override suspend fun createSubscription(request: CreateSubscriptionRequest): CreateSubscriptionResponse =
        abtGatewayService.createSubscription(request).fold(
            {
                createSubscriptionResponse {
                    error = toOperationError(Error(it))
                }
            },
            { subId ->
                createSubscriptionResponse {
                    info = subscriptionInfo {
                        subscriptionId = subId
                    }
                }
            }
        )

    override suspend fun subscriptionList(request: SubscriptionListRequest): SubscriptionListResponse =
        abtGatewayService.getSubscriptionList(request).fold(
            {
                subscriptionListResponse {
                    error = toOperationError(Error(it))
                }
            },
            { response ->
                response
            }
        )

    override suspend fun walletRefill(request: WalletRefillRequest): WalletRefillResponse =
        abtGatewayService.walletRefill(request).fold(
            {
                walletRefillResponse {
                    error = toOperationError(Error(it))
                }
            },
            { response ->
                response
            }
        )
}