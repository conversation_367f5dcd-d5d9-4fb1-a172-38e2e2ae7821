package ru.sbertroika.tkp3.abt.gateway.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplate
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplatePK
import java.util.*

interface SubscriptionTemplateRepository : CoroutineCrudRepository<SubscriptionTemplate, SubscriptionTemplatePK> {

    @Query("SELECT * FROM subscription_template_mv where st_project_id = :projectId")
    suspend fun findAllByProjectId(projectId: UUID): Flow<SubscriptionTemplate>

    @Query("SELECT * FROM subscription_template_mv where st_id = :stId")
    suspend fun findById(stId: UUID): SubscriptionTemplate?
}