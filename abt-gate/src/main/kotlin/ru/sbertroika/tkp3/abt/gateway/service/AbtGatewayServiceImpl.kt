package ru.sbertroika.tkp3.abt.gateway.service

import arrow.core.Either
import arrow.core.getOrElse
import kotlinx.coroutines.flow.toList
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.abt.gateway.v1.*
import ru.sbertroika.common.NotFound
import ru.sbertroika.tkp3.abt.gateway.model.CardInfo
import ru.sbertroika.tkp3.abt.model.Subscription
import ru.sbertroika.tkp3.abt.model.SubscriptionBaseCounter
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateValidTimeType
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionTemplateCounterRepository
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionTemplateRepository
import ru.sbertroika.tkp3.abt.gateway.output.service.AbtEmissionService
import ru.sbertroika.tkp3.abt.gateway.output.service.KafkaService
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionRepository
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionBaseCounterRepository

@Service
class AbtGatewayServiceImpl(
    val abtEmissionService: AbtEmissionService,
    val kafkaService: KafkaService,
    val subscriptionTemplateRepository: SubscriptionTemplateRepository,
    val counterRepository: SubscriptionTemplateCounterRepository,
    val subscriptionRepository: SubscriptionRepository,
    val subscriptionBaseCounterRepository: SubscriptionBaseCounterRepository
): AbtGatewayService {

    private val log = LoggerFactory.getLogger(this::class.java)

    override suspend fun getCardInfo(pan: String, projectId: String): Either<Throwable, CardInfo> {
        return abtEmissionService.getCardInfo(pan, projectId)
    }

    override suspend fun createSubscription(request: CreateSubscriptionRequest): Either<Throwable, String> = Either.catch {
        val template = subscriptionTemplateRepository.findById(UUID.fromString(request.templateId))
            ?: throw NotFound("Subscription template not found by id = ${request.templateId}")
        val subscriptionId = UUID.randomUUID()
        val now = ZonedDateTime.now()
        val subscription = Subscription(
            id = subscriptionId,
            createdAt = now,
            crdId = UUID.fromString(request.cardId),
            projectId = UUID.fromString(request.projectId),
            activeFrom = if (template.activeFrom == null) now else ZonedDateTime.ofInstant(template.activeFrom!!.toInstant(), ZoneId.of("UTC")),
            activeTill = getActiveTill(template),
            name = template.stName!!,
            description = template.description ?: "",
            isSocial = template.isSocial!!,
            ttId = template.id!!,
            ttVersion = template.version!!,
            tags = null,
            emissionSource = if (request.hasEmissionSource()) request.emissionSource.name else "ABT" // По умолчанию устанавливаем ABT как источник эмиссии
        )
        subscriptionRepository.save(subscription)
        val kafkaResp = kafkaService.produceSubscription(subscription)
        kafkaResp.getOrElse {
            throw RuntimeException("Failed to produce subscription")
        }
        val counters = counterRepository.findAllBySubscriptionTemplateId(template.id!!).toList()
        if (counters.isEmpty())
            log.warn("counters count == 0 for template ${template.id}")
        counters.forEach { counter ->
            val counterModel = SubscriptionBaseCounter(
                subscriptionId = subscriptionId,
                createdAt = now,
                counterType = counter.type!!,
                counterValue = counter.value!!,
                isBus = counter.isBus!!,
                isTrolleybus = counter.isTrolleybus!!,
                isTram = counter.isTram!!,
                isMetro = counter.isMetro!!,
                projectId = UUID.fromString(request.projectId)
            )
            subscriptionBaseCounterRepository.save(counterModel)
            kafkaService.produceCounter(counterModel).getOrElse {
                throw RuntimeException("Failed to produce counter")
            }
        }
        subscriptionId.toString()
    }

    override suspend fun getSubscriptionList(request: SubscriptionListRequest): Either<Throwable, SubscriptionListResponse> = Either.catch {
        val filter = request.filter
        val cardId = UUID.fromString(filter.cardId)
        val projectId = if (filter.hasProjectId()) UUID.fromString(filter.projectId) else null
        val emissionSource = if (filter.hasEmissionSource()) filter.emissionSource.name else null
        val limit = if (request.hasLimit()) request.limit else 100
        val now = ZonedDateTime.now()

        val subscriptions = subscriptionRepository.findSubscriptionsByFilters(
            cardId = cardId,
            projectId = projectId,
            emissionSource = emissionSource,
            isActive = filter.isActive,
            isActual = filter.isActual,
            now = now,
            limit = limit
        )

        val subscriptionInfos = subscriptions.map { subscription ->
            SubscriptionInfo.newBuilder()
                .setSubscriptionId(subscription.id.toString())
                .setAbonementId(subscription.abonementId?.toString() ?: "")
                .setProjectId(subscription.projectId.toString())
                .setName(subscription.name)
                .setActiveFrom(subscription.activeFrom?.toInstant()?.toEpochMilli() ?: 0L)
                .setActiveTill(subscription.activeTill?.toInstant()?.toEpochMilli() ?: 0L)
                .setDescription(subscription.description ?: "")
                .setTemplateId(subscription.ttId.toString())
                .build()
        }

        SubscriptionListResponse.newBuilder()
            .setList(
                SubscriptionList.newBuilder()
                    .addAllInfo(subscriptionInfos)
                    .build()
            )
            .build()
    }

    private fun getActiveTill(template: SubscriptionTemplate): ZonedDateTime {
        val startDate = if (template.activeFrom == null) ZonedDateTime.now()
            else ZonedDateTime.ofInstant(template.activeFrom!!.toInstant(), ZoneId.of("UTC"))
        if (template.validTimeType == null)
            return startDate
        return when (template.validTimeType!!) {
            "INTERVAL" -> startDate.plusMonths(1)
            "DAYS" -> startDate.plusDays(template.validTimeDays!!.toLong())
            "INTERVAL_AND_DAYS" -> {
                val days = template.validTimeDays!!.toLong()
                if (template.validTimeEnd == null)
                    return startDate.plusDays(days)
                val activeTill = ZonedDateTime.ofInstant(template.activeTill!!.toInstant(), ZoneId.of("UTC"))
                if (startDate.plusDays(days).isBefore(activeTill))
                    startDate.plusDays(days)
                else activeTill
            }
            else -> startDate
        }
    }

    override suspend fun walletRefill(request: WalletRefillRequest): Either<Throwable, WalletRefillResponse> = Either.catch {
        log.info("Пополнение кошелька: cardId=${request.cardId}, value=${request.value}")
        
        val cardId = UUID.fromString(request.cardId)
        val projectId = if (request.hasProjectId()) UUID.fromString(request.projectId) else null
        val walletId = if (request.hasWalletId()) UUID.fromString(request.walletId) else null
        val templateId = if (request.hasTemplateId()) UUID.fromString(request.templateId) else null
        val refillValue = request.value.toInt()
        
        // Находим активные абонементы-кошельки на карте
        val now = ZonedDateTime.now()
        val walletSubscriptions = subscriptionRepository.findSubscriptionsByFilters(
            cardId = cardId,
            projectId = projectId,
            emissionSource = null, // Для кошельков не важно
            isActive = true,
            isActual = true,
            now = now,
            limit = 100
        ).filter { subscription ->
            // Фильтруем только кошельки (абонементы с типом WALLET)
            val template = subscriptionTemplateRepository.findById(subscription.ttId)
            template?.type == "WALLET"
        }
        
        if (walletSubscriptions.isEmpty()) {
            throw NotFound("Кошелек не найден для карты ${request.cardId}")
        }
        
        // Если указан конкретный walletId, ищем его
        val targetSubscription = if (walletId != null) {
            walletSubscriptions.find { it.id == walletId }
                ?: throw NotFound("Кошелек с ID $walletId не найден")
        } else if (templateId != null) {
            // Если указан templateId, ищем абонемент с этим шаблоном
            walletSubscriptions.find { it.ttId == templateId }
                ?: throw NotFound("Кошелек с шаблоном $templateId не найден")
        } else {
            // Берем первый найденный кошелек
            walletSubscriptions.first()
        }
        
        // Находим счетчики для этого абонемента
        val counters = subscriptionBaseCounterRepository.findAllBySubscriptionId(targetSubscription.id)
        
        if (counters.isEmpty()) {
            throw RuntimeException("Счетчики не найдены для кошелька ${targetSubscription.id}")
        }
        
        // Пополняем все счетчики кошелька
        var newBalance = 0
        counters.forEach { counter ->
            val oldValue = counter.counterValue
            val newValue = oldValue + refillValue
            val updatedCounter = counter.copy(counterValue = newValue)
            subscriptionBaseCounterRepository.save(updatedCounter)
            
            // Отправляем событие в Kafka
            kafkaService.produceCounter(updatedCounter).getOrElse {
                log.error("Failed to produce counter update for ${updatedCounter.id}")
            }
            
            newBalance = newValue
        }
        
        log.info("Кошелек ${targetSubscription.id} пополнен на $refillValue. Новый баланс: $newBalance")
        
        WalletRefillResponse.newBuilder()
            .setBalance(newBalance.toInt())
            .build()
    }
}
