package ru.sbertroika.tkp3.abt.gateway.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateCounter
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateCounterPK
import java.util.*

interface SubscriptionTemplateCounterRepository : CoroutineCrudRepository<SubscriptionTemplateCounter, SubscriptionTemplateCounterPK> {

    @Query("SELECT * FROM subscription_template_counter_mv where stc_subscription_template_id = :subscriptionTemplateId")
    suspend fun findAllBySubscriptionTemplateId(subscriptionTemplateId: UUID): Flow<SubscriptionTemplateCounter>
}