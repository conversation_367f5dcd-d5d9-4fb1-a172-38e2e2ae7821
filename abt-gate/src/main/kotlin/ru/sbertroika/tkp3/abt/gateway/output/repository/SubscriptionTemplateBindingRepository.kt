package ru.sbertroika.tkp3.abt.gateway.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateBinding
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateBindingPK
import java.util.*

interface SubscriptionTemplateBindingRepository : CoroutineCrudRepository<SubscriptionTemplateBinding, SubscriptionTemplateBindingPK> {

    suspend fun findAllBySubscriptionTemplateId(subscriptionTemplateId: UUID): Flow<SubscriptionTemplateBinding>
}