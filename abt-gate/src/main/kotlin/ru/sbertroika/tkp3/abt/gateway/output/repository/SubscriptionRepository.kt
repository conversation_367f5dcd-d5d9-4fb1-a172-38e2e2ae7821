package ru.sbertroika.tkp3.abt.gateway.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.abt.model.Subscription
import java.time.ZonedDateTime
import java.util.*

interface SubscriptionRepository : CoroutineCrudRepository<Subscription, UUID> {
    suspend fun findAllByCrdId(crdId: UUID): List<Subscription>
    suspend fun findAllByTtId(ttId: UUID): List<Subscription>
    
    @Query("""
        SELECT * FROM subscription 
        WHERE sub_crd_id = :cardId
        AND (:projectId IS NULL OR sub_project_id = :projectId)
        AND (:emissionSource IS NULL OR sub_emission_source = :emissionSource)
        AND (:isActive = false OR (sub_active_from <= :now AND sub_active_till >= :now))
        AND (:isActual = false OR sub_active_till >= :now)
        ORDER BY sub_created_at DESC
        LIMIT :limit
    """)
    suspend fun findSubscriptionsByFilters(
        @Param("cardId") cardId: UUID,
        @Param("projectId") projectId: UUID?,
        @Param("emissionSource") emissionSource: String?,
        @Param("isActive") isActive: Boolean,
        @Param("isActual") isActual: Boolean,
        @Param("now") now: ZonedDateTime,
        @Param("limit") limit: Int
    ): List<Subscription>
} 