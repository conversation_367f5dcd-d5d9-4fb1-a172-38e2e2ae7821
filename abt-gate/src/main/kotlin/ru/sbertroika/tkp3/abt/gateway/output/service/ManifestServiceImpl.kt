package ru.sbertroika.tkp3.abt.gateway.output.service

import arrow.core.Either
import com.google.protobuf.Timestamp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext
import org.springframework.stereotype.Service
import ru.sbertroika.common.manifest.v1.ManifestRequest
import ru.sbertroika.common.manifest.v1.pro.*
import ru.sbertroika.common.v1.AbonementType
import ru.sbertroika.common.v1.SubscriptionCounterType
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionTemplateBindingRepository
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionTemplateCounterRepository
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionTemplateRepository
import ru.sbertroika.tkp3.abt.gateway.output.repository.SubscriptionTemplateRuleRepository
import java.util.*

@Service
class ManifestServiceImpl(
    private val subscriptionTemplateRepository: SubscriptionTemplateRepository,
    private val subscriptionTemplateRuleRepository: SubscriptionTemplateRuleRepository,
    private val subscriptionTemplateCounterRepository: SubscriptionTemplateCounterRepository,
    private val subscriptionTemplateBindingRepository: SubscriptionTemplateBindingRepository
) : ManifestService {

    override suspend fun getManifest(request: ManifestRequest): Either<Throwable, ManifestProAbt> = Either.catch {
        withContext(Dispatchers.IO) {
            manifestProAbt {
                dict = manifestProAbtDict {
                    template += subscriptionTemplateRepository.findAllByProjectId(UUID.fromString(request.projectId)).map { template ->
                        subscriptionTemplate {
                            id = template.id.toString()
                            name = template.stName!!
                            version = template.version!!
                            appCode = template.appCode!!
                            crdCode = template.crdCode!!
                            type = AbonementType.valueOf(template.type!!)
                            isSocial = template.isSocial == true
                            validTimeType = if (template.validTimeType == null) ValidTimeType.DAYS
                            else ValidTimeType.valueOf(template.validTimeType!!)
                            validTimeStart = if (template.validTimeStart == null) Timestamp.getDefaultInstance()
                            else toProtoTimestamp(template.validTimeStart!!)
                            validTimeEnd = if (template.validTimeEnd == null) Timestamp.getDefaultInstance()
                            else toProtoTimestamp(template.validTimeEnd!!)
                            validTimeDays = template.validTimeDays ?: 0

                            rules += subscriptionTemplateRuleRepository.findAllBySubscriptionTemplateId(template.id!!).map { rule ->
                                subscriptionTemplatePassRule {
                                    id = rule.id.toString()
                                    version = rule.version!!
                                    index = rule.passIndex!!
                                    action = rule.passAction!!
                                }
                            }.toList()
                            counter += subscriptionTemplateCounterRepository.findAllBySubscriptionTemplateId(template.id!!).map { counter ->
                                subscriptionTemplateCounter {
                                    id = counter.id.toString()
                                    version = counter.version!!
                                    type = SubscriptionCounterType.valueOf("SCT_${counter.type!!}")
                                    value = counter.value!!
                                    isBus = counter.isBus == true
                                    isTrolleybus = counter.isTrolleybus == true
                                    isTram = counter.isTram == true
                                    isMetro = counter.isMetro == true
                                }
                            }.toList()
                            cardType += subscriptionTemplateBindingRepository.findAllBySubscriptionTemplateId(template.id!!).map { cardType ->
                                abtCardType {
                                    id = cardType.cardTypeId!!.toString()
                                }
                            }.toList()
                        }
                    }.toList()
                }
            }
        }
    }

    private fun toProtoTimestamp(time: java.sql.Timestamp): Timestamp {
        val instant = time.toInstant()
        return Timestamp.newBuilder()
            .setSeconds(instant.epochSecond)
            .setNanos(instant.nano)
            .build()
    }
}