spring:
  r2dbc:
    url: r2dbc:pool:${DB_R2URL:postgresql://postgres:postgres@localhost:5432/abt}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5432/abt}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    subscription_topic: ${ABT_SUBSCRIPTION_OUT_TOPIC:ABT.SUBSCRIPTION}
    subscription_counters_topic: ${ABT_SUBSCRIPTION_COUNTER_OUT_TOPIC:ABT.SUBSCRIPTION.COUNTER}

abt_emission_url: ${ABT_EMISSION_URL:localhost:5008}

grpc:
  port: 5011
