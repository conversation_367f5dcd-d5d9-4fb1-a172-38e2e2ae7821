-- Миграция для замены enum типов на VARCHAR поля
-- Это позволит избежать проблем с конвертацией enum в Spring Data R2DBC

-- 0. Удаляем триггеры и функции для материализованных представлений
DROP TRIGGER IF EXISTS trigger_refresh_subscription_template_mv ON subscription_template;
DROP TRIGGER IF EXISTS trigger_refresh_subscription_template_counter_mv ON subscription_template_counter;
DROP FUNCTION IF EXISTS refresh_subscription_template_mv();
DROP FUNCTION IF EXISTS refresh_subscription_template_counter_mv();

-- 1. Удаляем материализованные представления
DROP MATERIALIZED VIEW IF EXISTS subscription_template_mv;
DROP MATERIALIZED VIEW IF EXISTS subscription_template_counter_mv;

-- 2. Изменяем поля в таблице subscription_template
ALTER TABLE subscription_template 
    ALTER COLUMN st_type TYPE VARCHAR(250),
    ALTER COLUMN st_valid_time_type TYPE VARCHAR(250);

-- 3. Изменяем поля в таблице subscription_template_counter
ALTER TABLE subscription_template_counter 
    ALTER COLUMN stc_counter_type TYPE VARCHAR(250);

-- 4. Изменяем поля в таблице subscription_base_counter
ALTER TABLE subscription_base_counter 
    ALTER COLUMN sbc_counter_type TYPE VARCHAR(250);

-- 5. Удаляем enum типы (после изменения всех полей)
DROP TYPE IF EXISTS subscription_template_type;
DROP TYPE IF EXISTS subscription_template_valid_time_type;
DROP TYPE IF EXISTS subscription_template_counter_type;

-- 6. Пересоздаем материализованные представления
CREATE MATERIALIZED VIEW subscription_template_mv AS
SELECT res.*
FROM (SELECT o.* FROM subscription_template o
      INNER JOIN (SELECT st_id, MAX(st_version) vers FROM subscription_template GROUP BY st_id) o2
                 ON o.st_id = o2.st_id AND o.st_version = o2.vers) res;
CREATE UNIQUE INDEX ON subscription_template_mv(st_id, st_version);

CREATE MATERIALIZED VIEW subscription_template_counter_mv AS
SELECT res.*
FROM (SELECT o.* FROM subscription_template_counter o
      INNER JOIN (SELECT stc_id, MAX(stc_version) vers FROM subscription_template_counter GROUP BY stc_id) o2
                 ON o.stc_id = o2.stc_id AND o.stc_version = o2.vers) res;
CREATE UNIQUE INDEX ON subscription_template_counter_mv(stc_id, stc_version);

-- 7. Пересоздаем функции и триггеры
CREATE FUNCTION refresh_subscription_template_mv()
    RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_template_mv;
    RETURN NULL;
END;
$$;

CREATE FUNCTION refresh_subscription_template_counter_mv()
    RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_template_counter_mv;
    RETURN NULL;
END;
$$;

CREATE TRIGGER trigger_refresh_subscription_template_mv
    AFTER INSERT OR UPDATE OR DELETE ON subscription_template
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_subscription_template_mv();

CREATE TRIGGER trigger_refresh_subscription_template_counter_mv
    AFTER INSERT OR UPDATE OR DELETE ON subscription_template_counter
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_subscription_template_counter_mv();

-- Комментарии для новых VARCHAR полей
COMMENT ON COLUMN subscription_template.st_type IS 'Тип билета (WALLET, TRAVEL, UNLIMITED)';
COMMENT ON COLUMN subscription_template.st_valid_time_type IS 'Тип срока действия (INTERVAL, DAYS, INTERVAL_AND_DAYS)';
COMMENT ON COLUMN subscription_template_counter.stc_counter_type IS 'Тип счетчика (ALL, ALLOW_LIST, SINGLE, ALL_UNLIMITED, ALLOW_LIST_UNLIMITED)';
COMMENT ON COLUMN subscription_base_counter.sbc_counter_type IS 'Тип счетчика (ALL, ALLOW_LIST, SINGLE, ALL_UNLIMITED, ALLOW_LIST_UNLIMITED)'; 