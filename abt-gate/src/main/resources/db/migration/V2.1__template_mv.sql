-- Subscription MV
CREATE MATERIALIZED VIEW subscription_template_mv AS
SELECT res.*
FROM (SELECT o.* FROM subscription_template o
      INNER JOIN (SELECT st_id, MAX(st_version) vers FROM subscription_template GROUP BY st_id) o2
                 ON o.st_id = o2.st_id AND o.st_version = o2.vers) res;
CREATE UNIQUE INDEX ON subscription_template_mv(st_id, st_version);


CREATE FUNCTION refresh_subscription_template_mv()
    RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_template_mv;
RETURN NULL;
END;
$$;

CREATE TRIGGER trigger_refresh_subscription_template_mv
    AFTER INSERT OR UPDATE OR DELETE ON subscription_template
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_subscription_template_mv();

-- Counters MV
CREATE MATERIALIZED VIEW subscription_template_counter_mv AS
SELECT res.*
FROM (SELECT o.* FROM subscription_template_counter o
      INNER JOIN (SELECT stc_id, MAX(stc_version) vers FROM subscription_template_counter GROUP BY stc_id) o2
                 ON o.stc_id = o2.stc_id AND o.stc_version = o2.vers) res;
CREATE UNIQUE INDEX ON subscription_template_counter_mv(stc_id, stc_version);


CREATE FUNCTION refresh_subscription_template_counter_mv()
    RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_template_counter_mv;
    RETURN NULL;
END;
$$;

CREATE TRIGGER trigger_refresh_subscription_template_counter_mv
    AFTER INSERT OR UPDATE OR DELETE ON subscription_template_counter
    FOR EACH STATEMENT
EXECUTE FUNCTION refresh_subscription_template_counter_mv();