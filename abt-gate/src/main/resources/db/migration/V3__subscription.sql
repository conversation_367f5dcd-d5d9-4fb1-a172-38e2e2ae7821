-- Создаем последовательность с начальным значением равным вашему минимальному
CREATE SEQUENCE subscription_abonement_id_seq
    START WITH 1000000000000000000
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    OWNED BY subscription.sub_abonement_id;

-- Абонементы
CREATE TABLE IF NOT EXISTS subscription (
    sub_id UUID PRIMARY KEY,
    sub_created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    sub_abonement_id BIGINT NOT NULL DEFAULT nextval('subscription_abonement_id_seq'),
    sub_crd_id UUID NOT NULL,
    sub_project_id UUID NOT NULL,
    sub_active_from TIMESTAMP WITH TIME ZONE NOT NULL,
    sub_active_till TIMESTAMP WITH TIME ZONE NOT NULL,
    sub_name VA<PERSON>HA<PERSON>(512) NOT NULL,
    sub_description VARCHAR(4096),
    sub_is_social BOOLEAN NOT NULL,
    sub_tt_id UUID NOT NULL,
    sub_tt_version INTEGER NOT NULL,
    sub_tags TEXT
);

COMMENT ON COLUMN subscription.sub_id IS 'Идентификатор абонемента';
COMMENT ON COLUMN subscription.sub_created_at IS 'Время формирования абонемента';
COMMENT ON COLUMN subscription.sub_crd_id IS 'Идентификатор карты';
COMMENT ON COLUMN subscription.sub_abonement_id IS 'Сквозной идентификатор абонемента';
COMMENT ON COLUMN subscription.sub_project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN subscription.sub_active_from IS 'Время начала действия абонемента';
COMMENT ON COLUMN subscription.sub_active_till IS 'Время окончания действия абонемента';
COMMENT ON COLUMN subscription.sub_name IS 'Название';
COMMENT ON COLUMN subscription.sub_description IS 'Расширенное описание';
COMMENT ON COLUMN subscription.sub_is_social IS 'Является социальным';
COMMENT ON COLUMN subscription.sub_tt_id IS 'Идентификатор шаблона';
COMMENT ON COLUMN subscription.sub_tt_version IS 'Версия шаблона';
COMMENT ON COLUMN subscription.sub_tags IS 'Теги';

-- Индексы для быстрого поиска
CREATE INDEX IF NOT EXISTS idx_subscription_crd_id ON subscription (sub_crd_id);
CREATE INDEX IF NOT EXISTS idx_subscription_tt_id ON subscription (sub_tt_id);
CREATE INDEX IF NOT EXISTS idx_subscription_abonement_id ON subscription (sub_abonement_id);
CREATE INDEX IF NOT EXISTS idx_subscription_project_id ON subscription (sub_project_id);

-- Счетчики абонементов
CREATE TABLE IF NOT EXISTS subscription_base_counter (
    sbc_id UUID PRIMARY KEY,
    sbc_created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    sbc_subscription_id UUID NOT NULL REFERENCES subscription(sub_id) ON DELETE CASCADE,
    sbc_counter_type subscription_template_counter_type NOT NULL,
    sbc_counter_value INTEGER NOT NULL,
    sbc_is_bus BOOLEAN NOT NULL,
    sbc_is_trolleybus BOOLEAN NOT NULL,
    sbc_is_tram BOOLEAN NOT NULL,
    sbc_is_metro BOOLEAN NOT NULL,
    sbc_project_id UUID
);

COMMENT ON COLUMN subscription_base_counter.sbc_id IS 'Идентификатор счетчика';
COMMENT ON COLUMN subscription_base_counter.sbc_created_at IS 'Время формирования билета';
COMMENT ON COLUMN subscription_base_counter.sbc_subscription_id IS 'Идентификатор абонемента';
COMMENT ON COLUMN subscription_base_counter.sbc_counter_type IS 'Тип счетчика';
COMMENT ON COLUMN subscription_base_counter.sbc_counter_value IS 'Начальное число поездок на счетчике (при покупке)';
COMMENT ON COLUMN subscription_base_counter.sbc_is_bus IS 'Признак автобуса';
COMMENT ON COLUMN subscription_base_counter.sbc_is_trolleybus IS 'Признак троллейбуса';
COMMENT ON COLUMN subscription_base_counter.sbc_is_tram IS 'Признак трамвая';
COMMENT ON COLUMN subscription_base_counter.sbc_is_metro IS 'Признак метро';
COMMENT ON COLUMN subscription_base_counter.sbc_project_id IS 'Идентификатор проекта';

-- Индекс для быстрого поиска счетчиков по абонементу
CREATE INDEX IF NOT EXISTS idx_counter_subscription_id ON subscription_base_counter (sbc_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscription_base_counter_project_id ON subscription_base_counter(sbc_project_id);

-- SubscriptionTemplateBinding
ALTER TABLE IF EXISTS subscription_template_binding ADD COLUMN IF NOT EXISTS stb_project_id UUID;
CREATE INDEX IF NOT EXISTS idx_subscription_template_binding_project_id ON subscription_template_binding(stb_project_id);

-- SubscriptionTemplateCounter
ALTER TABLE IF EXISTS subscription_template_counter ADD COLUMN IF NOT EXISTS stc_project_id UUID;
CREATE INDEX IF NOT EXISTS idx_subscription_template_counter_project_id ON subscription_template_counter(stc_project_id);

-- SubscriptionTemplateRule
ALTER TABLE IF EXISTS subscription_template_rule ADD COLUMN IF NOT EXISTS str_project_id UUID;
CREATE INDEX IF NOT EXISTS idx_subscription_template_rule_project_id ON subscription_template_rule(str_project_id); 