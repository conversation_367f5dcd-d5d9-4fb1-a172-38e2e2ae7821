create type project_function_status as enum ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

create table public.project_function
(
    pf_id                 uuid      default gen_random_uuid() not null,
    pf_version            integer   default 1                 not null,
    pf_version_created_at timestamp default now()             not null,
    pf_version_created_by uuid,
    pf_type               varchar(150)                        not null,
    pf_project_id         uuid                                not null,
    pf_active_from        timestamp default now()             not null,
    pf_active_till        timestamp,
    pf_status             project_function_status             not null,
    primary key (pf_id, pf_version)
);

insert into project_function (pf_type, pf_project_id, pf_status)
values ('ABT.PAYMENT_ABT_TICKET', '43db9de7-72ec-4eae-8978-8aef1c46873a', 'ACTIVE'::project_function_status);
insert into project_function (pf_type, pf_project_id, pf_status)
values ('ABT.PAYMENT_ABT_WALLET', '43db9de7-72ec-4eae-8978-8aef1c46873a', 'ACTIVE'::project_function_status);
insert into project_function (pf_type, pf_project_id, pf_status)
values ('ABT.PAYMENT_ABT_WALLET.FISCAL', '43db9de7-72ec-4eae-8978-8aef1c46873a', 'ACTIVE'::project_function_status);
insert into project_function (pf_type, pf_project_id, pf_status)
values ('ABT.PAYMENT_EMV_TICKET', '43db9de7-72ec-4eae-8978-8aef1c46873a', 'ACTIVE'::project_function_status);

create type subscription_template_type as enum ('WALLET', 'TRAVEL', 'UNLIMITED');
create type subscription_template_valid_time_type as enum ('INTERVAL', 'DAYS', 'INTERVAL_AND_DAYS');

-- Шаблон
create table subscription_template
(
    st_id                 uuid      default gen_random_uuid()   not null,
    st_version            integer   default 1                   not null,
    st_version_created_at timestamp default now()               not null,
    st_version_created_by uuid,

    st_project_id         uuid                                  not null,
    st_active_from        timestamp default now()               not null,
    st_active_till        timestamp,

    st_name               varchar(64)                           null,
    st_description        varchar(256)                          null,
    st_type               subscription_template_type            null,
    st_is_social          bool      default false               null,

    st_app_code           int                                   not null,
    st_crd_code           int                                   not null,

    st_valid_time_type    subscription_template_valid_time_type null,
    st_valid_time_start   timestamp                             null,
    st_valid_time_days    int       default 0                   null,
    st_valid_time_end     timestamp                             null,
    primary key (st_id, st_version)
);

comment on column subscription_template.st_name is 'Название';
comment on column subscription_template.st_description is 'Расширенное описание';
comment on column subscription_template.st_type is 'Тип билета (0 - Кошелек, 1 - Поездочный, 2 - Безлимитный)';
comment on column subscription_template.st_is_social is 'Является социальным';
comment on column subscription_template.st_app_code is 'Код приложения';
comment on column subscription_template.st_crd_code is 'Код билета';
comment on column subscription_template.st_valid_time_type is 'Тип срока действия (0 - С периодом на календарный месяц, 1 - Со сроком действия)';
comment on column subscription_template.st_valid_time_start is 'Начало действия';
comment on column subscription_template.st_valid_time_days is 'Период действия (дней)';
comment on column subscription_template.st_valid_time_end is 'Окончание действия';

create type subscription_template_counter_type as enum ('ALL', 'ALLOW_LIST', 'SINGLE', 'ALL_UNLIMITED', 'ALLOW_LIST_UNLIMITED');
-- ALL - Один счетчик на все виды транспорта
-- ALLOW_LIST - Один счетчик на все разрешенные виды транспорта
-- SINGLE - Для каждого перечисленного вида транспорта свой счетчик
-- ALL_UNLIMITED - Неограниченное число поездок
-- ALLOW_LIST_UNLIMITED - Неограниченное число поездок на все разрешенные виды транспорта

-- Шаблон счетчика
create table subscription_template_counter
(
    stc_id                       uuid      default gen_random_uuid() not null,
    stc_version                  integer   default 1                 not null,
    stc_version_created_at       timestamp default now()             not null,
    stc_version_created_by       uuid,

    stc_subscription_template_id uuid                                not null,

    stc_counter_type             subscription_template_counter_type,
    stc_counter_value            int,
    stc_is_bus                   boolean,
    stc_is_trolleybus            boolean,
    stc_is_tram                  boolean,
    stc_is_metro                 boolean,
    primary key (stc_id, stc_version)
);

-- Список правил списания за проезд
create table subscription_template_rule
(
    str_id                       uuid      default gen_random_uuid() not null,
    str_version                  integer   default 1                 not null,
    str_version_created_at       timestamp default now()             not null,
    str_version_created_by       uuid,

    str_subscription_template_id uuid                                not null,

    str_pass_index               int,
    str_pass_action              varchar(150)
);

-- Связка шаблона с типом карты в эмиссии
create table subscription_template_binding
(
    stb_id                       uuid      default gen_random_uuid() not null,
    stb_version                  integer   default 1                 not null,
    stb_version_created_at       timestamp default now()             not null,
    stb_version_created_by       uuid,

    stb_subscription_template_id uuid                                not null,

    stb_active_from              timestamp default now()             not null,
    stb_active_till              timestamp,

    stb_card_type_id             uuid                                not null,
    primary key (stb_id, stb_version)
);

comment on column subscription_template_binding.stb_card_type_id is 'Идентификатор типа карты в эмиссии';
