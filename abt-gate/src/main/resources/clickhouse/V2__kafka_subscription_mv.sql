CREATE TABLE kafka_subscription
(
    subscriptionId String,
    createdAt      String,
    crdId          String,
    projectId      String,
    activeFrom     String,
    activeTill     String,
    name           String,
    description    String,
    isSocial       BOOLEAN,
    ttId           String,
    ttVersion      UInt32,
    tags           Nullable(String)
)
    ENGINE = Kafka('**********:9092,***********:9092,**********:9092', 'ABT.SUBSCRIPTION', 'abt_subscription_loader',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE
MATERIALIZED VIEW subscription_mv TO subscription as
SELECT toUUID(subscriptionId)                     as subscription_id,
       parseDateTimeBestEffort(createdAt, 'UTC')  as created_at,
       toUUID(crdId)                              as crd_id,
       toUUID(projectId)                          as project_id,
       parseDateTimeBestEffort(activeFrom, 'UTC') as active_from,
       parseDateTimeBestEffort(activeTill, 'UTC') as active_till,
       name,
       description,
       isSocial                                   as is_social,
       ttId                                       as tt_id,
       ttVersion                                  as tt_version,
       tags
FROM kafka_subscription;


-- Counters
CREATE TABLE kafka_subscription_base_counter
(
    counterId      String,
    createdAt      String,
    subscriptionId String,
    counterType    String,
    counterValue   UInt32,
    isBus          BOOLEAN,
    isTrolleybus   BOOLEAN,
    isTram         BOOLEAN,
    isMetro        BOOLEAN
)
    ENGINE = Kafka('**********:9092,***********:9092,**********:9092', 'ABT.SUBSCRIPTION.COUNTER', 'abt_subscription_loader',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE MATERIALIZED VIEW subscription_base_counter_mv TO subscription_base_counter as
SELECT toUUID(counterId)                         as counter_id,
       toUUID(subscriptionId)                    as subscription_id,
       parseDateTimeBestEffort(createdAt, 'UTC') as created_at,
       counterType                               as counter_type,
       counterValue                              as counter_value,
       isBus                                     as is_bus,
       isTrolleybus                              as is_trolleybus,
       isTram                                    as is_tram,
       isMetro                                   as is_metro
FROM kafka_subscription_base_counter;