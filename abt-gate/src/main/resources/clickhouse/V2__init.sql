create table prod.subscription
(
    subscription_id UUID comment 'Идентификатор билета',
    created_at      DateTime('UTC') comment 'Время формирования билета',
    crd_id          UUID comment 'Идентификатор карты',
    project_id      UUID comment 'Идентификатор проекта',
    active_from     DateTime('UTC') comment 'Время начала действия абонемента',
    active_till     DateTime('UTC') comment 'Время окончания действия абонемента',

    name            var<PERSON><PERSON>(64) comment 'Название',
    description     varchar(256) comment 'Расширенное описание',
    is_social       boolean comment 'Является социальным',

    tt_id           UUID comment 'Идентификатор шаблона',
    tt_version      UInt16 comment 'Версия шаблона',

    tags            Nullable(String) comment 'Тэги'
) engine = MergeTree PARTITION BY toYYYYMM(created_at)
      PRIMARY KEY subscription_id
      ORDER BY subscription_id
      SETTINGS index_granularity = 8192;

create table prod.subscription_base_counter
(
    counter_id      UUID comment 'Ключ записи',
    created_at      DateTime('UTC') comment 'Время формирования билета',
    subscription_id UUID comment 'Идентификатор билета',

    counter_type    Enum8('ALL' = 0, 'ALLOW_LIST' = 1, 'SINGLE' = 2, 'ALL_UNLIMITED' = 3, 'ALLOW_LIST_UNLIMITED' = 4) comment 'Тип счетчика',
    counter_value   int comment 'Начальное число поездок на счетчике (при покупке)',

    is_bus          tinyint default 0 comment 'Автобус',
    is_trolleybus   tinyint default 0 comment 'Троллейбус',
    is_tram         tinyint default 0 comment 'Трамвай',
    is_metro        tinyint default 0 comment 'Метрополитен'
) engine = MergeTree PARTITION BY toYYYYMM(created_at)
        PRIMARY KEY counter_id
        ORDER BY counter_id
        SETTINGS index_granularity = 8192;

create table prod.subscription_sale_trx
(
    trx_id          UUID comment 'Идентификатор транзакции (заказа)',
    created_at      DateTime('UTC') comment 'Время формирования транзакции на терминале',

    crd_id          UUID comment 'Идентификатор карты',
    subscription_id UUID comment 'Идентификатор билета',
    agent_id        UUID comment 'Идентификатор агента',
    amount          UInt16 comment 'Сумма авторизации',

    tags            Nullable(String) comment 'Тэги'
) engine = MergeTree PARTITION BY toYYYYMM(created_at)
        PRIMARY KEY trx_id
        ORDER BY trx_id
        SETTINGS index_granularity = 8192;

create table prod.abt_trx
(
    trx_id          UUID comment 'Идентификатор транзакции (заказа)',
    created_at      DateTime('UTC') comment 'Время формирования транзакции на терминале',
    record_at       DateTime('UTC') comment 'Время формирования транзакции на сервере',

    crd_id          UUID comment 'Идентификатор карты',
    subscription_id UUID comment 'Идентификатор билета',
    terminal_serial String comment 'Заводской номер терминала',
    tid             String comment 'Идентификатор терминала',
    shift_num       UInt16 comment 'Номер смены на терминале',
    ern             UInt16 comment 'Единый номер операции на терминале (уникальный в рамках смены)',

    tags            Nullable(String) comment 'Тэги'
) engine = MergeTree PARTITION BY toYYYYMM(created_at)
        PRIMARY KEY trx_id
        ORDER BY trx_id
        SETTINGS index_granularity = 8192;