package ru.sbertroika.tkp3.abt.gateway.input

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import io.grpc.Metadata
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request

class KeycloakUtils {

    companion object {
        val keycloakClientId = "test-auth"
        val keycloakClientSecret = "MBsXGtoqZKZZUhrm31bOm1laAxPyJgzY"
        val keycloakHost = "https://dev-auth.sbertroika.tech"
        val keycloakRealm = "test-asop"
        val userName = "test-pasiv"
        val password = "q1w2e3r4++"

        private const val TOKEN_URL = "%s/realms/%s/protocol/openid-connect/token"
        private val client: OkHttpClient = OkHttpClient().newBuilder().build()
        private val objectMapper = ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

        fun getToken(): KeycloakToken? {
            val requestBody = FormBody.Builder()
                .add("client_id", keycloakClientId)
                .add("client_secret", keycloakClientSecret)
                .add("grant_type", "password")
                .add("scope", "profile")
                .add("username", userName)
                .add("password", password)
                .build()

            val request = Request.Builder()
                .url(String.format(TOKEN_URL, keycloakHost, keycloakRealm))
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .method("POST", requestBody)
                .build()

            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                return objectMapper.readValue(response.body?.string(), KeycloakToken::class.java)
            }

            return null
        }

        private val AUTHORIZATION_METADATA_KEY: Metadata.Key<String> = Metadata.Key.of("Authorization", Metadata.ASCII_STRING_MARSHALLER)
        private const val BEARER_TYPE = "Bearer"

        fun metadata(): Metadata {
            val headers = Metadata()
            headers.put(AUTHORIZATION_METADATA_KEY, String.format("%s %s", BEARER_TYPE, getToken()?.accessToken))
            return headers
        }
    }
}

data class KeycloakToken(
    @JsonProperty("access_token")
    var accessToken: String,

    @JsonProperty("expires_in")
    var expiresIn: Int,

    @JsonProperty("refresh_token")
    var refreshToken: String? = null,

    @JsonProperty("refresh_expires_in")
    var refreshExpiresIn: Int = 0
)