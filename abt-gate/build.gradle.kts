import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.springframework.boot.gradle.tasks.bundling.BootJar

plugins {
    idea
    kotlin("jvm")
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
}

group = "ru.sbertroika.abt"
version = rootProject.version

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(project(":abt-gate-api"))
    implementation(project(":abt-emission-api"))
    implementation(project(":abt-model"))

    implementation("ru.sbertroika.common:common-api:1.0.6")
    implementation("ru.sbertroika.common:common-manifest:1.0.6")
    implementation("io.github.lognet:grpc-spring-boot-starter:5.2.0") {
        exclude(group = "io.grpc", module = "grpc-netty-shaded")
    }

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-r2dbc")

    //Logging
    implementation("org.springframework.boot:spring-boot-starter-logging")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.8.1")
    implementation("org.zalando:logbook-spring-boot-webflux-autoconfigure:3.10.0")
    implementation("org.zalando:logbook-okhttp:3.10.0")

    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")

    //Kotlin-ext
    implementation("io.arrow-kt:arrow-core:1.0.1")

    //gRPC
    implementation("io.grpc:grpc-kotlin-stub:1.4.3")
    implementation("io.grpc:grpc-protobuf:1.71.0")
    implementation("io.grpc:grpc-stub:1.71.0")
    implementation("io.grpc:grpc-netty:1.71.0")
    implementation("com.google.protobuf:protobuf-kotlin:3.21.7")

    //okHttp
    implementation("com.squareup.okhttp3:okhttp:4.10.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.10.0")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.0")
    implementation("ru.gildor.coroutines:kotlin-coroutines-okhttp:1.0")

    // kafka
    implementation("org.springframework.kafka:spring-kafka:3.3.8")

    runtimeOnly("org.postgresql:r2dbc-postgresql")
    runtimeOnly("io.r2dbc:r2dbc-pool")
    implementation("io.r2dbc:r2dbc-postgresql:0.8.13.RELEASE")

    runtimeOnly("org.flywaydb:flyway-core:9.14.1")
    testImplementation("org.flywaydb:flyway-core:9.14.1")
    runtimeOnly("org.postgresql:postgresql:42.5.2")

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
    testImplementation("org.testcontainers:testcontainers:1.21.2")
    testImplementation("org.testcontainers:junit-jupiter:1.21.2")
    testImplementation("org.testcontainers:postgresql:1.21.2")
    testImplementation("org.jeasy:easy-random-core:5.0.0")
    testImplementation("io.grpc:grpc-okhttp:1.71.0")
//    testImplementation("io.grpc:grpc-core:1.43.0")
//    testImplementation("io.grpc:grpc-netty-shaded:1.43.0")

//    testImplementation("net.devh:grpc-client-spring-boot-starter:3.1.0.RELEASE") {
//        exclude(group = "io.grpc", module = "grpc-netty-shaded")
//    }
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test>().configureEach {
    val skipProvider = providers.gradleProperty("skipTest")
    if (!skipProvider.isPresent()) {
        useJUnitPlatform()
        testLogging {
            showStandardStreams = true
        }
    }
}

tasks.named<Jar>("jar") {
    enabled = false
}

tasks.named<BootJar>("bootJar") {
    enabled = true
}
