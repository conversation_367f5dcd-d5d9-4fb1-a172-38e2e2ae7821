package ru.sbertroika.abt.emission.input

import com.google.protobuf.Empty
import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.abt.emission.utils.clearNumber
import ru.sbertroika.abt.emission.v1.ABTEmissionServiceGrpcKt
import ru.sbertroika.abt.emission.v1.CardInfoRequest
import ru.sbertroika.abt.emission.v1.EmissionRequest
import ru.sbertroika.abt.emission.v1.MakeCardRequest
import java.util.concurrent.TimeUnit

class ABTEmissionServiceGrpcTests {

    companion object {
        private val server = "localhost:5008"
    }

    @Test
    fun testClearNumber() {
        Assertions.assertEquals("964310270330275397", "9643 102 7033 0275397".clearNumber())
        Assertions.assertEquals("964310270330275397", "9643- 10270-3302-75397".clearNumber())
    }

    @Test
    fun emission(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setRegion("47")
                .setNumber("9643102703300250150")
                .setUid("AD26B9D0")
                .build()
            val response = client.emission(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun testCardInfo(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val request = CardInfoRequest.newBuilder()
                .setCardId("e2b1a25d-e973-40f4-8cb2-34197a45c6b7")
                .build()
            val response = client.cardInfo(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.info)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun emissionByNumberOnly(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setRegion("47")
                .setNumber("9643102703300250150")
                .build()
            val response = client.emission(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun emissionInvalidNumber(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setRegion("47")
                .setNumber("96431-02703302-75397")
                .setUid("C2104c90")
                .build()
            val response = client.emission(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun cardInfo(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val request = CardInfoRequest.newBuilder()
                .setCardId("29d6619e-9033-4e39-a73a-21a446883263")
                .build()
            val response = client.cardInfo(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.info)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun cardTypes(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val response = client.cardTypes(Empty.getDefaultInstance())
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.types)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun makeCardNum(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val request = MakeCardRequest.newBuilder()
                .setNumber("12345678")
                .setCardTypeId("9563ef4b-1c79-4a8d-8413-f62316a8647b")
                .build()
            val response = client.makeCard(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.result)
            Assertions.assertEquals("3106470012345678", response.result.cardNum)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
}