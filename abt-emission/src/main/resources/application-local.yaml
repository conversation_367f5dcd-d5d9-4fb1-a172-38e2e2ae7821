spring:
  r2dbc:
    url: r2dbc:pool:${DB_URL:postgresql://localhost:5433/abt_emission}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${FLYWAY_DB_URL:postgresql://localhost:5433/abt_emission}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

emv_emission_url: ${EMV_EMISSION_URL:localhost:5009}

server:
  port: 8081

grpc:
  port: 5008