spring:
  application:
    name: abt-emission

  r2dbc:
    url: r2dbc:pool:${DB_URL:postgresql://postgres/abt_emission}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:false}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://postgres/abt_emission}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

grpc:
  port: 5000
server:
  port: 8080

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization
