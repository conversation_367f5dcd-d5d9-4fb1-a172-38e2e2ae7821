create table card
(
    id          uuid         not null
        constraint card_id_pk primary key default gen_random_uuid(),
    created_at  timestamp    not null     default current_timestamp,
    region      varchar(100) not null,
    type        uuid,
    card_number varchar(100),
    uid         varchar(100),
    CONSTRAINT number_and_region_uniq UNIQUE (region, card_number)
);

create index idx_card_number ON card USING btree (card_number);
create index idx_card_uid ON card USING btree (uid);
create index idx_card_number_and_region ON card USING btree (region, card_number);

create table card_type
(
    id          uuid         not null
        constraint card_type_pk primary key default gen_random_uuid(),
    created_at  timestamp    not null       default current_timestamp,
    region      varchar(100) not null,
    name        varchar(250) not null,
    img         varchar(500),
    preview     varchar(500),
    mask        varchar(50),
    prefix      varchar(50),
    description TEXT,
    patterns    TEXT
);
