package ru.sbertroika.abt.emission.converter

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.core.convert.converter.Converter
import org.springframework.data.convert.WritingConverter

@WritingConverter
class MapToStringConverter : Converter<Map<String, String>, String> {

    override fun convert(source: Map<String, String>): String? {
        return ObjectMapper().writeValueAsString(source)
    }
}