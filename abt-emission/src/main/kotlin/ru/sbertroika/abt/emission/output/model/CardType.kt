package ru.sbertroika.abt.emission.output.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("card_type")
data class CardType(
    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("region")
    var region: String? = null,

    @Column("project_id")
    var projectId: String? = null,

    @Column("name")
    var name: String? = null,

    @Column("mask")
    var mask: String? = null,

    @Column("img")
    var img: String? = null,

    @Column("preview")
    var preview: String? = null,

    @Column("prefix")
    var prefix: String? = null,

    @Column("description")
    var description: String? = null,

    @Column("patterns")
    var patterns: Map<String, String>? = null
)