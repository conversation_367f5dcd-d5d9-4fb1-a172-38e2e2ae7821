package ru.sbertroika.abt.emission.model

data class CardInfo(
    val id: String,
    val number: String,
    val uid: String?,
    val region: String,
    val type: CardType? = null,
    val projectId: String? = null
)

data class CardType(
    val id: String,
    val region: String,
    val name: String,
    val description: String? = null,
    val mask: String? = null,
    val prefix: String? = null,
    val img: String? = null,
    val preview: String? = null,
    val patterns: Map<String, String> = emptyMap(),
    val projectId: String? = null
)

data class MakeCard(
    val number: String,
    val region: String
)