package ru.sbertroika.abt.emission.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.abt.emission.output.model.ABTCard
import java.util.*

interface ABTCardRepository : CoroutineCrudRepository<ABTCard, UUID> {

    suspend fun findByRegionAndNumber(region: String, number: String): Flow<ABTCard>

    suspend fun findByUid(uid: String): Flow<ABTCard>

    fun findByProjectIdAndNumber(projectId: String, number: String): Flow<ABTCard>
}