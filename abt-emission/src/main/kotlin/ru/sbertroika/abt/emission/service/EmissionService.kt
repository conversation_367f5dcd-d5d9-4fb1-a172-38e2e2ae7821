package ru.sbertroika.abt.emission.service

import arrow.core.Either
import ru.sbertroika.abt.emission.model.CardInfo
import ru.sbertroika.abt.emission.model.CardType
import ru.sbertroika.abt.emission.model.MakeCard
import java.util.*

interface EmissionService {

    suspend fun emission(region: String, number: String?, uid: String?, type: String?, projectId: String?): Either<Error, UUID>

    suspend fun getCardInfo(id: String): Either<Error, CardInfo>

    suspend fun getCardTypes(): Either<Error, List<CardType>>

    suspend fun makeCardNum(number: String, typeId: String): Either<Error, MakeCard>
}