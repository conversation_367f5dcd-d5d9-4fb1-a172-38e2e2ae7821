package ru.sbertroika.abt.emission.output.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("card")
data class ABTCard(
    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("region")
    var region: String? = null,

    @Column("type")
    var type: UUID? = null,

    @Column("card_number")
    var number: String? = null,

    @Column("uid")
    var uid: String? = null,

    @Column("project_id")
    var projectId: UUID? = null
)
