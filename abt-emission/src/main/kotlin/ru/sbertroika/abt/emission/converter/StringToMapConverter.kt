package ru.sbertroika.abt.emission.converter

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.core.convert.converter.Converter
import org.springframework.data.convert.ReadingConverter

@ReadingConverter
class StringToMapConverter : Converter<String, Map<String, String>> {

    override fun convert(source: String): Map<String, String>? {
        return ObjectMapper().readValue(source) as Map<String, String>
    }
}