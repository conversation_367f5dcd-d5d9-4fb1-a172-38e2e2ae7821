package ru.sbertroika.abt.emission.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.r2dbc.convert.R2dbcCustomConversions
import org.springframework.data.r2dbc.dialect.PostgresDialect
import ru.sbertroika.abt.emission.converter.MapToStringConverter
import ru.sbertroika.abt.emission.converter.StringToMapConverter
import java.util.*

@Configuration
open class Converters {

    @Bean
    open fun customConversions(): R2dbcCustomConversions = R2dbcCustomConversions.of(
        PostgresDialect.INSTANCE,
        Arrays.asList(
            MapToStringConverter(),
            StringToMapConverter()
        )
    )
}