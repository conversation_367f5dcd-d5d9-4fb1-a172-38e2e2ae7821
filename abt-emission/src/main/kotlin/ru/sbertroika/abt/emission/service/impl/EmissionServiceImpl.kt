package ru.sbertroika.abt.emission.service.impl

import arrow.core.Either
import arrow.core.right
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.reactor.asFlux
import org.springframework.stereotype.Service
import ru.sbertroika.abt.emission.NotFound
import ru.sbertroika.abt.emission.model.MakeCard
import ru.sbertroika.abt.emission.output.model.ABTCard
import ru.sbertroika.abt.emission.output.model.CardType
import ru.sbertroika.abt.emission.output.repository.ABTCardRepository
import ru.sbertroika.abt.emission.output.repository.CardTypeRepository
import ru.sbertroika.abt.emission.service.EmissionService
import java.util.*

@Service
class EmissionServiceImpl(
    private val abtCardRepository: ABTCardRepository,
    private val cardTypeRepository: CardTypeRepository
) : EmissionService {

    override suspend fun emission(region: String, number: String?, uid: String?, type: String?, projectId: String?): Either<Error, UUID> {
        return try {
            var fType: CardType? = null
            val regionSelect = if (!type.isNullOrEmpty()) {
                fType = cardTypeRepository.findById(UUID.fromString(type))
                if (fType != null) {
                    fType.region
                } else region
            } else region

            val num = if (number.isNullOrEmpty()) null else {
                if (fType != null) {
                    if (fType.prefix.isNullOrEmpty()) number else fType.prefix!! + number
                } else {
                    number
                }
            }

            if (regionSelect.isNullOrEmpty()) {
                if (number?.isNotEmpty() == true && projectId?.isNotEmpty() == true) {
                    val card = abtCardRepository.findByProjectIdAndNumber(projectId, number).asFlux().awaitFirstOrNull()
                    if (card != null) {
                        return card.id!!.right()
                    }
                }

                return Either.Left(Error("region or type is required"))
            }

            if (!num.isNullOrEmpty() && !uid.isNullOrEmpty()) {
                return when (val res = emissionByNumberAndUid(regionSelect, num, uid, type, projectId)) {
                    is Either.Left -> Either.Left(Error("Error emission: regionSelect=$regionSelect, num=$num, uid=$uid, type=$type, projectId=$projectId"))
                    is Either.Right -> Either.Right(res.value)
                }
            }

            if (!num.isNullOrEmpty()) {
                return when (val res = emissionByNumber(regionSelect, num, type, projectId)) {
                    is Either.Left -> Either.Left(Error("Error emission: regionSelect=$regionSelect, num=$num, type=$type, projectId=$projectId"))
                    is Either.Right -> Either.Right(res.value)
                }
            }

            if (uid.isNullOrEmpty()) return Either.Left(Error())

            return when (val res = emissionByUid(regionSelect, uid, type, projectId)) {
                is Either.Left -> Either.Left(Error("Error emission: regionSelect=$regionSelect, uid=$uid, type=$type, projectId=$projectId"))
                is Either.Right -> Either.Right(res.value)
            }
        } catch (e: Exception) {
            Either.Left(Error())
        }
    }

    override suspend fun makeCardNum(number: String, typeId: String): Either<Error, MakeCard> {
        val type = cardTypeRepository.findById(UUID.fromString(typeId))
        if (type != null) {
            if (!type.prefix.isNullOrEmpty()) {
                return Either.Right(
                    MakeCard(
                        number = "${type.prefix}$number",
                        region = type.region!!
                    )
                )
            }
            return Either.Right(MakeCard(number, type.region!!))
        }
        return Either.Left(NotFound("card type not found"))
    }

    private suspend fun emissionByUid(region: String, uid: String, type: String?, projectId: String?): Either<Error, UUID> {
        return try {
            val res = abtCardRepository.findByUid(uid)
                .asFlux()
                .awaitFirstOrNull()

            if (res == null) {
                val newCard = abtCardRepository.save(
                    ABTCard(
                        region = region,
                        uid = uid,
                        type = if (type.isNullOrEmpty()) null else UUID.fromString(type),
                        projectId = UUID.fromString(projectId)
                    )
                )
                Either.Right(newCard.id!!)
            } else {
                if (res.type == null && !type.isNullOrEmpty()) {
                    abtCardRepository.save(
                        res.copy(type = UUID.fromString(type)).apply {
                            if (projectId != null) {
                                this.projectId = UUID.fromString(projectId)
                            }
                        }
                    )
                }
                Either.Right(res.id!!)
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    private suspend fun emissionByNumber(region: String, number: String, type: String?, projectId: String?): Either<Error, UUID> {
        return try {
            val res = abtCardRepository.findByRegionAndNumber(region, number)
                .asFlux()
                .awaitFirstOrNull()

            if (res == null) {
                val newCard = abtCardRepository.save(
                    ABTCard(
                        region = region,
                        number = number,
                        type = if (type.isNullOrEmpty()) null else UUID.fromString(type),
                        projectId = UUID.fromString(projectId)
                    )
                )
                Either.Right(newCard.id!!)
            } else {
                if (res.type == null && !type.isNullOrEmpty()) {
                    abtCardRepository.save(
                        res.copy(type = UUID.fromString(type)).apply {
                            if (projectId != null) {
                                this.projectId = UUID.fromString(projectId)
                            }
                        }
                    )
                }
                Either.Right(res.id!!)
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun getCardInfo(id: String): Either<Error, ru.sbertroika.abt.emission.model.CardInfo> = try {
        val res = abtCardRepository.findById(UUID.fromString(id))
        if (res != null) {
            if (res.type != null) {
                val type = cardTypeRepository.findById(res.type!!)
                if (type != null) {
                    Either.Right(toCardInfo(res, type))
                } else {
                    Either.Right(toCardInfo(res))
                }
            } else {
                Either.Right(toCardInfo(res))
            }
        } else {
            Either.Left(NotFound())
        }
    } catch (e: Exception) {
        Either.Left(Error(e))
    }

    //TODO Найти где используется и разделить, не дело что abt-emission знает что-то про emv-emission
//    override suspend fun getCardInfoAbtOrEmv(pan: String, projectId: String): Either<Throwable, CardInfo> = Either.catch {
//        if (pan.isEmpty()) throw Error("pan is empty")
//        val res = abtCardRepository.findByProjectIdAndNumber(projectId = projectId, number = pan).asFlux().awaitFirstOrNull()
//        if (res != null) {
//            getCardInfo(res)
//        } else {
//            val emvCardId = emvEmissionService.getCardId(pan)
//            if (emvCardId.isRight()) {
//                val card = abtCardRepository.findById(UUID.fromString(emvCardId.bind()))
//                if (card != null)
//                    return getCardInfo(card).right()
//            }
//            throw NotFound("not found by pan = $pan and projectId = $projectId")
//        }
//    }

    private suspend fun getCardInfo(res: ABTCard) =
        if (res.type != null) {
            val type = cardTypeRepository.findById(res.type!!)
            if (type != null) {
                toCardInfo(res, type)
            } else {
                toCardInfo(res)
            }
        } else {
            toCardInfo(res)
        }


    override suspend fun getCardTypes(): Either<Error, List<ru.sbertroika.abt.emission.model.CardType>> = try {
        Either.Right(cardTypeRepository.findAll().map(this::toCardType).toList())
    } catch (e: Exception) {
        Either.Left(Error(e))
    }

    private suspend fun emissionByNumberAndUid(region: String, number: String, uid: String, type: String?, projectId: String?): Either<Error, UUID> {
        return try {
            val res = abtCardRepository.findByRegionAndNumber(region, number)
                .asFlux()
                .awaitFirstOrNull()

            if (res == null) {
                val byUid = abtCardRepository.findByUid(uid)
                    .asFlux()
                    .awaitFirstOrNull()

                if (byUid == null) {
                    val newCard = abtCardRepository.save(
                        ABTCard(
                            region = region,
                            number = number,
                            uid = uid,
                            type = if (type.isNullOrEmpty()) null else UUID.fromString(type),
                            projectId = UUID.fromString(projectId)
                        )
                    )
                    return Either.Right(newCard.id!!)
                }


                abtCardRepository.save(
                    byUid.copy(
                        number = number,
                        type = if (type.isNullOrEmpty()) null else UUID.fromString(type)
                    ).apply {
                        if (projectId != null) {
                            this.projectId = UUID.fromString(projectId)
                        }
                    }
                )
                return Either.Right(byUid.id!!)
            }

            if (res.type == null && !type.isNullOrEmpty()) {
                abtCardRepository.save(
                    res.copy(type = UUID.fromString(type)).apply {
                        if (projectId != null) {
                            this.projectId = UUID.fromString(projectId)
                        }
                    }
                )
            }
            Either.Right(res.id!!)
        } catch (e: Exception) {
            Either.Left(Error())
        }
    }

    private fun toCardInfo(card: ABTCard, type: CardType? = null): ru.sbertroika.abt.emission.model.CardInfo {
        return ru.sbertroika.abt.emission.model.CardInfo(
            id = card.id.toString(),
            number = card.number!!,
            uid = card.uid,
            region = card.region!!,
            type = if (type != null) toCardType(type) else null,
            projectId = card.projectId.toString()
        )
    }

    private fun toCardType(type: CardType): ru.sbertroika.abt.emission.model.CardType {
        return ru.sbertroika.abt.emission.model.CardType(
            id = type.id.toString(),
            region = type.region!!,
            name = type.name!!,
            description = type.description,
            mask = type.mask,
            prefix = type.prefix,
            img = type.img,
            preview = type.preview,
            patterns = type.patterns ?: emptyMap(),
            projectId = type.projectId
        )
    }
}