package ru.sbertroika.abt.emission.input

import com.google.protobuf.Empty
import org.lognet.springboot.grpc.GRpcService
import org.slf4j.LoggerFactory
import ru.sbertroika.abt.emission.NotFound
import ru.sbertroika.abt.emission.service.EmissionService
import ru.sbertroika.abt.emission.utils.clearNumber
import ru.sbertroika.abt.emission.v1.*
import ru.sbertroika.common.v1.ErrorType
import ru.sbertroika.common.v1.OperationError

@GRpcService
class ABTEmissionServiceGrpc(
    private val emissionService: EmissionService
) : ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineImplBase() {

    private val log = LoggerFactory.getLogger(this::class.java)

    override suspend fun emission(request: EmissionRequest): EmissionResponse {
        if (request.number.isNullOrEmpty() && request.uid.isNullOrEmpty()) {
            EmissionResponse.newBuilder()
                .setError(
                    OperationError.newBuilder()
                        .setType(ErrorType.BAD_REQUEST)
                )
        }

        return emissionService.emission(request.region, request.number.clearNumber(), request.uid.uppercase(), request.type, request.projectId)
            .fold({
                log.error("Error emission", it)
                EmissionResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setType(ErrorType.SERVICE_ERROR)
                    )
            }, {
                EmissionResponse.newBuilder()
                    .setCardId(it.toString())
            }).build()
    }

    override suspend fun cardInfo(request: CardInfoRequest): CardInfoRequestResponse {
        return emissionService.getCardInfo(request.cardId)
            .fold({
                log.error("Error get cardInfo", it)
                CardInfoRequestResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder().setType(e(it))
                    )

            }, { info ->
                CardInfoRequestResponse.newBuilder()
                    .setInfo(toCardInfo(info))
            }).build()
    }

    override suspend fun cardTypes(request: Empty): CardTypesResponse {
        return emissionService.getCardTypes().fold(
            {
                log.error("Error get cardTypes", it)
                CardTypesResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder().setType(e(it))
                    )
            },
            { list ->
                CardTypesResponse.newBuilder()
                    .setTypes(
                        CardTypes.newBuilder()
                            .addAllList(list.map(this::toTypeItem).toList())
                    )
            }
        ).build()
    }

    override suspend fun makeCard(request: MakeCardRequest): MakeCardResponse {
        return emissionService.makeCardNum(request.number, request.cardTypeId).fold(
            {
                log.error("Error make CardNum", it)
                MakeCardResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setType(ErrorType.NOT_FOUND)
                            .setMessage(it.message)
                    )
            },
            { makeCard ->
                MakeCardResponse.newBuilder()
                    .setResult(
                        MakeCardResult.newBuilder()
                            .setCardNum(makeCard.number)
                            .setRegion(makeCard.region)
                    )
            }
        ).build()
    }

    private fun toCardInfo(card: ru.sbertroika.abt.emission.model.CardInfo): CardInfo {
        val c = CardInfo.newBuilder()
            .setId(card.id)
            .setRegion(card.region)
            .setNumber(card.number)
            .setUid(if (card.uid.isNullOrEmpty()) "" else card.uid)
        if (card.type != null) {
            c.type = toTypeItem(card.type)
        }
        if (card.projectId != null) {
            c.projectId = card.projectId
        }
        return c.build()
    }

    private fun toTypeItem(type: ru.sbertroika.abt.emission.model.CardType): CardType = CardType.newBuilder()
        .setId(type.id)
        .setRegion(type.region)
        .setName(type.name)
        .setPrefix(if (type.prefix.isNullOrEmpty()) "" else type.prefix)
        .setMask(if (type.mask.isNullOrEmpty()) "" else type.mask)
        .setDescription(if (type.description.isNullOrEmpty()) "" else type.description)
        .setImg(if (type.img.isNullOrEmpty()) "" else type.img)
        .setPreview(if (type.preview.isNullOrEmpty()) "" else type.preview)
        .putAllPatterns(type.patterns)
        .setProjectId(if (type.projectId.isNullOrEmpty()) "" else type.projectId)
        .build()

    private fun e(error: Error): ErrorType {
        return when (error) {
            is NotFound -> ErrorType.NOT_FOUND
            else -> ErrorType.SERVICE_ERROR
        }
    }
}