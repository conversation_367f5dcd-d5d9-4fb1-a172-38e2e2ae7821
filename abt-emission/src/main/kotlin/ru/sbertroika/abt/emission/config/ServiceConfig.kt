package ru.sbertroika.abt.emission.config

import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories

@Configuration
@ComponentScan(basePackages = ["ru.sbertroika.abt.emission"])
@EntityScan(basePackages = ["ru.sbertroika.abt.emission.model"])
@EnableR2dbcRepositories(basePackages = ["ru.sbertroika.abt.emission.output.repository"])
open class ServiceConfig