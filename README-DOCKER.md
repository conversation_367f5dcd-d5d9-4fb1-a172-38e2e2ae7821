# ABT Domain - Локальная разработка

## Требования

- Docker и Docker Compose
- Java 17+
- Gradle

## Быстрый старт

### 1. Запуск инфраструктуры

```bash
# Запуск только инфраструктуры (БД, Kafka, Kafka UI)
make docker-run-infra

# Или через docker-compose
docker-compose up -d abt_gate_db abt_emission_db zoo1 kafka kafka-ui
```

### 2. Сборка и запуск всех сервисов

```bash
# Сборка всех образов и запуск
make docker-build-and-run

# Или пошагово
make docker-build
make start
```

### 3. Локальная разработка из IDE

1. Запустите инфраструктуру:
```bash
make docker-run-infra
```

2. Запустите сервисы из IDE с профилем `local`:

#### ABT Gate
```bash
./gradlew :abt-gate:bootRun --args='--spring.profiles.active=local'
```

#### ABT Emission
```bash
./gradlew :abt-emission:bootRun --args='--spring.profiles.active=local'
```

#### ABT Processing
```bash
./gradlew :abt-processing:bootRun --args='--spring.profiles.active=local'
```

## Полезные команды

```bash
# Остановка всех сервисов
make stop

# Перезапуск сервисов
make restart

# Полная пересборка и перезапуск
make rebuild-and-restart

# Удаление контейнеров
make docker-rm

# Удаление всех контейнеров и образов
make docker-rm-all
```

## Порты сервисов

- **ABT Gate**: 
  - gRPC: 5010
  - HTTP: 8080
- **ABT Emission**: 
  - gRPC: 5008
  - HTTP: 8081
- **ABT Processing**: 
  - HTTP: 8082
- **Kafka UI**: 8086
- **ABT Gate DB**: 5432
- **ABT Emission DB**: 5433
- **Kafka**: 29092
- **Zookeeper**: 2181

## Схемы баз данных

- **abt_gate_db**: схема `abt` (используется abt-gate и abt-processing)
- **abt_emission_db**: схема `abt_emission` (используется только abt-emission)

## Переменные окружения

### ABT Gate (профиль local)
- `DB_URL`: postgresql://localhost:5432/abt
- `KAFKA_SERVERS`: localhost:29092
- `ABT_EMISSION_URL`: localhost:5008

### ABT Emission (профиль local)
- `DB_URL`: postgresql://localhost:5433/abt_emission
- `FLYWAY_DB_URL`: postgresql://localhost:5433/abt_emission

### ABT Processing (профиль local)
- `R2DB_URL`: postgresql://postgres:postgres@localhost:5432/abt
- `KAFKA_SERVERS`: localhost:29092
- `ABT_EMISSION_URL`: localhost:5008

## Структура данных

```
data/
├── abt_gate/          # Данные БД abt-gate
└── abt_emission/      # Данные БД abt-emission
```

## Troubleshooting

### Проблемы с подключением к БД
1. Убедитесь, что контейнеры БД запущены: `docker-compose ps`
2. Проверьте логи: `docker-compose logs abt_gate_db abt_emission_db`

### Проблемы с Kafka
1. Проверьте, что Zookeeper запущен: `docker-compose logs zoo1`
2. Проверьте логи Kafka: `docker-compose logs kafka`

### Проблемы с миграциями
1. Убедитесь, что переменная `DB_MIGRATION_ENABLE=true`
2. Проверьте логи сервисов на наличие ошибок миграции 