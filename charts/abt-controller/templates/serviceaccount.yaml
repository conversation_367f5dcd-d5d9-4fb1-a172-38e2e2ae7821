{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "abt-controller.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "abt-controller.fullname" .) }}
  labels:
    {{- include "abt-controller.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }} 