{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "abt-sbol-gate.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "abt-sbol-gate.fullname" .) }}
  labels:
    {{- include "abt-sbol-gate.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }} 