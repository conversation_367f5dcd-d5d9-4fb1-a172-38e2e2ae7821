apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "abt-gate.fullname" . }}
  namespace: {{ .Values.namespace | default (include "abt-gate.fullname" .) }}
  labels:
    {{- include "abt-gate.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "abt-gate.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "abt-gate.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "abt-gate.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-jar", "abt-gate.jar"]
          env:
            - name: CLIENT_LOGGING_ENABLE
              value: {{ ternary "true" "false" .Values.env.client.logging.enable | quote }}
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db
                  key: username
            - name: DB_R2URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: r2url
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: url
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db
                  key: password
            - name: DB_MIGRATION_ENABLE
              value: {{ ternary "true" "false" .Values.env.db.migration.enable | quote }}
            - name: KEYCLOAK_REALM_URL
              value: {{ .Values.env.keycloak.realm.url }}
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: grpc
              containerPort: {{ .Values.service.grpc.targetPort }}
              protocol: TCP
            - name: http
              containerPort: {{ .Values.service.http.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
