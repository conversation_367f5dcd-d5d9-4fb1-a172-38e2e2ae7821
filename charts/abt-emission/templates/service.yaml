apiVersion: v1
kind: Service
metadata:
  name: {{ include "abt-emission.fullname" . }}
  namespace: {{ .Values.namespace | default (include "abt-emission.fullname" .) }}
  labels:
    {{- include "abt-emission.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
      name: grpc
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "abt-emission.selectorLabels" . | nindent 4 }}
