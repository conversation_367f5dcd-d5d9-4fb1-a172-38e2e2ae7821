{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "abt-emission.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "abt-emission.fullname" .) }}
  labels:
    {{- include "abt-emission.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
