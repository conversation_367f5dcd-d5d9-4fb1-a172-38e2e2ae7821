{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "abt-processing.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "abt-processing.fullname" .) }}
  labels:
    {{- include "abt-processing.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }} 