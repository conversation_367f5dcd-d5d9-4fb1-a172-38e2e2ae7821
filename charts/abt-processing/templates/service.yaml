apiVersion: v1
kind: Service
metadata:
  name: {{ include "abt-processing.fullname" . }}
  namespace: {{ .Values.namespace | default (include "abt-processing.fullname" .) }}
  labels:
    {{- include "abt-processing.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "abt-processing.selectorLabels" . | nindent 4 }} 