apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "abt-processing.fullname" . }}
  namespace: {{ .Values.namespace | default (include "abt-processing.fullname" .) }}
  labels:
    {{- include "abt-processing.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "abt-processing.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "abt-processing.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "abt-processing.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-jar", "abt-processing.jar"]
          env:
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db
                  key: username
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: url
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db
                  key: password
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers }}
            - name: ABT_IN_TOPIC
              value: {{ .Values.env.kafka.abt_in_topic }}
            - name: ABT_TRX_IN_TOPIC
              value: {{ .Values.env.kafka.abt_trx_in_topic }}
            - name: ABT_OPERATION_OUT_TOPIC
              value: {{ .Values.env.kafka.abt_operation_out_topic }}
            - name: ABT_ERROR_OUT_TOPIC
              value: {{ .Values.env.kafka.abt_error_out_topic }}
            - name: ABT_PROCESSING_IN_GROUP
              value: {{ .Values.env.kafka.abt_processing_in_group }}
            - name: ABT_BATCH_PROCESSING_IN_GROUP
              value: {{ .Values.env.kafka.abt_batch_processing_in_group }}
            - name: ABT_EMISSION_URL
              value: {{ .Values.env.service.abt_emission_url }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.http.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }} 