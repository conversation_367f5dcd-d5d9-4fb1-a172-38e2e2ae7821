# Default values for abt-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: abt-processing
namespace: ""

replicaCount: 1

image:
  repository: abt-processing
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  kafka:
    servers: "**********:9092;***********:9092;**********:9092"
    abt_in_topic: "PRO.INTERNAL.ABT"
    abt_trx_in_topic: "ABT.TRX"
    abt_operation_out_topic: "ABT.OPERATION.OUT"
    abt_error_out_topic: "ABT.ERROR.OUT"
    abt_processing_in_group: "abt_processing_in_group"
    abt_batch_processing_in_group: "abt_batch_processing_in_group"
  service:
    abt_emission_url: "abt-emission.abt.svc.cluster.local:5000" 