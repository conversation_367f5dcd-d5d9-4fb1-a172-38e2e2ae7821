{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "abt-sbol-controller.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "abt-sbol-controller.fullname" .) }}
  labels:
    {{- include "abt-sbol-controller.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }} 