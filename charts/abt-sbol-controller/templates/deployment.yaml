apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "abt-sbol-controller.fullname" . }}
  namespace: {{ .Values.namespace | default (include "abt-sbol-controller.fullname" .) }}
  labels:
    {{- include "abt-sbol-controller.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "abt-sbol-controller.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "abt-sbol-controller.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "abt-sbol-controller.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-jar", "abt-sbol-controller.jar"]
          env:
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-sbol
                  key: username
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: db-sbol
                  key: url
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-sbol
                  key: password
            - name: DB_MIGRATION_ENABLE
              value: {{ ternary "true" "false" .Values.env.db.migration.enable | quote }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.http.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }} 