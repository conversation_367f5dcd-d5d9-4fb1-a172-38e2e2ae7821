apiVersion: v1
kind: Service
metadata:
  name: {{ include "abt-ui.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "abt-ui.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
  selector:
    {{- include "abt-ui.selectorLabels" . | nindent 4 }}
