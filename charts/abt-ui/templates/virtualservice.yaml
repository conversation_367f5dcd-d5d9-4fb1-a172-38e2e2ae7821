kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: abt-ui
  namespace: abt
spec:
  hosts:
    - crm-abt-dev.sbertroika.tech
  gateways:
    - istio-ingressgateway/abt-ui-gateway
  http:
    - match:
        - uri:
            prefix: /api/v1/abt/sbol/
      route:
        - destination:
            host: abt-abol-controller.abt.svc.cluster.local
            port:
              number: 8080
      rewrite:
        uri: /api/v1/abt/sbol/
    - match:
        - uri:
            prefix: /api/v1/abt/
      route:
        - destination:
            host: abt-controller.abt.svc.cluster.local
            port:
              number: 8080
      rewrite:
        uri: /api/v1/abt/
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: abt-ui.abt.svc.cluster.local
            port:
              number: 80
status: {}
