{{- if .Values.virtualService.enabled }}
kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: {{ include "abt-ui.fullname" . }}
  namespace: {{ .Release.Namespace }}
spec:
  hosts:
    - {{ .Values.virtualService.host }}
  gateways:
    - {{ .Values.virtualService.gateway }}
  http:
    - match:
        - uri:
            prefix: {{ .Values.virtualService.apiPrefix | default "/api/" }}
      route:
        - destination:
            host: {{ .Values.virtualService.apiDestination.host }}
            port:
              number: {{ .Values.virtualService.apiDestination.port }}
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: {{ .Values.virtualService.destination.host }}
            port:
              number: {{ .Values.virtualService.destination.port }}
{{- end }}
