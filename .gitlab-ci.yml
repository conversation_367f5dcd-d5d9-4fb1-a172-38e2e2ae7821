# GitLab CI/CD Pipeline для abt-domain (shell executor + docker run)

include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/main-pipeline-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates
  - local: '/abt-controller/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/abt-emission/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/abt-gate/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/abt-processing/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/abt-sbol-controller/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/abt-sbol-gate/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/abt-ui/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always

variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  DOCKER_BASE_IMAGE: "gradle:8.5-jdk17"
  KUBE_NAMESPACE: "abt"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - .gradle/daemon

stages:
  - publish-snapshot
  - build
  - test
  - deploy
  - release

.docker_gradle: &docker_gradle
  before_script:
    - echo "Docker base image $DOCKER_BASE_IMAGE"
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh

# ========================
# 1. ЛЮБАЯ ВЕТКА (кроме develop/master): build + test
# ========================
build_and_test:
  <<: *docker_gradle
  stage: test
  extends:
    - .gradle_build_new_template
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME != "develop" && $CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_TAG == null

# ========================
# 2. DEVELOP: build + test + publish SNAPSHOT (одним шагом)
# ========================

develop_build_test_publish:
  <<: *docker_gradle
  stage: publish-snapshot
  extends:
    - .gradle_build_test_publish_template
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  environment:
    name: develop
    url: https://nexus.sbertroika.tech/repository/maven-snapshots/

# ========================
# 3. MASTER: только release (без отдельного build/test)
# ========================

release:
  <<: *docker_gradle
  stage: release
  extends:
    - .gradle_release_template