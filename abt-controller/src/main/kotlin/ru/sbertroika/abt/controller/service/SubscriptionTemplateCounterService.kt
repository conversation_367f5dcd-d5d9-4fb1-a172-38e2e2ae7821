package ru.sbertroika.abt.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.input.model.SubscriptionTemplateCounterDto
import ru.sbertroika.abt.controller.repository.SubscriptionTemplateCounterRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateCounter
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Service
class SubscriptionTemplateCounterService(
    private val counterRepository: SubscriptionTemplateCounterRepository
) {

    /**
     * Получить счетчики шаблона по ID шаблона
     */
    fun getCountersByTemplateId(templateId: UUID): Flux<SubscriptionTemplateCounter> {
        return counterRepository.findBySubscriptionTemplateId(templateId)
    }

    /**
     * Создать счетчик для шаблона
     */
    fun createCounter(counterDto: SubscriptionTemplateCounterDto): Mono<SubscriptionTemplateCounter> {
        val id = UUID.randomUUID()
        val versionCreatedAt = Timestamp.valueOf(LocalDateTime.now())
        
        return counterRepository.insertCounter(
            id = id,
            version = 1,
            versionCreatedAt = versionCreatedAt,
            versionCreatedBy = null,
            projectId = UUID.fromString("43db9de7-72ec-4eae-8978-8aef1c46873a"),
            subscriptionTemplateId = counterDto.subscriptionTemplateId,
            type = counterDto.type,
            value = counterDto.value,
            isBus = counterDto.isBus,
            isTrolleybus = counterDto.isTrolleybus,
            isTram = counterDto.isTram,
            isMetro = counterDto.isMetro
        ).then(
            counterRepository.findById(id)
        ).switchIfEmpty(
            Mono.error<SubscriptionTemplateCounter>(RuntimeException("Не удалось создать счетчик"))
        )
    }

    /**
     * Обновить счетчик
     */
    fun updateCounter(id: UUID, counterDto: SubscriptionTemplateCounterDto): Mono<SubscriptionTemplateCounter> {
        return counterRepository.findById(id)
            .flatMap { existingCounter ->
                val updatedCounter = existingCounter.copy(
                    type = counterDto.type,
                    value = counterDto.value,
                    isBus = counterDto.isBus,
                    isTrolleybus = counterDto.isTrolleybus,
                    isTram = counterDto.isTram,
                    isMetro = counterDto.isMetro,
                    version = (existingCounter.version ?: 0) + 1,
                    versionCreatedAt = Timestamp.valueOf(LocalDateTime.now()),
                    versionCreatedBy = null
                )
                
                counterRepository.save(updatedCounter)
            }
            .switchIfEmpty(Mono.error(RuntimeException("Счетчик с id $id не найден")))
    }

    /**
     * Удалить счетчик
     */
    fun deleteCounter(id: UUID): Mono<Boolean> {
        return counterRepository.findById(id)
            .flatMap { counter ->
                counterRepository.delete(counter)
                    .then(Mono.just(true))
            }
            .switchIfEmpty(Mono.just(false))
    }

    /**
     * Удалить все счетчики шаблона
     */
    fun deleteCountersByTemplateId(templateId: UUID): Mono<Void> {
        return counterRepository.deleteBySubscriptionTemplateId(templateId)
    }

    /**
     * Создать несколько счетчиков для шаблона
     */
    fun createCountersForTemplate(templateId: UUID, counters: List<SubscriptionTemplateCounterDto>): Flux<SubscriptionTemplateCounter> {
        return Flux.fromIterable(counters)
            .flatMap { counterDto ->
                val counterWithTemplateId = counterDto.copy(subscriptionTemplateId = templateId)
                createCounter(counterWithTemplateId)
            }
    }
} 