package ru.sbertroika.abt.controller.input

import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.input.model.SubscriptionTemplateCounterDto
import ru.sbertroika.abt.controller.output.model.ApiResponse
import ru.sbertroika.abt.controller.service.SubscriptionTemplateCounterService
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateCounter
import java.util.*

@RestController
@RequestMapping("/api/v1/abt/subscription-template-counters")
class SubscriptionTemplateCounterController(
    private val counterService: SubscriptionTemplateCounterService
) {

    @GetMapping("/template/{templateId}")
    fun getCountersByTemplateId(@PathVariable templateId: UUID): Mono<ApiResponse<List<SubscriptionTemplateCounter>>> {
        return counterService.getCountersByTemplateId(templateId)
            .collectList()
            .map { counters ->
                ApiResponse.success(counters)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplateCounter>>("Ошибка получения счетчиков: ${error.message}"))
            }
    }

    @PostMapping
    fun createCounter(@Valid @RequestBody counterDto: SubscriptionTemplateCounterDto): Mono<ApiResponse<SubscriptionTemplateCounter>> {
        return counterService.createCounter(counterDto)
            .map { counter ->
                ApiResponse.success(counter, "Счетчик успешно создан")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplateCounter>("Ошибка создания счетчика: ${error.message}"))
            }
    }

    @PutMapping("/{id}")
    fun updateCounter(
        @PathVariable id: UUID,
        @Valid @RequestBody counterDto: SubscriptionTemplateCounterDto
    ): Mono<ApiResponse<SubscriptionTemplateCounter>> {
        return counterService.updateCounter(id, counterDto)
            .map { counter ->
                ApiResponse.success(counter, "Счетчик успешно обновлен")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplateCounter>("Ошибка обновления счетчика: ${error.message}"))
            }
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteCounter(@PathVariable id: UUID): Mono<ApiResponse<Boolean>> {
        return counterService.deleteCounter(id)
            .map { success ->
                if (success) {
                    ApiResponse.success(true, "Счетчик успешно удален")
                } else {
                    ApiResponse.error<Boolean>("Счетчик не найден")
                }
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Boolean>("Ошибка удаления счетчика: ${error.message}"))
            }
    }

    @DeleteMapping("/template/{templateId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteCountersByTemplateId(@PathVariable templateId: UUID): Mono<ApiResponse<Unit>> {
        return counterService.deleteCountersByTemplateId(templateId)
            .then(Mono.just(ApiResponse.success(Unit, "Все счетчики шаблона удалены")))
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Unit>("Ошибка удаления счетчиков: ${error.message}"))
            }
    }

    @PostMapping("/template/{templateId}/batch")
    fun createCountersForTemplate(
        @PathVariable templateId: UUID,
        @Valid @RequestBody counters: List<SubscriptionTemplateCounterDto>
    ): Mono<ApiResponse<List<SubscriptionTemplateCounter>>> {
        return counterService.createCountersForTemplate(templateId, counters)
            .collectList()
            .map { createdCounters ->
                ApiResponse.success(createdCounters, "Счетчики успешно созданы")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplateCounter>>("Ошибка создания счетчиков: ${error.message}"))
            }
    }
} 