package ru.sbertroika.abt.controller.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateCounter
import java.util.UUID

@Repository
interface SubscriptionTemplateCounterRepository : ReactiveCrudRepository<SubscriptionTemplateCounter, UUID> {

    @Query("SELECT * FROM subscription_template_counter WHERE stc_subscription_template_id = :templateId ORDER BY stc_counter_type")
    fun findBySubscriptionTemplateId(templateId: UUID): Flux<SubscriptionTemplateCounter>

    @Query("""
        INSERT INTO subscription_template_counter (
            stc_id, stc_version, stc_version_created_at, stc_version_created_by, stc_project_id,
            stc_subscription_template_id, stc_counter_type, stc_counter_value,
            stc_is_bus, stc_is_trolleybus, stc_is_tram, stc_is_metro
        ) VALUES (
            :id, :version, :versionCreatedAt, :versionCreatedBy, :projectId,
            :subscriptionTemplateId, :type, :value,
            :isBus, :isTrolleybus, :isTram, :isMetro
        )
    """)
    fun insertCounter(
        id: UUID,
        version: Int,
        versionCreatedAt: java.sql.Timestamp,
        versionCreatedBy: UUID?,
        projectId: UUID?,
        subscriptionTemplateId: UUID,
        type: String,
        value: Int,
        isBus: Boolean,
        isTrolleybus: Boolean,
        isTram: Boolean,
        isMetro: Boolean
    ): Mono<Void>

    @Query("DELETE FROM subscription_template_counter WHERE stc_subscription_template_id = :templateId")
    fun deleteBySubscriptionTemplateId(templateId: UUID): Mono<Void>
} 