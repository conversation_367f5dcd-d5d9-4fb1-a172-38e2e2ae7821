package ru.sbertroika.abt.controller.input

import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.input.model.SubscriptionTemplateDto
import ru.sbertroika.abt.controller.output.model.ApiResponse
import ru.sbertroika.abt.controller.service.SubscriptionTemplateService
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplate
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateType
import java.util.*

@RestController
@RequestMapping("/api/v1/abt/subscription-templates")
class SubscriptionTemplateController(
    private val subscriptionTemplateService: SubscriptionTemplateService
) {

    @PostMapping
    fun createTemplate(@Valid @RequestBody templateDto: SubscriptionTemplateDto): Mono<ApiResponse<SubscriptionTemplate>> {
        return subscriptionTemplateService.createTemplate(templateDto)
            .map { template ->
                ApiResponse.success(template, "Шаблон абонемента успешно создан")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplate>("Ошибка создания шаблона: ${error.message}"))
            }
    }

    @PutMapping("/{id}")
    fun updateTemplate(
        @PathVariable id: UUID,
        @Valid @RequestBody templateDto: SubscriptionTemplateDto
    ): Mono<ApiResponse<SubscriptionTemplate>> {
        return subscriptionTemplateService.updateTemplate(id, templateDto)
            .map { template ->
                ApiResponse.success(template, "Шаблон абонемента успешно обновлен")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplate>("Ошибка обновления шаблона: ${error.message}"))
            }
    }

    @GetMapping("/{id}")
    fun getTemplateById(@PathVariable id: UUID): Mono<ApiResponse<SubscriptionTemplate>> {
        return subscriptionTemplateService.getTemplateById(id)
            .map { template ->
                ApiResponse.success(template)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplate>("Шаблон с id $id не найден"))
            }
    }

    @GetMapping
    fun getAllTemplates(): Mono<ApiResponse<List<SubscriptionTemplate>>> {
        return subscriptionTemplateService.getAllTemplates()
            .collectList()
            .map { templates ->
                ApiResponse.success(templates)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplate>>("Ошибка получения шаблонов: ${error.message}"))
            }
    }

    @GetMapping("/type/{type}")
    fun getTemplatesByType(@PathVariable type: SubscriptionTemplateType): Mono<ApiResponse<List<SubscriptionTemplate>>> {
        return subscriptionTemplateService.getTemplatesByType(type)
            .collectList()
            .map { templates ->
                ApiResponse.success(templates)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplate>>("Ошибка получения шаблонов по типу: ${error.message}"))
            }
    }

    @GetMapping("/social/{isSocial}")
    fun getTemplatesBySocialStatus(@PathVariable isSocial: Boolean): Mono<ApiResponse<List<SubscriptionTemplate>>> {
        return subscriptionTemplateService.getTemplatesBySocialStatus(isSocial)
            .collectList()
            .map { templates ->
                ApiResponse.success(templates)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplate>>("Ошибка получения шаблонов по социальному статусу: ${error.message}"))
            }
    }

    @GetMapping("/active")
    fun getActiveTemplates(): Mono<ApiResponse<List<SubscriptionTemplate>>> {
        return subscriptionTemplateService.getActiveTemplates()
            .collectList()
            .map { templates ->
                ApiResponse.success(templates)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplate>>("Ошибка получения активных шаблонов: ${error.message}"))
            }
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteTemplate(@PathVariable id: UUID): Mono<ApiResponse<Boolean>> {
        return subscriptionTemplateService.deleteTemplate(id)
            .map { success ->
                if (success) {
                    ApiResponse.success(true, "Шаблон успешно удален")
                } else {
                    ApiResponse.error<Boolean>("Ошибка удаления шаблона")
                }
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Boolean>("Ошибка удаления шаблона: ${error.message}"))
            }
    }
} 