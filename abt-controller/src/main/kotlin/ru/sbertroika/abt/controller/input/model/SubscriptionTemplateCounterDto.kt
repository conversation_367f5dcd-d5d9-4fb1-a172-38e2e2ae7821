package ru.sbertroika.abt.controller.input.model

import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID

data class SubscriptionTemplateCounterDto(
    @field:NotNull(message = "ID шаблона обязателен")
    val subscriptionTemplateId: UUID,
    
    @field:NotNull(message = "Тип счетчика обязателен")
    val type: String,
    
    @field:PositiveOrZero(message = "Значение счетчика должно быть неотрицательным")
    val value: Int,
    
    val isBus: Boolean = false,
    val isTrolleybus: Boolean = false,
    val isTram: Boolean = false,
    val isMetro: Boolean = false
) 