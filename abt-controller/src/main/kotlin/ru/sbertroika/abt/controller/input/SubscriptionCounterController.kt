package ru.sbertroika.abt.controller.input

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.output.model.ApiResponse
import ru.sbertroika.abt.controller.service.SubscriptionCounterService
import ru.sbertroika.tkp3.abt.model.SubscriptionBaseCounter
import java.util.*

@RestController
@RequestMapping("/api/v1/abt/subscription-counters")
class SubscriptionCounterController(
    private val subscriptionCounterService: SubscriptionCounterService
) {

    @GetMapping("/subscription/{subscriptionId}")
    fun getCountersBySubscription(@PathVariable subscriptionId: UUID): Mono<ApiResponse<List<SubscriptionBaseCounter>>> {
        return subscriptionCounterService.getCountersBySubscription(subscriptionId)
            .collectList()
            .map { counters ->
                ApiResponse.success(counters)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionBaseCounter>>("Ошибка получения счетчиков: ${error.message}"))
            }
    }

    @GetMapping("/{id}")
    fun getCounterById(@PathVariable id: UUID): Mono<ApiResponse<SubscriptionBaseCounter>> {
        return subscriptionCounterService.getCounterById(id)
            .map { counter ->
                ApiResponse.success(counter)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionBaseCounter>("Счетчик с id $id не найден"))
            }
    }

    @GetMapping("/project/{projectId}")
    fun getCountersByProject(@PathVariable projectId: UUID): Mono<ApiResponse<List<SubscriptionBaseCounter>>> {
        return subscriptionCounterService.getCountersByProject(projectId)
            .collectList()
            .map { counters ->
                ApiResponse.success(counters)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionBaseCounter>>("Ошибка получения счетчиков по проекту: ${error.message}"))
            }
    }
} 