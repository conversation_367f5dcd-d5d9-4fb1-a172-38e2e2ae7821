package ru.sbertroika.abt.controller.input.model

import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import java.util.UUID

data class SubscriptionTemplateRuleDto(
    @field:NotNull(message = "ID шаблона обязателен")
    val subscriptionTemplateId: UUID,
    
    @field:NotNull(message = "Индекс прохода обязателен")
    @field:Positive(message = "Индекс прохода должен быть положительным")
    val passIndex: Int,
    
    @field:NotNull(message = "Действие прохода обязательно")
    val passAction: String
) 