package ru.sbertroika.abt.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.repository.SubscriptionCounterRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionBaseCounter
import java.util.UUID

@Service
class SubscriptionCounterService(
    private val subscriptionCounterRepository: SubscriptionCounterRepository
) {

    /**
     * Получить счетчики по абонементу
     */
    fun getCountersBySubscription(subscriptionId: UUID): Flux<SubscriptionBaseCounter> {
        return subscriptionCounterRepository.findBySubscriptionId(subscriptionId)
    }

    /**
     * Получить счетчик по ID
     */
    fun getCounterById(id: UUID): Mono<SubscriptionBaseCounter> {
        return subscriptionCounterRepository.findById(id)
            .switchIfEmpty(Mono.error(RuntimeException("Счетчик с id $id не найден")))
    }

    /**
     * Получить счетчики по проекту
     */
    fun getCountersByProject(projectId: UUID): Flux<SubscriptionBaseCounter> {
        return subscriptionCounterRepository.findByProjectId(projectId)
    }
} 