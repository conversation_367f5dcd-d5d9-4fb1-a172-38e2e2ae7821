package ru.sbertroika.abt.controller.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateRule
import java.util.UUID

@Repository
interface SubscriptionTemplateRuleRepository : ReactiveCrudRepository<SubscriptionTemplateRule, UUID> {

    @Query("SELECT * FROM subscription_template_rule WHERE str_subscription_template_id = :templateId ORDER BY str_pass_index")
    fun findBySubscriptionTemplateId(templateId: UUID): Flux<SubscriptionTemplateRule>

    @Query("""
        INSERT INTO subscription_template_rule (
            str_id, str_version, str_version_created_at, str_version_created_by, str_project_id,
            str_subscription_template_id, str_pass_index, str_pass_action
        ) VALUES (
            :id, :version, :versionCreatedAt, :versionCreatedBy, :projectId,
            :subscriptionTemplateId, :passIndex, :passAction
        )
    """)
    fun insertRule(
        id: UUID,
        version: Int,
        versionCreatedAt: java.sql.Timestamp,
        versionCreatedBy: UUID?,
        projectId: UUID?,
        subscriptionTemplateId: UUID,
        passIndex: Int,
        passAction: String
    ): Mono<Void>

    @Query("DELETE FROM subscription_template_rule WHERE str_subscription_template_id = :templateId")
    fun deleteBySubscriptionTemplateId(templateId: UUID): Mono<Void>
} 