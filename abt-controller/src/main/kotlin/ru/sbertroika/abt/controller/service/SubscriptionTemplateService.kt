package ru.sbertroika.abt.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.input.model.SubscriptionTemplateDto
import ru.sbertroika.abt.controller.repository.SubscriptionTemplateRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplate
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateType
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateValidTimeType
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Service
class SubscriptionTemplateService(
    private val subscriptionTemplateRepository: SubscriptionTemplateRepository
) {

    /**
     * Создать новый шаблон абонемента
     */
    fun createTemplate(templateDto: SubscriptionTemplateDto): Mono<SubscriptionTemplate> {
        val id = UUID.randomUUID()
        val versionCreatedAt = Timestamp.valueOf(LocalDateTime.now())
        
        return subscriptionTemplateRepository.insertTemplate(
            id = id,
            version = 1,
            versionCreatedAt = versionCreatedAt,
            versionCreatedBy = null,
            projectId = UUID.fromString("43db9de7-72ec-4eae-8978-8aef1c46873a"),
            activeFrom = Timestamp.valueOf(templateDto.activeFrom),
            activeTill = Timestamp.valueOf(templateDto.activeTill),
            stName = templateDto.stName,
            description = templateDto.description,
            type = templateDto.type.name,
            appCode = templateDto.appCode,
            crdCode = templateDto.crdCode,
            isSocial = templateDto.isSocial,
            validTimeType = templateDto.validTimeType.name,
            validTimeStart = templateDto.validTimeStart?.let { Timestamp.valueOf(it) },
            validTimeDays = templateDto.validTimeDays,
            validTimeEnd = templateDto.validTimeEnd?.let { Timestamp.valueOf(it) }
        ).then(
            subscriptionTemplateRepository.findById(id)
        ).switchIfEmpty(
            Mono.error<SubscriptionTemplate>(RuntimeException("Не удалось создать шаблон"))
        )
    }

    /**
     * Обновить шаблон абонемента
     */
    fun updateTemplate(id: UUID, templateDto: SubscriptionTemplateDto): Mono<SubscriptionTemplate> {
        return subscriptionTemplateRepository.findById(id)
            .flatMap { existingTemplate ->
                val updatedTemplate = existingTemplate.copy(
                    stName = templateDto.stName,
                    description = templateDto.description,
                    type = templateDto.type.name,
                    appCode = templateDto.appCode,
                    crdCode = templateDto.crdCode,
                    isSocial = templateDto.isSocial,
                    validTimeType = templateDto.validTimeType.name,
                    validTimeStart = templateDto.validTimeStart?.let { Timestamp.valueOf(it) },
                    validTimeDays = templateDto.validTimeDays,
                    validTimeEnd = templateDto.validTimeEnd?.let { Timestamp.valueOf(it) },
                    activeFrom = Timestamp.valueOf(templateDto.activeFrom),
                    activeTill = Timestamp.valueOf(templateDto.activeTill),
                    version = (existingTemplate.version ?: 0) + 1,
                    versionCreatedAt = Timestamp.valueOf(LocalDateTime.now()),
                    versionCreatedBy = UUID.randomUUID()
                )
                
                subscriptionTemplateRepository.save(updatedTemplate)
            }
            .switchIfEmpty(Mono.error(RuntimeException("Шаблон с id $id не найден")))
    }

    /**
     * Получить шаблон по ID
     */
    fun getTemplateById(id: UUID): Mono<SubscriptionTemplate> {
        return subscriptionTemplateRepository.findById(id)
            .switchIfEmpty(Mono.error(RuntimeException("Шаблон с id $id не найден")))
    }

    /**
     * Получить все шаблоны
     */
    fun getAllTemplates(): Flux<SubscriptionTemplate> {
        return subscriptionTemplateRepository.findAll()
    }

    /**
     * Получить шаблоны по типу
     */
    fun getTemplatesByType(type: SubscriptionTemplateType): Flux<SubscriptionTemplate> {
        return subscriptionTemplateRepository.findByType(type.name)
    }

    /**
     * Получить шаблоны по социальному статусу
     */
    fun getTemplatesBySocialStatus(isSocial: Boolean): Flux<SubscriptionTemplate> {
        return subscriptionTemplateRepository.findByIsSocial(isSocial)
    }

    /**
     * Получить активные шаблоны
     */
    fun getActiveTemplates(): Flux<SubscriptionTemplate> {
        return subscriptionTemplateRepository.findActiveTemplates()
    }

    /**
     * Удалить шаблон
     */
    fun deleteTemplate(id: UUID): Mono<Boolean> {
        return subscriptionTemplateRepository.findById(id)
            .flatMap { template ->
                subscriptionTemplateRepository.delete(template)
                    .then(Mono.just(true))
            }
            .switchIfEmpty(Mono.just(false))
    }
} 