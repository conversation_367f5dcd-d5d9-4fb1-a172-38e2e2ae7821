package ru.sbertroika.abt.controller.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.tkp3.abt.model.SubscriptionBaseCounter
import java.util.UUID

@Repository
interface SubscriptionCounterRepository : ReactiveCrudRepository<SubscriptionBaseCounter, UUID> {

    @Query("SELECT * FROM subscription_base_counter WHERE sbc_subscription_id = :subscriptionId ORDER BY sbc_created_at DESC")
    fun findBySubscriptionId(subscriptionId: UUID): Flux<SubscriptionBaseCounter>

    @Query("SELECT * FROM subscription_base_counter WHERE sbc_project_id = :projectId ORDER BY sbc_created_at DESC")
    fun findByProjectId(projectId: UUID): Flux<SubscriptionBaseCounter>
} 