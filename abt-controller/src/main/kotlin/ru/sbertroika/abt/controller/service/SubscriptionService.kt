package ru.sbertroika.abt.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.repository.SubscriptionRepository
import ru.sbertroika.tkp3.abt.model.Subscription
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateType
import java.time.ZonedDateTime
import java.util.UUID

@Service
class SubscriptionService(
    private val subscriptionRepository: SubscriptionRepository
) {

    /**
     * Получить все абонементы с фильтрацией и пагинацией
     */
    fun getAllSubscriptions(
        type: SubscriptionTemplateType?,
        excludeType: SubscriptionTemplateType?,
        isSocial: Boolean?,
        projectId: UUID?,
        crdId: UUID?,
        page: Int,
        size: Int
    ): Flux<Subscription> {
        val offset = page * size
        return subscriptionRepository.findAllWithFilters(
            type?.name,
            excludeType?.name,
            isSocial,
            projectId,
            crdId,
            size,
            offset
        )
    }

    /**
     * Получить общее количество абонементов с фильтрацией
     */
    fun getSubscriptionsCount(
        type: SubscriptionTemplateType?,
        excludeType: SubscriptionTemplateType?,
        isSocial: Boolean?,
        projectId: UUID?,
        crdId: UUID?
    ): Mono<Long> {
        return subscriptionRepository.countWithFilters(
            type?.name,
            excludeType?.name,
            isSocial,
            projectId,
            crdId
        ).next()
    }

    /**
     * Получить абонемент по ID
     */
    fun getSubscriptionById(id: UUID): Mono<Subscription> {
        return subscriptionRepository.findById(id)
            .switchIfEmpty(Mono.error(RuntimeException("Абонемент с id $id не найден")))
    }

    /**
     * Получить абонементы по карте
     */
    fun getSubscriptionsByCard(crdId: UUID): Flux<Subscription> {
        return subscriptionRepository.findByCrdId(crdId)
    }

    /**
     * Получить абонементы по типу
     */
    fun getSubscriptionsByType(type: SubscriptionTemplateType): Flux<Subscription> {
        return subscriptionRepository.findByType(type.name)
    }

    /**
     * Получить абонементы по социальному статусу
     */
    fun getSubscriptionsBySocialStatus(isSocial: Boolean): Flux<Subscription> {
        return subscriptionRepository.findByIsSocial(isSocial)
    }

    /**
     * Получить активные абонементы
     */
    fun getActiveSubscriptions(): Flux<Subscription> {
        val now = ZonedDateTime.now()
        return subscriptionRepository.findActiveSubscriptions(now)
    }

    /**
     * Получить абонементы по проекту
     */
    fun getSubscriptionsByProject(projectId: UUID): Flux<Subscription> {
        return subscriptionRepository.findByProjectId(projectId)
    }
} 