package ru.sbertroika.abt.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.output.model.ApiResponse
import ru.sbertroika.abt.controller.service.SubscriptionService
import ru.sbertroika.tkp3.abt.model.Subscription
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateType
import java.util.*

@RestController
@RequestMapping("/api/v1/abt/subscriptions")
class SubscriptionController(
    private val subscriptionService: SubscriptionService
) {

    @GetMapping
    fun getAllSubscriptions(
        @RequestParam(required = false) type: String?,
        @RequestParam(required = false) excludeType: String?,
        @RequestParam(required = false) isSocial: Boolean?,
        @RequestParam(required = false) projectId: UUID?,
        @RequestParam(required = false) crdId: UUID?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        val subscriptionType = type?.let { SubscriptionTemplateType.valueOf(it.uppercase()) }
        val excludeSubscriptionType = excludeType?.let { SubscriptionTemplateType.valueOf(it.uppercase()) }
        
        return Mono.zip(
            subscriptionService.getAllSubscriptions(subscriptionType, excludeSubscriptionType, isSocial, projectId, crdId, page, size).collectList(),
            subscriptionService.getSubscriptionsCount(subscriptionType, excludeSubscriptionType, isSocial, projectId, crdId)
        ).map { result ->
            val subscriptions = result.t1
            val totalCount = result.t2
            val totalPages = (totalCount + size - 1) / size
            val response = mapOf(
                "content" to subscriptions,
                "pagination" to mapOf(
                    "page" to page,
                    "size" to size,
                    "totalElements" to totalCount,
                    "totalPages" to totalPages,
                    "hasNext" to (page < totalPages - 1),
                    "hasPrevious" to (page > 0)
                )
            )
            ApiResponse.success(response)
        }.onErrorResume { error ->
            Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения абонементов: ${error.message}"))
        }
    }

    @GetMapping("/{id}")
    fun getSubscriptionById(@PathVariable id: UUID): Mono<ApiResponse<Subscription>> {
        return subscriptionService.getSubscriptionById(id)
            .map { subscription ->
                ApiResponse.success(subscription)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Subscription>("Ошибка получения абонемента: ${error.message}"))
            }
    }

    @GetMapping("/by-card/{crdId}")
    fun getSubscriptionsByCard(@PathVariable crdId: UUID): Mono<ApiResponse<List<Subscription>>> {
        return subscriptionService.getSubscriptionsByCard(crdId)
            .collectList()
            .map { subscriptions ->
                ApiResponse.success(subscriptions)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Subscription>>("Ошибка получения абонементов по карте: ${error.message}"))
            }
    }

    @GetMapping("/type/{type}")
    fun getSubscriptionsByType(@PathVariable type: String): Mono<ApiResponse<List<Subscription>>> {
        val subscriptionType = SubscriptionTemplateType.valueOf(type.uppercase())
        return subscriptionService.getSubscriptionsByType(subscriptionType)
            .collectList()
            .map { subscriptions ->
                ApiResponse.success(subscriptions)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Subscription>>("Ошибка получения абонементов по типу: ${error.message}"))
            }
    }

    @GetMapping("/social/{isSocial}")
    fun getSubscriptionsBySocialStatus(@PathVariable isSocial: Boolean): Mono<ApiResponse<List<Subscription>>> {
        return subscriptionService.getSubscriptionsBySocialStatus(isSocial)
            .collectList()
            .map { subscriptions ->
                ApiResponse.success(subscriptions)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Subscription>>("Ошибка получения абонементов по социальному статусу: ${error.message}"))
            }
    }

    @GetMapping("/active")
    fun getActiveSubscriptions(): Mono<ApiResponse<List<Subscription>>> {
        return subscriptionService.getActiveSubscriptions()
            .collectList()
            .map { subscriptions ->
                ApiResponse.success(subscriptions)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Subscription>>("Ошибка получения активных абонементов: ${error.message}"))
            }
    }

    @GetMapping("/project/{projectId}")
    fun getSubscriptionsByProject(@PathVariable projectId: UUID): Mono<ApiResponse<List<Subscription>>> {
        return subscriptionService.getSubscriptionsByProject(projectId)
            .collectList()
            .map { subscriptions ->
                ApiResponse.success(subscriptions)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Subscription>>("Ошибка получения абонементов по проекту: ${error.message}"))
            }
    }
} 