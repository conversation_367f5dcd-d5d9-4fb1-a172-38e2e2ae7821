package ru.sbertroika.abt.controller.input.model

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateType
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateValidTimeType
import java.time.LocalDateTime

data class SubscriptionTemplateDto(
    @field:NotBlank(message = "Название шаблона обязательно")
    val stName: String,
    
    val description: String? = null,
    
    @field:NotNull(message = "Тип абонемента обязателен")
    val type: SubscriptionTemplateType,
    
    @field:PositiveOrZero(message = "Код приложения должен быть положительным")
    val appCode: Int,
    
    @field:PositiveOrZero(message = "Код карты должен быть положительным")
    val crdCode: Int,
    
    val isSocial: Boolean = false,
    
    @field:NotNull(message = "Тип срока действия обязателен")
    val validTimeType: SubscriptionTemplateValidTimeType,
    
    val validTimeStart: LocalDateTime? = null,
    
    @field:Positive(message = "Количество дней должно быть положительным")
    val validTimeDays: Int? = null,
    
    val validTimeEnd: LocalDateTime? = null,
    
    @field:NotNull(message = "Дата начала активности обязательна")
    val activeFrom: LocalDateTime,
    
    @field:NotNull(message = "Дата окончания активности обязательна")
    val activeTill: LocalDateTime,
    
    val status: String = "active"
) 