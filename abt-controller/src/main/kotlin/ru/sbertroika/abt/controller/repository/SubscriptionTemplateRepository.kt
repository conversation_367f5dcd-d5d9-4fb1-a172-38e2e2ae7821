package ru.sbertroika.abt.controller.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplate
import java.util.UUID

@Repository
interface SubscriptionTemplateRepository : ReactiveCrudRepository<SubscriptionTemplate, UUID> {

    @Query("SELECT * FROM subscription_template WHERE st_type = :type")
    fun findByType(type: String): Flux<SubscriptionTemplate>

    @Query("SELECT * FROM subscription_template WHERE st_is_social = :isSocial")
    fun findByIsSocial(isSocial: Boolean): Flux<SubscriptionTemplate>

    @Query("SELECT * FROM subscription_template WHERE st_active_from <= NOW() AND st_active_till >= NOW()")
    fun findActiveTemplates(): Flux<SubscriptionTemplate>

    @Query("SELECT * FROM subscription_template ORDER BY st_name")
    override fun findAll(): Flux<SubscriptionTemplate>

    @Query("""
        INSERT INTO subscription_template (
            st_id, st_version, st_version_created_at, st_version_created_by, st_project_id,
            st_active_from, st_active_till, st_name, st_description, st_type,
            st_app_code, st_crd_code, st_is_social, st_valid_time_type,
            st_valid_time_start, st_valid_time_days, st_valid_time_end
        ) VALUES (
            :id, :version, :versionCreatedAt, :versionCreatedBy, :projectId,
            :activeFrom, :activeTill, :stName, :description, :type,
            :appCode, :crdCode, :isSocial, :validTimeType,
            :validTimeStart, :validTimeDays, :validTimeEnd
        )
    """)
    fun insertTemplate(
        id: UUID,
        version: Int,
        versionCreatedAt: java.sql.Timestamp,
        versionCreatedBy: UUID?,
        projectId: UUID?,
        activeFrom: java.sql.Timestamp,
        activeTill: java.sql.Timestamp?,
        stName: String,
        description: String?,
        type: String,
        appCode: Int,
        crdCode: Int,
        isSocial: Boolean,
        validTimeType: String,
        validTimeStart: java.sql.Timestamp?,
        validTimeDays: Int?,
        validTimeEnd: java.sql.Timestamp?
    ): Mono<Void>
} 