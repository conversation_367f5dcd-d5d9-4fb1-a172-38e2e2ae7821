package ru.sbertroika.abt.controller.input

import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.input.model.SubscriptionTemplateRuleDto
import ru.sbertroika.abt.controller.output.model.ApiResponse
import ru.sbertroika.abt.controller.service.SubscriptionTemplateRuleService
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateRule
import java.util.*

@RestController
@RequestMapping("/api/v1/abt/subscription-template-rules")
class SubscriptionTemplateRuleController(
    private val ruleService: SubscriptionTemplateRuleService
) {

    @GetMapping("/template/{templateId}")
    fun getRulesByTemplateId(@PathVariable templateId: UUID): Mono<ApiResponse<List<SubscriptionTemplateRule>>> {
        return ruleService.getRulesByTemplateId(templateId)
            .collectList()
            .map { rules ->
                ApiResponse.success(rules)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplateRule>>("Ошибка получения правил: ${error.message}"))
            }
    }

    @PostMapping
    fun createRule(@Valid @RequestBody ruleDto: SubscriptionTemplateRuleDto): Mono<ApiResponse<SubscriptionTemplateRule>> {
        return ruleService.createRule(ruleDto)
            .map { rule ->
                ApiResponse.success(rule, "Правило успешно создано")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplateRule>("Ошибка создания правила: ${error.message}"))
            }
    }

    @PutMapping("/{id}")
    fun updateRule(
        @PathVariable id: UUID,
        @Valid @RequestBody ruleDto: SubscriptionTemplateRuleDto
    ): Mono<ApiResponse<SubscriptionTemplateRule>> {
        return ruleService.updateRule(id, ruleDto)
            .map { rule ->
                ApiResponse.success(rule, "Правило успешно обновлено")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<SubscriptionTemplateRule>("Ошибка обновления правила: ${error.message}"))
            }
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteRule(@PathVariable id: UUID): Mono<ApiResponse<Boolean>> {
        return ruleService.deleteRule(id)
            .map { success ->
                if (success) {
                    ApiResponse.success(true, "Правило успешно удалено")
                } else {
                    ApiResponse.error<Boolean>("Правило не найдено")
                }
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Boolean>("Ошибка удаления правила: ${error.message}"))
            }
    }

    @DeleteMapping("/template/{templateId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteRulesByTemplateId(@PathVariable templateId: UUID): Mono<ApiResponse<Unit>> {
        return ruleService.deleteRulesByTemplateId(templateId)
            .then(Mono.just(ApiResponse.success(Unit, "Все правила шаблона удалены")))
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Unit>("Ошибка удаления правил: ${error.message}"))
            }
    }

    @PostMapping("/template/{templateId}/batch")
    fun createRulesForTemplate(
        @PathVariable templateId: UUID,
        @Valid @RequestBody rules: List<SubscriptionTemplateRuleDto>
    ): Mono<ApiResponse<List<SubscriptionTemplateRule>>> {
        return ruleService.createRulesForTemplate(templateId, rules)
            .collectList()
            .map { createdRules ->
                ApiResponse.success(createdRules, "Правила успешно созданы")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<SubscriptionTemplateRule>>("Ошибка создания правил: ${error.message}"))
            }
    }
} 