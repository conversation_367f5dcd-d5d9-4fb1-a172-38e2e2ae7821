package ru.sbertroika.abt.controller.repository

import org.springframework.data.domain.Pageable
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.data.repository.reactive.ReactiveSortingRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import ru.sbertroika.tkp3.abt.model.Subscription
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateType
import java.time.ZonedDateTime
import java.util.UUID

@Repository
interface SubscriptionRepository : ReactiveCrudRepository<Subscription, UUID>, ReactiveSortingRepository<Subscription, UUID> {

    @Query("""
        SELECT s.* FROM subscription s
        WHERE (:type IS NULL OR EXISTS (
            SELECT 1 FROM subscription_template st 
            WHERE st.st_id = s.sub_tt_id 
            AND st.st_type = :type
        ))
        AND (:excludeType IS NULL OR NOT EXISTS (
            SELECT 1 FROM subscription_template st 
            WHERE st.st_id = s.sub_tt_id 
            AND st.st_type = :excludeType
        ))
        AND (:isSocial IS NULL OR s.sub_is_social = :isSocial)
        AND (:projectId IS NULL OR s.sub_project_id = :projectId)
        AND (:crdId IS NULL OR s.sub_crd_id = :crdId)
        ORDER BY s.sub_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findAllWithFilters(
        type: String?,
        excludeType: String?,
        isSocial: Boolean?,
        projectId: UUID?,
        crdId: UUID?,
        limit: Int,
        offset: Int
    ): Flux<Subscription>

    @Query("""
        SELECT COUNT(*) FROM subscription s
        WHERE (:type IS NULL OR EXISTS (
            SELECT 1 FROM subscription_template st 
            WHERE st.st_id = s.sub_tt_id 
            AND st.st_type = :type
        ))
        AND (:excludeType IS NULL OR NOT EXISTS (
            SELECT 1 FROM subscription_template st 
            WHERE st.st_id = s.sub_tt_id 
            AND st.st_type = :excludeType
        ))
        AND (:isSocial IS NULL OR s.sub_is_social = :isSocial)
        AND (:projectId IS NULL OR s.sub_project_id = :projectId)
        AND (:crdId IS NULL OR s.sub_crd_id = :crdId)
    """)
    fun countWithFilters(
        type: String?,
        excludeType: String?,
        isSocial: Boolean?,
        projectId: UUID?,
        crdId: UUID?
    ): Flux<Long>

    @Query("SELECT * FROM subscription WHERE sub_crd_id = :crdId ORDER BY sub_created_at DESC")
    fun findByCrdId(crdId: UUID): Flux<Subscription>

    @Query("""
        SELECT s.* FROM subscription s
        WHERE EXISTS (
            SELECT 1 FROM subscription_template st 
            WHERE st.st_id = s.sub_tt_id 
            AND st.st_type = :type
        )
        ORDER BY s.sub_created_at DESC
    """)
    fun findByType(type: String): Flux<Subscription>

    @Query("SELECT * FROM subscription WHERE sub_is_social = :isSocial ORDER BY sub_created_at DESC")
    fun findByIsSocial(isSocial: Boolean): Flux<Subscription>

    @Query("""
        SELECT * FROM subscription
        WHERE sub_active_from <= :now AND sub_active_till >= :now
        ORDER BY sub_created_at DESC
    """)
    fun findActiveSubscriptions(now: ZonedDateTime): Flux<Subscription>

    @Query("SELECT * FROM subscription WHERE sub_project_id = :projectId ORDER BY sub_created_at DESC")
    fun findByProjectId(projectId: UUID): Flux<Subscription>
} 