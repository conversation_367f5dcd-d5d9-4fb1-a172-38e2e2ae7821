package ru.sbertroika.abt.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.abt.controller.input.model.SubscriptionTemplateRuleDto
import ru.sbertroika.abt.controller.repository.SubscriptionTemplateRuleRepository
import ru.sbertroika.tkp3.abt.model.SubscriptionTemplateRule
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.UUID

@Service
class SubscriptionTemplateRuleService(
    private val ruleRepository: SubscriptionTemplateRuleRepository
) {

    /**
     * Получить правила шаблона по ID шаблона
     */
    fun getRulesByTemplateId(templateId: UUID): Flux<SubscriptionTemplateRule> {
        return ruleRepository.findBySubscriptionTemplateId(templateId)
    }

    /**
     * Создать правило для шаблона
     */
    fun createRule(ruleDto: SubscriptionTemplateRuleDto): Mono<SubscriptionTemplateRule> {
        val id = UUID.randomUUID()
        val versionCreatedAt = Timestamp.valueOf(LocalDateTime.now())
        
        return ruleRepository.insertRule(
            id = id,
            version = 1,
            versionCreatedAt = versionCreatedAt,
            versionCreatedBy = null,
            projectId = UUID.fromString("43db9de7-72ec-4eae-8978-8aef1c46873a"),
            subscriptionTemplateId = ruleDto.subscriptionTemplateId,
            passIndex = ruleDto.passIndex,
            passAction = ruleDto.passAction
        ).then(
            ruleRepository.findById(id)
        ).switchIfEmpty(
            Mono.error<SubscriptionTemplateRule>(RuntimeException("Не удалось создать правило"))
        )
    }

    /**
     * Обновить правило
     */
    fun updateRule(id: UUID, ruleDto: SubscriptionTemplateRuleDto): Mono<SubscriptionTemplateRule> {
        return ruleRepository.findById(id)
            .flatMap { existingRule ->
                val updatedRule = existingRule.copy(
                    passIndex = ruleDto.passIndex,
                    passAction = ruleDto.passAction,
                    version = (existingRule.version ?: 0) + 1,
                    versionCreatedAt = Timestamp.valueOf(LocalDateTime.now()),
                    versionCreatedBy = null
                )
                
                ruleRepository.save(updatedRule)
            }
            .switchIfEmpty(Mono.error(RuntimeException("Правило с id $id не найдено")))
    }

    /**
     * Удалить правило
     */
    fun deleteRule(id: UUID): Mono<Boolean> {
        return ruleRepository.findById(id)
            .flatMap { rule ->
                ruleRepository.delete(rule)
                    .then(Mono.just(true))
            }
            .switchIfEmpty(Mono.just(false))
    }

    /**
     * Удалить все правила шаблона
     */
    fun deleteRulesByTemplateId(templateId: UUID): Mono<Void> {
        return ruleRepository.deleteBySubscriptionTemplateId(templateId)
    }

    /**
     * Создать несколько правил для шаблона
     */
    fun createRulesForTemplate(templateId: UUID, rules: List<SubscriptionTemplateRuleDto>): Flux<SubscriptionTemplateRule> {
        return Flux.fromIterable(rules)
            .flatMap { ruleDto ->
                val ruleWithTemplateId = ruleDto.copy(subscriptionTemplateId = templateId)
                createRule(ruleWithTemplateId)
            }
    }
} 