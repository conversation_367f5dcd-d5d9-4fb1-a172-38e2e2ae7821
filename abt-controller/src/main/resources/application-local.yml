server:
  port: 8086

spring:
  application:
    name: abt-controller
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # R2DBC конфигурация для local профиля
  r2dbc:
    url: r2dbc:${DB_URL:postgresql://localhost:5432/abt}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
  
  flyway:
    enabled: false

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    ru.sbertroika.abt.controller: INFO
    org.springframework.web: INFO
    reactor.netty: WARN
    org.springframework.data.r2dbc: DEBUG
    org.flywaydb: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Конфигурация для контроллера
controller:
  pagination:
    default-page-size: 20
    max-page-size: 100 