### ABT Controller API Examples

# Базовый URL
@baseUrl = http://localhost:8086

### Получить все шаблоны абонементов
GET {{baseUrl}}/api/v1/abt/subscription-templates
Content-Type: application/json

### Получить активные шаблоны
GET {{baseUrl}}/api/v1/abt/subscription-templates/active
Content-Type: application/json

### Получить шаблоны по типу (TRAVEL)
GET {{baseUrl}}/api/v1/abt/subscription-templates/type/TRAVEL
Content-Type: application/json

### Получить шаблоны по типу (WALLET)
GET {{baseUrl}}/api/v1/abt/subscription-templates/type/WALLET
Content-Type: application/json

### Получить социальные шаблоны
GET {{baseUrl}}/api/v1/abt/subscription-templates/social/true
Content-Type: application/json

### Получить обычные шаблоны
GET {{baseUrl}}/api/v1/abt/subscription-templates/social/false
Content-Type: application/json

### Получить шаблон по ID
GET {{baseUrl}}/api/v1/abt/subscription-templates/377f61d2-81f0-4d4d-be74-58b8d38100b7
Content-Type: application/json

### Создать новый шаблон абонемента
POST {{baseUrl}}/api/v1/abt/subscription-templates
Content-Type: application/json

{
  "stName": "Тестовый абонемент",
  "description": "Тестовый абонемент для проверки API",
  "type": "TRAVEL",
  "appCode": 9999,
  "crdCode": 9999,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "validTimeStart": "2024-01-01T00:00:00",
  "validTimeDays": 30,
  "validTimeEnd": null,
  "activeFrom": "2024-01-01T00:00:00",
  "activeTill": "2024-12-31T23:59:59"
}

### Обновить шаблон абонемента
PUT {{baseUrl}}/api/v1/abt/subscription-templates/377f61d2-81f0-4d4d-be74-58b8d38100b7
Content-Type: application/json

{
  "stName": "Обновленный стандартный абонемент",
  "description": "Обновленное описание абонемента",
  "type": "TRAVEL",
  "appCode": 1001,
  "crdCode": 2001,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "validTimeStart": "2024-01-01T00:00:00",
  "validTimeDays": 30,
  "validTimeEnd": null,
  "activeFrom": "2024-01-01T00:00:00",
  "activeTill": "2024-12-31T23:59:59"
}

### Удалить шаблон абонемента
DELETE {{baseUrl}}/api/v1/abt/subscription-templates/377f61d2-81f0-4d4d-be74-58b8d38100b7

### Health Check
GET {{baseUrl}}/actuator/health

### API Documentation
GET {{baseUrl}}/api-docs 

### Получить все абонементы
GET http://localhost:8080/api/v1/abt/subscriptions

### Получить абонементы с фильтрацией по типу (кошельки)
GET http://localhost:8080/api/v1/abt/subscriptions?type=WALLET

### Получить абонементы с фильтрацией по типу (поездочные)
GET http://localhost:8080/api/v1/abt/subscriptions?type=TRAVEL

### Получить абонементы с фильтрацией по типу (безлимитные)
GET http://localhost:8080/api/v1/abt/subscriptions?type=UNLIMITED

### Получить социальные абонементы
GET http://localhost:8080/api/v1/abt/subscriptions?isSocial=true

### Получить обычные абонементы
GET http://localhost:8080/api/v1/abt/subscriptions?isSocial=false

### Получить абонементы по проекту
GET http://localhost:8080/api/v1/abt/subscriptions?projectId=43db9de7-72ec-4eae-8978-8aef1c46873a

### Получить абонементы по карте
GET http://localhost:8080/api/v1/abt/subscriptions?crdId=123e4567-e89b-12d3-a456-426614174000

### Получить абонементы с пагинацией
GET http://localhost:8080/api/v1/abt/subscriptions?page=0&size=10

### Получить абонемент по ID
GET http://localhost:8080/api/v1/abt/subscriptions/123e4567-e89b-12d3-a456-426614174000

### Получить абонементы по карте
GET http://localhost:8080/api/v1/abt/subscriptions/by-card/123e4567-e89b-12d3-a456-426614174000

### Получить абонементы по типу
GET http://localhost:8080/api/v1/abt/subscriptions/type/WALLET

### Получить абонементы по социальному статусу
GET http://localhost:8080/api/v1/abt/subscriptions/social/true

### Получить активные абонементы
GET http://localhost:8080/api/v1/abt/subscriptions/active

### Получить абонементы по проекту
GET http://localhost:8080/api/v1/abt/subscriptions/project/43db9de7-72ec-4eae-8978-8aef1c46873a

### Получить счетчики абонемента
GET http://localhost:8080/api/v1/abt/subscription-counters/subscription/123e4567-e89b-12d3-a456-426614174000

### Получить счетчик по ID
GET http://localhost:8080/api/v1/abt/subscription-counters/123e4567-e89b-12d3-a456-426614174000

### Получить счетчики по проекту
GET http://localhost:8080/api/v1/abt/subscription-counters/project/43db9de7-72ec-4eae-8978-8aef1c46873a

### Шаблоны абонементов

### Получить все шаблоны
GET http://localhost:8080/api/v1/abt/subscription-templates

### Получить шаблон по ID
GET http://localhost:8080/api/v1/abt/subscription-templates/123e4567-e89b-12d3-a456-426614174000

### Получить шаблоны по типу
GET http://localhost:8080/api/v1/abt/subscription-templates/type/WALLET

### Получить шаблоны по социальному статусу
GET http://localhost:8080/api/v1/abt/subscription-templates/social/true

### Получить активные шаблоны
GET http://localhost:8080/api/v1/abt/subscription-templates/active

### Создать новый шаблон
POST http://localhost:8080/api/v1/abt/subscription-templates
Content-Type: application/json

{
  "stName": "Тестовый кошелек",
  "description": "Тестовый шаблон кошелька",
  "type": "WALLET",
  "appCode": 1001,
  "crdCode": 2001,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "activeFrom": "2024-01-01T00:00:00",
  "activeTill": "2024-12-31T23:59:59"
}

### Обновить шаблон
PUT http://localhost:8080/api/v1/abt/subscription-templates/123e4567-e89b-12d3-a456-426614174000
Content-Type: application/json

{
  "stName": "Обновленный кошелек",
  "description": "Обновленный шаблон кошелька",
  "type": "WALLET",
  "appCode": 1001,
  "crdCode": 2001,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "activeFrom": "2024-01-01T00:00:00",
  "activeTill": "2024-12-31T23:59:59"
}

### Удалить шаблон
DELETE http://localhost:8080/api/v1/abt/subscription-templates/123e4567-e89b-12d3-a456-426614174000 