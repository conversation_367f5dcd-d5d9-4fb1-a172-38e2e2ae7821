# ABT Controller

Контроллер для управления абонементами и кошельками в системе ABT.

## API Endpoints

### Абонементы

#### Получить все абонементы
```
GET /api/v1/subscriptions
```

**Параметры запроса:**
- `type` (опционально) - Тип абонемента (WALLET, TRAVEL, UNLIMITED)
- `isSocial` (опционально) - Социальный статус (true/false)
- `projectId` (опционально) - ID проекта
- `crdId` (опционально) - ID карты
- `page` (опционально) - Но<PERSON><PERSON>р страницы (по умолчанию 0)
- `size` (опционально) - Раз<PERSON>ер страницы (по умолчанию 20)

**Примеры:**
```bash
# Получить все абонементы
GET /api/v1/subscriptions

# Получить только кошельки
GET /api/v1/subscriptions?type=WALLET

# Получить социальные абонементы
GET /api/v1/subscriptions?isSocial=true

# Получить абонементы с пагинацией
GET /api/v1/subscriptions?page=0&size=10
```

#### Получить абонемент по ID
```
GET /api/v1/subscriptions/{id}
```

#### Получить абонементы по карте
```
GET /api/v1/subscriptions/by-card/{crdId}
```

#### Получить абонементы по типу
```
GET /api/v1/subscriptions/type/{type}
```

#### Получить абонементы по социальному статусу
```
GET /api/v1/subscriptions/social/{isSocial}
```

#### Получить активные абонементы
```
GET /api/v1/subscriptions/active
```

#### Получить абонементы по проекту
```
GET /api/v1/subscriptions/project/{projectId}
```

### Счетчики абонементов

#### Получить счетчики абонемента
```
GET /api/v1/subscription-counters/subscription/{subscriptionId}
```

#### Получить счетчик по ID
```
GET /api/v1/subscription-counters/{id}
```

#### Получить счетчики по проекту
```
GET /api/v1/subscription-counters/project/{projectId}
```

### Шаблоны абонементов

#### Получить все шаблоны
```
GET /api/v1/subscription-templates
```

#### Получить шаблон по ID
```
GET /api/v1/subscription-templates/{id}
```

#### Получить шаблоны по типу
```
GET /api/v1/subscription-templates/type/{type}
```

#### Получить шаблоны по социальному статусу
```
GET /api/v1/subscription-templates/social/{isSocial}
```

#### Получить активные шаблоны
```
GET /api/v1/subscription-templates/active
```

#### Создать шаблон
```
POST /api/v1/subscription-templates
Content-Type: application/json

{
  "stName": "Название шаблона",
  "description": "Описание шаблона",
  "type": "WALLET",
  "appCode": 1001,
  "crdCode": 2001,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "activeFrom": "2024-01-01T00:00:00",
  "activeTill": "2024-12-31T23:59:59"
}
```

#### Обновить шаблон
```
PUT /api/v1/subscription-templates/{id}
Content-Type: application/json

{
  "stName": "Обновленное название",
  "description": "Обновленное описание",
  "type": "WALLET",
  "appCode": 1001,
  "crdCode": 2001,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "activeFrom": "2024-01-01T00:00:00",
  "activeTill": "2024-12-31T23:59:59"
}
```

#### Удалить шаблон
```
DELETE /api/v1/subscription-templates/{id}
```

## Типы абонементов

- `WALLET` - Кошелек
- `TRAVEL` - Поездочный абонемент
- `UNLIMITED` - Безлимитный абонемент

## Структура данных

### Абонемент (Subscription)
```json
{
  "id": "uuid",
  "createdAt": "2024-01-01T00:00:00Z",
  "crdId": "uuid",
  "abonementId": 1000000000000000001,
  "projectId": "uuid",
  "activeFrom": "2024-01-01T00:00:00Z",
  "activeTill": "2024-01-31T23:59:59Z",
  "name": "Название абонемента",
  "description": "Описание абонемента",
  "isSocial": false,
  "ttId": "uuid",
  "ttVersion": 1,
  "tags": "теги",
  "emissionSource": "источник эмиссии"
}
```

### Счетчик абонемента (SubscriptionBaseCounter)
```json
{
  "id": "uuid",
  "createdAt": "2024-01-01T00:00:00Z",
  "subscriptionId": "uuid",
  "counterType": "ALL",
  "counterValue": 100,
  "isBus": true,
  "isTrolleybus": true,
  "isTram": false,
  "isMetro": true,
  "projectId": "uuid"
}
```

### Шаблон абонемента (SubscriptionTemplate)
```json
{
  "id": "uuid",
  "version": 1,
  "versionCreatedAt": "2024-01-01T00:00:00Z",
  "versionCreatedBy": "uuid",
  "projectId": "uuid",
  "activeFrom": "2024-01-01T00:00:00Z",
  "activeTill": "2024-12-31T23:59:59Z",
  "stName": "Название шаблона",
  "description": "Описание шаблона",
  "type": "WALLET",
  "appCode": 1001,
  "crdCode": 2001,
  "isSocial": false,
  "validTimeType": "INTERVAL",
  "validTimeStart": "2024-01-01T00:00:00Z",
  "validTimeDays": 30,
  "validTimeEnd": "2024-12-31T23:59:59Z"
}
```

## Особенности реализации

1. **Фильтрация по типу**: Абонементы фильтруются по типу через связь с шаблоном абонемента
2. **Счетчики**: У каждого абонемента может быть несколько счетчиков для разных типов транспорта
3. **Версионирование**: Шаблоны поддерживают версионирование
4. **Социальные абонементы**: Поддерживается флаг социального статуса
5. **Активность**: Абонементы имеют период действия

## Запуск

```bash
./gradlew bootRun
```

## Тестирование

Для тестирования API используйте файл `api-examples.http` с примерами запросов. 